{{ 'section-events-calendar.css' | asset_url | stylesheet_tag }}
{{ 'section-banner-two-columns.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

{%- if section.settings.banner_height == 'adapt_image' and section.blocks.first.settings.image != blank -%}
  {%- style -%}
    @media screen and (max-width: 749px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .media::before,
      #Banner-{{ section.id }}:not(.banner--mobile-bottom) .banner-content::before {
        padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }

    @media screen and (min-width: 750px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .media::before {
        padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {%- endstyle -%}
{%- endif -%}

{%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }

    @media screen and (max-width: 990px) {
      .margin-spacing-negative.section-{{ section.id }}-margin {
        margin-top: -{{ section.settings.margin_top }}px;
      }
      .margin-spacing-positive.section-{{ section.id }}-margin {
        margin-top: {{ section.settings.margin_top }}px;
      }
    }

  .sticky-banners .banner-two-columns-box {
    max-width: 60rem;
  }
  
  .sticky-images-with-text .slider-component {
    position: relative;
    overflow: hidden;
    margin-top: 0; /* Ensure there's no extra margin at the top */
  }

  .sticky-images-with-text .banner {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: translateX(-100%); /* Adjust the initial position */
    transition: transform .8s ease-out; /* Longer duration for slower effect */
    will-change: transform;
  }

  .sticky-images-with-text .banner.in-view {
    transform: translateX(0%); /* Adjust the final position */
  }

  .sticky-images-with-text .banner-two-columns .banner-media {
    border-radius: var(--media-radius);
  }
  
  .sticky-images-with-text .banner-section--full-width.banner-two-columns .banner-media {
    border-radius: 0;
  }

  .events-calendar .events-calendar__heading {
    margin: 0 0 2rem 0;
  }

  .events-calendar .events-calendar__caption {
      margin: 0;
  }
{%- endstyle -%}

<script>
document.addEventListener('DOMContentLoaded', function() {
  const offset = 120;

  function handleScroll() {
    window.requestAnimationFrame(() => {
      document.querySelectorAll('.banner').forEach(element => {
        const rect = element.getBoundingClientRect();
        const isVisible = (
          rect.top < (window.innerHeight - offset) &&
          rect.bottom > 0
        );

        if (isVisible) {
          element.classList.add('in-view');
        } else {
          element.classList.remove('in-view');
        }
      });
    });
  }

  window.addEventListener('scroll', handleScroll);
  handleScroll();

  document.addEventListener('shopify:section:select', function() {
    handleScroll(); // Re-run the scroll logic when a section is selected
  });
});

</script>

<div class="events-calendar sticky-banners ignore-{{ section.settings.ignore_spacing }}">
  <div
    class="sticky-images-with-text color-{{ section.settings.color_scheme }} gradient section-{{ section.id }}-padding margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin" data-aos="fade-up"
  >
    <div class="{% if section.settings.full_width %}banner-section--full-width{% endif %} page-width banner-two-columns">
      {%- if section.settings.caption != blank -%}
        <p
          class="events-calendar__caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}"
        >
          {{ section.settings.caption | escape }}
        </p>
      {%- endif -%}

      {%- if section.settings.heading != blank -%}
        <{{ section.settings.heading_tag }} class="events-calendar__heading heading-bold {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }}">
          {{ section.settings.heading | escape }}
        </{{ section.settings.heading_tag }}>
      {%- endif -%}
      <slider-component class="slider-mobile-gutter">
        {%- for block in section.blocks -%}
          <div
            id="Banner-{{ section.id }}"
            class="banner banner--{{ section.settings.banner_height }} {% if section.blocks.first.settings.image == blank %} slideshow--placeholder{% endif %}"
            {{ block.shopify_attributes }}
          >
            <style>
              #Cover-{{ section.id }}-{{ forloop.index }} .banner-media::after {
                opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};
              }
            </style>
            <div id="Cover-{{ section.id }}-{{ forloop.index }}">
              <div class="banner-media media{% if block.settings.image == blank %} placeholder{% endif %} {% if section.settings.animate_slider == true %} animate--slider{% endif %}">
                {%- if block.settings.image -%}
                  {%- assign height = block.settings.image.width
                    | divided_by: block.settings.image.aspect_ratio
                    | round
                  -%}
                  {{
                    block.settings.image
                    | image_url: width: 3840
                    | image_tag:
                      loading: 'lazy',
                      height: height,
                      sizes: '100vw',
                      widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
                  }}
                {%- else -%}
                  {%- assign placeholder_slide = forloop.index | modulo: 2 -%}
                  {%- if placeholder_slide == 1 -%}
                    {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- else -%}
                    {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- endif -%}
                {%- endif -%}
              </div>
            </div>

            {%- if block.settings.hide_date == false -%}
              <div class="event-item-date h3">
                <div class="article-date">
                <time datetime="01-31">
                  <span class="event-item-month">{{ block.settings.event_month | escape }}</span>
                  <span class="event-item-day">{{ block.settings.event_date | escape }}</span>
                </time>
                </div>
              </div>
            {%- endif -%}

            {%- if block.settings.event_price != blank -%}
              <span class="event-item-price">
                {{- block.settings.event_price }}
                {{ shop.currency -}}
              </span>
            {%- endif -%}

            <div class="banner-content {% if block.settings.show_text_box == false %} banner--desktop-transparent{% endif %}">
              <div class="global-media-settings banner-two-columns-box content-container content-container--full-width-mobile color-{{ block.settings.color_scheme_1 }} gradient">

                <div class="event-item-top-info">
                  {%- if block.settings.event_time != blank -%}
                    <span class="event-item-subheading">{{ block.settings.event_time | escape }}</span>
                  {%- endif -%}
                </div>
                
              {%- if block.settings.event_location != blank -%}
                <span class="event-item-location">{{ block.settings.event_location | escape }}</span>
              {%- endif -%}

              {%- if block.settings.event_heading != blank -%}
                <{{ block.settings.heading_tag }} class="banner-heading heading-bold {{ block.settings.heading_size }}">
                  {{- block.settings.event_heading | escape -}}
                </{{ block.settings.heading_tag }}>
              {%- endif -%}
                    
              {%- if block.settings.event_description != blank -%}
                <div class="rte">{{ block.settings.event_description }}</div>
              {%- endif -%}

              <div class="event-item-link">
                {%- if block.settings.link_label != blank -%}
                  <a
                    class="button-arrow button"
                    {% if block.settings.link == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.link }}"
                    {% endif %}
                  >
                    {{- block.settings.link_label | escape -}}
                    {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
                  </a>
                {%- endif -%}
              </div>
              </div>
            </div>
          </div>
        {%- endfor -%}
      </slider-component>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.events-calendar.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.related-products.settings.paragraph__1.content"
    },
    {
      "type": "text",
      "id": "caption",
      "default": "Caption",
      "label": "t:sections.related-products.settings.caption.label"
  },
  {
    "type": "select",
    "id": "text_style",
    "options": [
      {
        "value": "subtitle",
        "label": "t:sections.related-products.settings.text_style.options__1.label"
      },
      {
        "value": "caption-with-letter-spacing",
        "label": "t:sections.related-products.settings.text_style.options__2.label"
      }
    ],
    "default": "caption-with-letter-spacing",
    "label": "t:sections.related-products.settings.text_style.label"
  },
  {
    "type": "select",
    "id": "text_size",
    "options": [
      {
        "value": "small",
        "label": "t:sections.related-products.settings.text_size.options__1.label"
      },
      {
        "value": "medium",
        "label": "t:sections.related-products.settings.text_size.options__2.label"
      },
      {
        "value": "large",
        "label": "t:sections.related-products.settings.text_size.options__3.label"
      }
    ],
    "default": "medium",
    "label": "t:sections.related-products.settings.text_size.label"
  },
    {
      "type": "text",
      "id": "heading",
      "default": "You may also like",
      "label": "t:sections.related-products.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "select",
      "id": "banner_height",
      "options": [
        {
          "value": "adapt_image",
          "label": "t:sections.sticky-banners.settings.banner_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.sticky-banners.settings.banner_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.sticky-banners.settings.banner_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.sticky-banners.settings.banner_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.sticky-banners.settings.banner_height.label"
    },
    {
      "type": "checkbox",
      "id": "animate_slider",
      "default": false,
      "label": "t:sections.all.animate_slider.label"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.sticky-banners.settings.full_width.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.promotion-cards.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "event",
      "name": "t:sections.events-calendar.blocks.event.name",
      "limit": 5,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.events-calendar.blocks.event.settings.image.label"
        },
        {
          "type": "range",
          "id": "event_date",
          "min": 0,
          "max": 31,
          "step": 1,
          "label": "t:sections.events-calendar.blocks.event.settings.event_date.label",
          "default": 12
        },
        {
          "type": "text",
          "id": "event_month",
          "default": "April",
          "label": "t:sections.events-calendar.blocks.event.settings.event_month.label"
        },
        {
          "type": "checkbox",
          "id": "hide_date",
          "default": false,
          "label": "t:sections.events-calendar.blocks.event.settings.hide_date.label"
        },
        {
          "type": "text",
          "id": "event_time",
          "default": "Saturday, 7:30pm",
          "label": "t:sections.events-calendar.blocks.event.settings.event_time.label"
        },
        {
          "type": "text",
          "id": "event_price",
          "default": "$10",
          "label": "t:sections.events-calendar.blocks.event.settings.event_price.label"
        },
        {
          "type": "text",
          "id": "event_heading",
          "default": "Event Heading",
          "label": "t:sections.events-calendar.blocks.event.settings.event_heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "extra-large",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "large",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "medium",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "options": [
            {
              "value": "h1",
              "label": "t:sections.all.heading_tag.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_tag.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_tag.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_tag.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_tag.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_tag.options__6.label"
            }
          ],
          "default": "h2",
          "label": "t:sections.all.heading_tag.label",
          "info": "t:sections.all.heading_tag.info"
        },
        {
          "type": "richtext",
          "id": "event_description",
          "default": "<p>Add info details about your event.</p>",
          "label": "t:sections.events-calendar.blocks.event.settings.event_description.label"
        },
        {
          "type": "text",
          "id": "event_location",
          "default": "Event Location",
          "label": "t:sections.events-calendar.blocks.event.settings.event_location.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.events-calendar.blocks.event.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.events-calendar.blocks.event.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "show_text_box",
          "label": "t:sections.sticky-banners.blocks.slide.settings.show_text_box.label",
          "default": false
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "t:sections.sticky-banners.blocks.slide.settings.image_overlay_opacity.label",
          "default": 60
        },
        {
          "type": "color_scheme",
          "id": "color_scheme_1",
          "label": "t:sections.all.colors.label",
          "default": "option-3"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.events-calendar.presets.name",
      "blocks": [
        {
          "type": "event"
        },
        {
          "type": "event"
        },
        {
          "type": "event"
        }
      ]
    }
  ]
}
{% endschema %}

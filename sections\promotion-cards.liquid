{{ 'section-promotion-cards.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<script src="{{ 'promotion-cards.js' | asset_url }}" defer="defer"></script>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider
    assign show_desktop_slider = true
  endif
-%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
  <div class="color-{{ section.settings.color_scheme }} gradient {% if section.settings.title == blank %} no-heading{% endif %} {{ section.settings.promotion_cards_style }} {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
    <div class="promotion-cards-section page-width section-{{ section.id }}-padding extract" data-aos="fade-up">
      <slider-component class="slider-mobile-gutter {% if show_desktop_slider %} slider-component-desktop{% endif %} {% if show_mobile_slider == true and show_desktop_slider == false  %} slider-buttons-desktop-hide{% endif %} {% if show_mobile_slider == false and show_desktop_slider == true  %} slider-buttons-mobile-hide{% endif %}">
        <div class="collection-info grid">
          <div class="grid_item">
            {%- unless section.settings.title == blank -%}
              <div class="promotion-cards-title">
                <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
                  {{ section.settings.caption | escape }}
                </p>
                <{{ section.settings.heading_tag }} class="title {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }} heading-bold">
                  {{ section.settings.title | escape }}
                </{{ section.settings.heading_tag }}>
                {%- if section.settings.button_label != blank and show_mobile_slider -%}
                  <a href="{{ section.settings.button_link }}" class="link underlined-link large-up-hide">
                    {{- section.settings.button_label | escape -}}
                  </a>
                {%- endif -%}
              </div>
            {%- endunless -%}
          </div>

          <div class="grid_item">
            {%- if show_mobile_slider or show_desktop_slider -%}
              <div class="disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden">
                <button
                  type="button"
                  class="slider-button slider-button--prev"
                  name="previous"
                  aria-label="{{ 'general.slider.previous_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
                <button
                  type="button"
                  class="slider-button slider-button--next"
                  name="next"
                  aria-label="{{ 'general.slider.next_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
              </div>
            {%- endif -%}
          </div>
        </div>
        <ul
          class="promotion-cards-list contains-content-container grid grid--1-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop {% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
          id="Slider-{{ section.id }}"
          role="list"
        >
          {%- liquid
            assign highest_ratio = 0
            for block in section.blocks
              if block.settings.image.aspect_ratio > highest_ratio
                assign highest_ratio = block.settings.image.aspect_ratio
              endif
            endfor
          -%}
          {%- for block in section.blocks -%}
            {%- assign empty_column = '' -%}
            {%- if block.settings.image == blank
              and block.settings.title == blank
              and block.settings.text == blank
              and block.settings.link_label == blank
            -%}
              {%- assign empty_column = ' promotion-cards-list__item--empty' -%}
            {%- endif -%}

            <li
              id="Slide-{{ section.id }}-{{ forloop.index }}"
              class="promotion-cards-list__item grid-item{% if section.settings.swipe_on_mobile %} slider-slide{% endif %}{% if section.settings.column_alignment == 'center' %} center{% endif %}{{ empty_column }}"
              {{ block.shopify_attributes }}
            >
              <div class="promotion-cards content-container">
                {%- if block.settings.image != blank -%}
                  <a
                    {% if block.settings.link == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.link }}"
                    {% endif %}
                  >
                    <div class="promotion-cards-image">
                      <div class="promotion-cards-media media media--square">
                        {%- capture sizes -%}(min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %}, (min-width: 750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %}, calc(100vw - 30px){%- endcapture -%}
                        {{
                          block.settings.image
                          | image_url: width: 1420
                          | image_tag:
                            loading: 'lazy',
                            sizes: sizes,
                            widths: '275, 550, 710, 1420',
                            class: 'multicolumn-card__image'
                        }}
                      </div>
                    </div>
                  </a>
                {%- else -%}
                <div class="promotion-cards-media media media--square">
                  {%- assign placeholder_slide = forloop.index | modulo: 4 -%}
                  {%- if placeholder_slide == 1 -%}
                    {{ 'collection-3' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- elsif placeholder_slide == 2 -%}
                    {{ 'collection-4' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- elsif placeholder_slide == 3 -%}
                    {{ 'collection-5' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- else -%}
                    {{ 'collection-6' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- endif -%}
                </div>
                {%- endif -%}

                <div class="color-{{ section.settings.color_scheme_1 }} gradient promotion-cards-text">
                  {%- if block.settings.caption != blank -%}
                    <p class="caption caption-with-letter-spacing--small caption-with-letter-spacing">
                      {{ block.settings.caption | escape }}
                    </p>
                  {%- endif -%}
                  {%- if block.settings.title != blank -%}
                    <h3 class="heading-bold">{{ block.settings.title | escape }}</h3>
                  {%- endif -%}
                  {%- if block.settings.link_label != blank -%}
                    <a
                      class="link animate-arrow"
                      {% if block.settings.link == blank %}
                        role="link" aria-disabled="true"
                      {% else %}
                        href="{{ block.settings.link }}"
                      {% endif %}
                    >
                      {{- block.settings.link_label | escape -}}
                      <span class="icon-wrap">&nbsp;{% render 'icon-arrow' %}</span></a
                    >
                  {%- endif -%}
                </div>
              </div>
            </li>
          {%- endfor -%}
        </ul>
      </slider-component>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.promotion-cards.name",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "text",
      "id": "title",
      "default": "Promotion Cards",
      "label": "t:sections.promotion-cards.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "text",
      "id": "caption",
      "default": "Share your best offers",
      "label": "t:sections.promotion-cards.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "select",
      "id": "promotion_cards_style",
      "options": [
        {
          "value": "promotion-cards-one",
          "label": "t:sections.promotion-cards.settings.promotion_cards_style.options__1.label"
        },
        {
          "value": "promotion-cards-two",
          "label": "t:sections.promotion-cards.settings.promotion_cards_style.options__2.label"
        },
        {
          "value": "promotion-cards-three",
          "label": "t:sections.promotion-cards.settings.promotion_cards_style.options__3.label"
        }
      ],
      "default": "promotion-cards-two",
      "label": "t:sections.promotion-cards.settings.promotion_cards_style.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "t:sections.promotion-cards.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "enable_desktop_slider",
      "label": "t:sections.collection-list.settings.enable_desktop_slider.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.promotion-cards.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.promotion-cards.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.all.disable_arrow_mobile.label"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.promotion-cards.blocks.column.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.promotion-cards.blocks.column.settings.image.label"
        },
        {
          "type": "text",
          "id": "caption",
          "default": "Caption Text",
          "label": "t:sections.promotion-cards.blocks.column.settings.caption.label"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Column",
          "label": "t:sections.promotion-cards.blocks.column.settings.title.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.promotion-cards.blocks.column.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.promotion-cards.blocks.column.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.promotion-cards.presets.name",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}

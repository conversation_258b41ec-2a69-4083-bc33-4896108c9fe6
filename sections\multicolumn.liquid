{{ 'section-multicolumn.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  .multicolumn video {
    border-radius: var(--media-radius);
    width: 100%;
  }

  .multicolumn .style-two .video-bg {
    margin-right: 1rem;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif
-%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
<div class="multicolumn color-{{ section.settings.color_scheme }} gradient {% if section.settings.title == blank %} no-heading{% endif %} {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
  <div
    class="page-width section-{{ section.id }}-padding extract {{ section.settings.multicolumn_style }}"
    data-aos="fade-up"
  >
    {%- if section.settings.caption != blank -%}
      <div class="title-wrapper--self-padded-mobile">
        <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
          {{ section.settings.caption | escape }}
        </p>
      </div>
    {%- endif -%}
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin">
        <{{ section.settings.heading_tag }} class="title {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }} heading-bold">
          {{ section.settings.title | escape }}
        </{{ section.settings.heading_tag }}>
        {%- if section.settings.button_label != blank and show_mobile_slider -%}
          <a href="{{ section.settings.button_link }}" class="button-arrow link underlined-link large-up-hide">
            {{- section.settings.button_label | escape -}}
            {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
          </a>
        {%- endif -%}
      </div>
    {%- endunless -%}
    <slider-component class="slider-mobile-gutter">
      {%- if show_mobile_slider -%}
        <div class="disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden">
          <button
            type="button"
            class="slider-button slider-button--prev"
            name="previous"
            aria-label="{{ 'general.slider.previous_slide' | t }}"
            aria-controls="Slider-{{ section.id }}"
          >
            {% render 'icon-slider-arrows' %}
          </button>
          <button
            type="button"
            class="slider-button slider-button--next"
            name="next"
            aria-label="{{ 'general.slider.next_slide' | t }}"
            aria-controls="Slider-{{ section.id }}"
          >
            {% render 'icon-slider-arrows' %}
          </button>
        </div>
      {%- endif -%}
      <ul
        class="multicolumn-list contains-content-container grid grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop{% if show_mobile_slider %} slider slider--mobile grid--peek{% endif %}"
        id="Slider-{{ section.id }}"
        role="list"
      >
        {%- liquid
          assign highest_ratio = 0
          for block in section.blocks
            if block.settings.image.aspect_ratio > highest_ratio
              assign highest_ratio = block.settings.image.aspect_ratio
            endif
          endfor
        -%}
        {%- for block in section.blocks -%}
          {%- assign empty_column = '' -%}
          {%- if block.settings.image == blank
            and block.settings.title == blank
            and block.settings.text == blank
            and block.settings.link_label == blank
          -%}
            {%- assign empty_column = ' multicolumn-list-item--empty' -%}
          {%- endif -%}

          <li
            id="Slide-{{ section.id }}-{{ forloop.index }}"
            class="multicolumn-list-item grid-item{% if section.settings.swipe_on_mobile %} slider-slide{% endif %}{% if section.settings.column_alignment == 'center' %} center{% endif %}{{ empty_column }}"
            {{ block.shopify_attributes }}
          >
            <div class="color-{{ section.settings.color_scheme_1 }} gradient multicolumn-card {% if section.settings.enable_card_background == false %} card-content-container-transparent{% endif %} {% if section.settings.enable_card_background == true %} card-content-container{% endif %}">
              
             {% if block.settings.hide_image != true %}
              {%- if block.settings.image != blank -%}
                {% if section.settings.image_ratio == 'adapt' or section.settings.image_ratio == 'circle' %}
                  {% assign spaced_image = true %}
                {% endif %}

                <div class="multicolumn-card-image-wrapper multicolumn-card-image-wrapper--{{ section.settings.image_width }}-width{% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}">
                  <div
                    class="media media--transparent {% if section.settings.enable_card_background == true %}global-media-settings{% endif %} media--{{ section.settings.image_ratio }}"
                    {% if section.settings.image_ratio == 'adapt' %}
                      style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"
                    {% endif %}
                  >
                    {%- capture sizes -%}(min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %}, (min-width: 750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %}, calc(100vw - 30px){%- endcapture -%}
                    {{
                      block.settings.image
                      | image_url: width: 1420
                      | image_tag:
                        loading: 'lazy',
                        sizes: sizes,
                        widths: '275, 550, 710, 1420',
                        class: 'multicolumn-card__image'
                    }}
                  </div>
                </div>
                {%- else -%}
                {%- if block.settings.video_url != blank -%}
                  <div class="video-bg">
                    {{ block.settings.video_url | video_tag: autoplay: true, loop: true }}
                  </div>
                {%- endif -%}
              {%- else -%}
                <div class="multicolumn-card-image-wrapper multicolumn-card-image-wrapper--{{ section.settings.image_width }}-width{% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}">
                  <div
                    class="media media--transparent {% if section.settings.enable_card_background == true %}global-media-settings{% endif %} media--{{ section.settings.image_ratio }}"
                    {% if section.settings.image_ratio == 'adapt' %}
                      style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"
                    {% endif %}
                  >
                      {{ 'collection-1' | placeholder_svg_tag: 'placeholder-svg' }}
                  </div>
                </div>
              {% endif %}
            {%- endif -%}

              
              <div class="multicolumn-card-info">
                {%- if block.settings.title != blank -%}
                  <h3 class="heading-bold">{{ block.settings.title | escape }}</h3>
                {%- endif -%}
                {%- if block.settings.text != blank -%}
                  <div class="rte">{{ block.settings.text }}</div>
                {%- endif -%}
                {%- if block.settings.link_label != blank -%}
                  <a
                    class="button-arrow button"
                    {% if block.settings.link == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.link }}"
                    {% endif %}
                  >
                    {{- block.settings.link_label | escape -}}
                    {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
                  </a>
                {%- endif -%}
              </div>
            </div>
          </li>
        {%- endfor -%}
      </ul>
    </slider-component>
    <div class="center{% if show_mobile_slider %} small-hide medium-hide{% endif %}">
      {%- if section.settings.button_label != blank -%}
        <a
          class="button-arrow button button--primary"
          {% if section.settings.button_link == blank %}
            role="link" aria-disabled="true"
          {% else %}
            href="{{ section.settings.button_link }}"
          {% endif %}
        >
          {{ section.settings.button_label | escape }}
          {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
        </a>
      {%- endif -%}
    </div>
  </div>
</div>
</div>

{% schema %}
{
  "name": "t:sections.multicolumn.name",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "multicolumn_style",
      "options": [
        {
          "value": "style-one",
          "label": "t:sections.multicolumn.settings.multicolumn_style.options__1.label"
        },
        {
          "value": "style-two",
          "label": "t:sections.multicolumn.settings.multicolumn_style.options__2.label"
        }
      ],
      "default": "style-two",
      "label": "t:sections.multicolumn.settings.multicolumn_style.label"
    },
    {
      "type": "checkbox",
      "id": "enable_card_background",
      "default": false,
      "label": "t:sections.multicolumn.settings.enable_card_background.label"
    },
    {
      "type": "text",
      "id": "caption",
      "default": "Image Caption",
      "label": "t:sections.multicolumn.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Multicolumn",
      "label": "t:sections.multicolumn.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "select",
      "id": "image_width",
      "options": [
        {
          "value": "fourth",
          "label": "t:sections.multicolumn.settings.image_width.options__1.label"
        },
        {
          "value": "third",
          "label": "t:sections.multicolumn.settings.image_width.options__2.label"
        },
        {
          "value": "half",
          "label": "t:sections.multicolumn.settings.image_width.options__3.label"
        },
        {
          "value": "full",
          "label": "t:sections.multicolumn.settings.image_width.options__4.label"
        }
      ],
      "default": "fourth",
      "label": "t:sections.multicolumn.settings.image_width.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.multicolumn.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.multicolumn.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.multicolumn.settings.image_ratio.options__3.label"
        },
        {
          "value": "circle",
          "label": "t:sections.multicolumn.settings.image_ratio.options__4.label"
        },
        {
          "value": "asymmetrical",
          "label": "t:sections.multicolumn.settings.image_ratio.options__5.label"
        }
      ],
      "default": "square",
      "label": "t:sections.multicolumn.settings.image_ratio.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 3,
      "label": "t:sections.multicolumn.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multicolumn.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multicolumn.settings.column_alignment.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multicolumn.settings.column_alignment.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.multicolumn.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.multicolumn.settings.button_link.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.multicolumn.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.multicolumn.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.all.disable_arrow_mobile.label"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.multicolumn.blocks.column.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multicolumn.blocks.column.settings.image.label"
        },
        {
          "type": "checkbox",
          "id": "hide_image",
          "default": false,
          "label": "t:sections.multicolumn.blocks.column.settings.hide_image.label"
        },
        {
          "type": "video",
          "id": "video_url",
          "label": "t:sections.video-background.settings.video_url.label"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Column",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.multicolumn.blocks.column.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.multicolumn.blocks.column.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multicolumn.presets.name",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}

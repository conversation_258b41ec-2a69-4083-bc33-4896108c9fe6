{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'section-collection-list.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}
<div class="main-list-collections-section color-{{ section.settings.color_scheme }} gradient">
<div class="page-width section-{{ section.id }}-padding">
  <h1 class="title heading-bold title--primary">{{ section.settings.title | escape }}</h1>
  {%- liquid
    case section.settings.sort
      when 'products_high', 'products_low'
        assign collections = collections | sort: 'all_products_count'
      when 'date', 'date_reversed'
        assign collections = collections | sort: 'published_at'
    endcase

    if section.settings.sort == 'products_high' or section.settings.sort == 'date_reversed' or section.settings.sort == 'alphabetical_reversed'
      assign collections = collections | reverse
    endif

    assign moduloResult = 28 | modulo: section.settings.columns_desktop
    assign paginate_by = 30
    if moduloResult == 0
      assign paginate_by = 28
    endif
  -%}
  {%- paginate collections by paginate_by -%}
    <ul
      class="collection-list grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--1-col-tablet-down"
      role="list"
      data-aos="fade-up"
    >
      {%- for collection in collections -%}
        <li class="collection-list__item grid-item">
          {% render 'card-collection',
            card_collection: collection,
            media_aspect_ratio: section.settings.image_ratio,
            columns: 3
          %}
        </li>
      {%- endfor -%}
    </ul>
    {% render 'pagination', paginate: paginate %}
  {%- endpaginate -%}
</div>
</div>
{% schema %}
{
  "name": "t:sections.main-list-collections.name",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.main-list-collections.settings.title.label",
      "default": "Collections"
    },
    {
      "type": "select",
      "id": "sort",
      "options": [
        {
          "value": "alphabetical",
          "label": "t:sections.main-list-collections.settings.sort.options__1.label"
        },
        {
          "value": "alphabetical_reversed",
          "label": "t:sections.main-list-collections.settings.sort.options__2.label"
        },
        {
          "value": "date_reversed",
          "label": "t:sections.main-list-collections.settings.sort.options__3.label"
        },
        {
          "value": "date",
          "label": "t:sections.main-list-collections.settings.sort.options__4.label"
        },
        {
          "value": "products_high",
          "label": "t:sections.main-list-collections.settings.sort.options__5.label"
        },
        {
          "value": "products_low",
          "label": "t:sections.main-list-collections.settings.sort.options__6.label"
        }
      ],
      "default": "alphabetical",
      "label": "t:sections.main-list-collections.settings.sort.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-list-collections.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.main-list-collections.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-list-collections.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.main-list-collections.settings.image_ratio.label",
      "info": "t:sections.main-list-collections.settings.image_ratio.info"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 3,
      "label": "t:sections.main-list-collections.settings.columns_desktop.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}

{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'section-related-products.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'quick-add.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and recommendations.products_count > columns_mobile_int
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider
    assign show_desktop_slider = true
  endif
-%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
  <div
    class="color-{{ section.settings.color_scheme }} gradient no-js-hidden margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin"
    data-aos="fade-up"
  >
    <product-recommendations
      class="related-products style-two page-width section-{{ section.id }}-padding extract {% if section.settings.swipe_on_mobile == false  %}no-margin-right{% endif %} "
      data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ section.settings.products_to_show }}"
    >
      {% if recommendations.performed and recommendations.products_count > 0 %}
        <slider-component class="slider-mobile-gutter slider-component-desktop {% if show_mobile_slider == true and show_desktop_slider == false  %} slider-buttons-desktop-hide{% endif %} {% if show_mobile_slider == false and show_desktop_slider == true  %} slider-buttons-mobile-hide{% endif %}">
          <div class="collection-info grid">
            <div class="grid_item">
              {%- if section.settings.caption != blank -%}
                <p class="{{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
                  {{ section.settings.caption | escape }}
                </p>
              {%- endif -%}

              {%- if section.settings.heading != blank -%}
                <{{ section.settings.heading_tag }} class="related-products__heading heading-bold {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }}">
                  {{ section.settings.heading | escape }}
                </{{ section.settings.heading_tag }}>
              {%- endif -%}
            </div>

            <div class="grid_item">
              {%- if show_mobile_slider or show_desktop_slider -%}
                <div class="disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden">
                  <button
                    type="button"
                    class="slider-button slider-button--prev"
                    name="previous"
                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                    aria-controls="Slider-{{ section.id }}"
                  >
                    {% render 'icon-slider-arrows' %}
                  </button>
                  <button
                    type="button"
                    class="slider-button slider-button--next"
                    name="next"
                    aria-label="{{ 'general.slider.next_slide' | t }}"
                    aria-controls="Slider-{{ section.id }}"
                  >
                    {% render 'icon-slider-arrows' %}
                  </button>
                </div>
              {%- endif -%}
            </div>
          </div>

          <ul
            class="grid product-grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down {% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
            id="Slider-{{ section.id }}"
            role="list"
          >
            {% for recommendation in recommendations.products %}
              <li id="Slide-{{ section.id }}-{{ forloop.index }}" class="grid-item">
                {% render 'card-product',
                  card_product: recommendation,
                  media_aspect_ratio: section.settings.image_ratio,
                  show_secondary_image: section.settings.show_secondary_image,
                  show_vendor: section.settings.show_vendor,
                  show_rating: section.settings.show_rating
                %}
              </li>
            {% endfor %}
          </ul>
        </slider-component>
      {% endif %}
    </product-recommendations>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.related-products.name",
  "tag": "section",
  "class": "section",
  "settings": [
      {
        "type": "paragraph",
        "content": "t:sections.related-products.settings.paragraph__1.content"
      },
      {
        "type": "text",
        "id": "caption",
        "default": "Caption",
        "label": "t:sections.related-products.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.related-products.settings.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.related-products.settings.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.related-products.settings.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.related-products.settings.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.related-products.settings.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.related-products.settings.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.related-products.settings.text_size.label"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "You may also like",
      "label": "t:sections.related-products.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 10,
      "step": 1,
      "default": 6,
      "label": "t:sections.related-products.settings.products_to_show.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "t:sections.related-products.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "enable_desktop_slider",
      "label": "t:sections.collection-list.settings.enable_desktop_slider.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.related-products.settings.header__2.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.related-products.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.related-products.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.related-products.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.related-products.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.related-products.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.related-products.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.related-products.settings.show_rating.label",
      "info": "t:sections.related-products.settings.show_rating.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-2"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.related-products.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.related-products.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.related-products.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.related-products.settings.columns_mobile.label"
    },
     {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": true,
      "label": "t:sections.promotion-cards.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.all.disable_arrow_mobile.label"
    }
  ]
}
{% endschema %}

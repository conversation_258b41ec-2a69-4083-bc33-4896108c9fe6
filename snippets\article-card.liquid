{% comment %}
  Renders an article card for a given blog with settings to either show the image or not.

  Accepts:
  - blog: {Object} Blog object
  - article: {Object} Article object
  - media_aspect_ratio: {String} The setting changes the aspect ratio of the article image, if shown
  - media_height: {String} The setting changes the height of the article image. Overrides media_aspect_ratio.
  - show_image: {String} The setting either show the article image or not. If it's not included it will show the image by default
  - show_date: {String} The setting either show the article date or not. If it's not included it will not show the image by default
  - show_author: {String} The setting either show the article author or not. If it's not included it will not show the author by default
  - show_badge: {String} The setting either show the blog badge or not.
  - lazy_load: {Boolean} Image should be lazy loaded. Default: true (optional)

  Usage:
  {% render 'article-card' blog: blog, article: article, show_image: section.settings.show_image %}
{% endcomment %}

{%- if article and article != empty -%}
  {%- liquid
    assign ratio = 1
    if media_aspect_ratio != null
      assign ratio = media_aspect_ratio
    endif
  -%}
  <div class="article-card-wrapper card-wrapper underline-links-hover">
    {% comment %} <article aria-labelledby="Article-{{ article.id }}"> {% endcomment %}
    <div
      class="
        {% if section.settings.blog_style == "blog-style-two" %} color-{{ section.settings.color_scheme_1 }} gradient{% endif %}
        card article-card
        card--{{ settings.blog_card_style }}
        {% if media_height and media_height != 'adapt' %} article-card-image--{{ media_height }}{% endif %}
        {% if article.image and show_image %} card--media{% else %} card--text{% endif %}
        {% if settings.blog_card_style == 'card' %} color-{{ settings.blog_card_color_scheme }} {% endif %}
        {% if settings.blog_card_style == 'card' and media_height == nil and article.image == empty or show_image == false %} ratio{% endif %}
      "
      style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
    >
      <div
        class="card-inner {% if settings.blog_card_style == 'standard' %} color-{{ settings.blog_card_color_scheme }} {% endif %}{% if article.image and show_image or settings.blog_card_style == 'standard' %} ratio{% endif %}"
        style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
      >
        {%- if show_image == true and article.image -%}
          <div class="article-card-image-wrapper card-media">
            <div
              class="article-card-image media media--hover-effect"
              {% if section.settings.media_height == 'adapt' %}
                style="padding-bottom: {{ 1 | divided_by: article.image.aspect_ratio | times: 100 }}%;"
              {% endif %}
            >
              {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
              <img
                srcset="
                  {%- if article.image.src.width >= 165 -%}{{ article.image.src | image_url: width: 165 }} 165w,{%- endif -%}
                  {%- if article.image.src.width >= 360 -%}{{ article.image.src | image_url: width: 360 }} 360w,{%- endif -%}
                  {%- if article.image.src.width >= 533 -%}{{ article.image.src | image_url: width: 533 }} 533w,{%- endif -%}
                  {%- if article.image.src.width >= 720 -%}{{ article.image.src | image_url: width: 720 }} 720w,{%- endif -%}
                  {%- if article.image.src.width >= 1000 -%}{{ article.image.src | image_url: width: 1000 }} 1000w,{%- endif -%}
                  {%- if article.image.src.width >= 1500 -%}{{ article.image.src | image_url: width: 1500 }} 1500w,{%- endif -%}
                  {{ article.image.src | image_url }} {{ article.image.src.width }}w
                "
                src="{{ article.image.src | image_url: width: 720 }}"
                sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
                alt="{{ article.image.src.alt | escape }}"
                class="motion-reduce"
                {% unless lazy_load == false %}
                  loading="lazy"
                {% endunless %}
                width="{{ article.image.width }}"
                height="{{ article.image.height }}"
              >
              {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
            </div>
          </div>
        {%- endif -%}
        <div class="card-content additional-card-content">
          <div class="color-{{ section.settings.color_scheme_1 }} gradient card-information">
            <div class="article-card-info caption-with-letter-spacing h5">
              {%- if show_date -%}
                <div class="article-date">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="icon-calendar" viewBox="0 0 16 16">
                <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5M2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1z"/>
                </svg>
                <span class="circle-divider">{{ article.published_at | time_tag: format: 'date' }}</span>
              </div>
              {%- endif -%}
              {%- if show_author -%}
                <div class="article-author">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="icon-author" viewBox="0 0 16 16">
                <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0"/>
                <path fill-rule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1"/>
                </svg>
                <span>{{ article.author }}</span>
                </div>
              {%- endif -%}
            </div>

            <h3 class="card-heading{% if show_excerpt %} h2{% endif %} heading-bold">
              <a href="{{ article.url }}" class="full-unstyled-link">
                {{ article.title | escape }}
              </a>
            </h3>
            
            {%- if show_excerpt -%}
              {%- if article.excerpt.size > 0 or article.content.size > 0 -%}
                <p class="article-card-excerpt rte-width">
                  {%- if article.excerpt.size > 0 -%}
                    {{ article.excerpt | strip_html | truncatewords: 30 }}
                  {%- else -%}
                    {{ article.content | strip_html | truncatewords: 30 }}
                  {%- endif -%}
                </p>
              {%- endif -%}
              
              <div class="article-card-footer">
                {%- if article.comments_count > 0 and blog.comments_enabled? -%}
                  <span>{{ 'blogs.article.comments' | t: count: article.comments_count }}</span>
                {%- endif -%}
              </div>
            {%- endif -%}
          </div>
          {%- if show_badge -%}
            <div class="card-badge {{ settings.badge_position }}">
              <span class="badge color-background-1">{{ 'blogs.article.blog' | t }}</span>
            </div>
          {%- endif -%}
        </div>
      </div>
      <div class="card-content bottom-card-content">
        <div class="color-{{ section.settings.color_scheme_1 }} gradient card-information">
          <div class="article-card-info caption-with-letter-spacing h5">
            {%- if show_date -%}
              <div class="article-date">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="icon-calendar" viewBox="0 0 16 16">
              <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5M2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1z"/>
              </svg>
              <span class="circle-divider">{{ article.published_at | time_tag: format: 'date' }}</span>
            </div>
            {%- endif -%}
            {%- if show_author -%}
              <div class="article-author">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="icon-author" viewBox="0 0 16 16">
              <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0"/>
              <path fill-rule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1"/>
              </svg>
              <span>{{ article.author }}</span>
              </div>
            {%- endif -%}
          </div>
          <h3 class="card-heading{% if show_excerpt %} h2{% endif %} heading-bold">
            <a href="{{ article.url }}" class="full-unstyled-link">
              {{ article.title | escape }}
            </a>
          </h3>
          {%- if show_excerpt -%}
            {%- if article.excerpt.size > 0 or article.content.size > 0 -%}
              <p class="article-card-excerpt rte-width">
                {%- if article.excerpt.size > 0 -%}
                  {{ article.excerpt | strip_html | truncatewords: 30 }}
                {%- else -%}
                  {{ article.content | strip_html | truncatewords: 30 }}
                {%- endif -%}
              </p>
            {%- endif -%}
            <div class="article-card-footer">
              {%- if article.comments_count > 0 and blog.comments_enabled? -%}
                <span>{{ 'blogs.article.comments' | t: count: article.comments_count }}</span>
              {%- endif -%}
            </div>
          {%- endif -%}
        </div>
        {%- if show_badge -%}
          <div class="card-badge {{ settings.badge_position }}">
            <span class="badge color-background-1">{{ 'blogs.article.blog' | t }}</span>
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
{%- endif -%}

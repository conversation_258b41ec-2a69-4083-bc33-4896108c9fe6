{{ 'section-image-banner.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-rte.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'section-featured-collections.css' | asset_url | stylesheet_tag }}
{{ 'collection-list-styles.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'template-collection.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    .image-banner-with-collections .banner-box p {
      padding: 0;
      margin: 0;
    }

    .image-banner-with-collections .banner-box p em.in-view {
      position: relative;
    }

    .image-banner-with-collections .featured-collections-wrapper h3.card-heading-q {
        font-size: calc(var(--font-heading-scale)* 1.8rem);
    }

    .image-banner-with-collections .collection-one .card-media .media img {
      background: none;
      -webkit-mask-image: none;
      mask-image: none;
    }

    .image-banner-with-collections .featured-collections-wrapper.grid {
      padding-left: 0;
      padding-right: 0;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }

    .section-{{ section.id }}-desktop-margin {
      margin-top: -{{ section.settings.desktop_margin_top }}px;
      position: relative;
      z-index: 1;
    }

    @media screen and (max-width: 990px) {
      #Banner-{{ section.id }}::after {
        opacity: {{ section.settings.image_overlay_opacity_mobile | divided_by: 100.0 }};
      }
      
      .section-{{ section.id }}-mobile-margin {
        margin-top: -{{ section.settings.mobile_margin_top }}px;
        position: relative;
        z-index: 1;
      }

      .image-banner-with-collections slider-component.placeholder-image.slider-no-padding.page-width.slider-component-mobile {
        margin-right: -1.5rem;
      }

      .image-banner-with-collections .slider--tablet  .card-content-1 {
        width: 70%;
      }

      .image-banner-with-collections .slider--tablet  h3.card-heading-q {
        font-size: calc(var(--font-heading-scale)* 1.4rem);
      }
    }

     @media screen and (min-width: 990px) {
      .image-banner-with-collections.boxed {
        max-width: var(--page-width);
        margin: 0 auto;
      }
  
      .image-banner-with-collections.boxed .banner-media.media,
      .image-banner-with-collections.boxed .banner-media.media img,
      .image-banner-with-collections.boxed .image-banner-with-featured-collection .banner--height,
      .image-banner-with-collections.boxed .banner:after, 
      .image-banner-with-collections.boxed .banner-media:after{
        border-radius: calc(var(--media-radius));
      }
       
      #Banner-{{ section.id }}::after {
        opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
      }
     .image-mobile {
        display: none;
      }
     .image-banner-with-collections .banner--height {
       min-height: {{ section.settings.image_height}}rem;
     }
     }

    @media screen and (max-width: 990px) {
       .image-banner-with-collections .banner--height {
         min-height: {{ section.settings.image_height_mobile}}rem;
       }
     }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }

  .image-banner-with-collections .highlight-underline.banner-heading-{{ section.id }} em.in-view::after {
     content: "";
     position: absolute;
     left: 50%;
     transform: translateX(-50%);
     bottom: -5px;
     width: 120%;
     height: 15px;
     background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='10'%3E%3Cpath d='M10 10 Q 50 0 90 10' stroke='%23{{ section.settings.word_animation_color | remove: '#' }}' stroke-width='2' fill='none'/%3E%3C/svg%3E") no-repeat center;
     background-size: 100% 100%;
     opacity: 0;
     animation: underlineGrow 0.5s ease forwards;
     animation-delay: 0.6s;
     z-index: -1;
  }

  .image-banner-with-collections .highlight-underline-hand.banner-heading-{{ section.id }} em.in-view::after {
     content: "";
     position: absolute;
     left: 50%;
     transform: translateX(-50%) scaleX(-1);
     bottom: -15px;
     width: 100%;
     height: 0.6em;
     background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='167' height='10' viewBox='0 0 167 10'%3E%3Cpath d='M5 8C40 4 130 -1 162 6' stroke='%23{{ section.settings.word_animation_color | remove: '#' }}' stroke-width='3' stroke-linecap='round' stroke-linejoin='round' fill='none'/%3E%3C/svg%3E") no-repeat center;
     background-size: contain;
     opacity: 0;
     animation: underlineGrow 0.5s ease forwards;
     animation-delay: 0.6s;
     z-index: -1;
  }
 
  .image-banner-with-collections .highlight-circle-hand.banner-heading-{{ section.id }} em.in-view::after {
     content: "";
     position: absolute;
     left: 50%;
     transform: translateX(-50%);
     bottom: -8px;
     width: 140%;
     height: 1.5em;
     background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 218 60'%3E%3Cpath d='M5 45C8 35 22 20 75 8C140 -5 205 15 198 40C190 60 120 55 85 57C55 58 20 55 12 45C10 40 15 35 25 30' stroke='%23{{ section.settings.word_animation_color | remove: '#' }}' stroke-width='4' stroke-linecap='round' fill='none'/%3E%3C/svg%3E") no-repeat center;
     background-size: 100% 100%;
     opacity: 0;
     animation: circleGrow 0.5s ease forwards;
     animation-delay: 0.6s;
     z-index: -1;
  }

  .image-banner-with-collections .highlight-circle.banner-heading-{{ section.id }} em.in-view::after {
     content: "";
     position: absolute;
     left: 50%;
     transform: translateX(-50%);
     bottom: -30px;
     width: 120%;
     height: 2em;
     background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='226' height='92' viewBox='0 0 226 92'%3E%3Cpath d='M223.164 36.9069C223.618 42.3287 221.306 47.9316 216.296 53.4861C211.282 59.0442 203.693 64.3996 193.997 69.2175C174.617 78.8478 147.203 86.1414 116.362 88.7259C85.521 91.3104 57.2753 88.6811 36.5628 82.4106C26.2005 79.2735 17.8263 75.2559 11.9575 70.6097C6.09246 65.9666 2.87988 60.8266 2.42553 55.4048C1.97118 49.983 4.2835 44.3801 9.29397 38.8255C14.3077 33.2675 21.8964 27.9121 31.592 23.0942C50.9721 13.4639 78.3863 6.17031 109.227 3.58582C140.068 1.00133 168.314 3.63062 189.027 9.90111C199.389 13.0382 207.763 17.0558 213.632 21.702C219.497 26.3451 222.71 31.4851 223.164 36.9069Z' stroke='%23{{ section.settings.word_animation_color | remove: '#' }}' stroke-width='2' fill='none'/%3E%3C/svg%3E") no-repeat center;
     background-size: contain;
     opacity: 0;
     animation: circleGrow 0.5s ease forwards;
     animation-delay: 0.6s;
     z-index: -1;
  }

{%- endstyle -%}

{% comment %} Start Featured Collection {% endcomment %}
{%- liquid
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider
    assign show_desktop_slider = true
  endif
-%}

{% comment %} Start Image Banner with Featured Collection {% endcomment %}
<div class="image-banner-with-collections {% if section.settings.full_width_banner == false %}boxed{% endif %} ignore-{{ section.settings.ignore_spacing }}">
<div class="image-banner-with-featured-collection section-{{ section.id }}-padding {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin collection-list-one" data-aos="fade-up" data-aos-delay="250">
  <div
    id="Banner-{{ section.id }}"
    class="color-{{ section.settings.color_scheme }} gradient image-banner banner banner--content-align-{{ section.settings.desktop_content_alignment }} banner--content-align-mobile-{{ section.settings.mobile_content_alignment }} banner--height"
  >

  {% if section.settings.hide_image != true %}
    {% comment %} Start Image {% endcomment %}
    {%- if section.settings.image != blank -%}
      <div class="banner-media media placeholder" data-aos="fade-up" data-aos-delay="200">
        {%- liquid
          assign image_height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
          assign sizes = '100vw'
        -%}
        {{
          section.settings.image
          | image_url: width: 3840
          | image_tag:
            loading: 'lazy',
            width: section.settings.image.width,
            height: image_height,
            sizes: sizes,
            widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
        }}
      </div>
      {%- else -%}
      <div class="banner-media media placeholder" data-aos="fade-up">
        {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
      </div>
    {%- endif -%}
    {% comment %} End Image {% endcomment %}

    {% comment %} Start Mobile Image {% endcomment %}
    {%- if section.settings.image_mobile != blank -%}
      <div class="banner-media media placeholder image-mobile" data-aos="fade-up"data-aos-delay="200">
        {%- liquid
          assign image_height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
          assign sizes = '100vw'
        -%}
        {{
          section.settings.image_mobile
          | image_url: width: 990
          | image_tag:
            loading: 'lazy',
            width: section.settings.image.width,
            height: image_height,
            sizes: sizes,
            widths: '375, 550, 750, 1100'
        }}
      </div>
      {%- else -%}
        <div class="banner-media media placeholder image-mobile" data-aos="fade-up" data-aos-delay="200">
        {%- liquid
          assign image_height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
          assign sizes = '100vw'
        -%}
        {{
          section.settings.image
          | image_url: width: 3840
          | image_tag:
            loading: 'lazy',
            width: section.settings.image.width,
            height: image_height,
            sizes: sizes,
            widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
        }}
      </div>
      {%- else -%}
      <div class="banner-media media placeholder" data-aos="fade-up" data-aos-delay="200">
        {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
      </div>
    {%- endif -%}
    {% comment %} End Image {% endcomment %}
  {%- endif -%}

    {% comment %} Start Content {% endcomment %}
    <div class="color-{{ section.settings.color_scheme }} gradient banner-content banner-content--{{ section.settings.desktop_content_position }} page-width banner-show-box--false">
      <div class="banner-box content-container content-container--full-width-mobile false">
        {%- if section.settings.caption != blank -%}
          <div class="title-wrapper--self-padded-mobile">
            <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
              {{ section.settings.caption | escape }}
            </p>
          </div>
        {%- endif -%}

        {%- if section.settings.heading != blank -%}
        <{{ section.settings.heading_tag }}
          class="banner-heading animated-highlight banner-heading-{{ section.id }} highlight-{{ section.settings.highlight_option }} heading-bold {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }}"
          data-aos="fade-up" data-aos-delay="300">
          <span>{{ section.settings.heading }}</span>
        </{{ section.settings.heading_tag }}>
        {%- endif -%}
      

        {%- if section.settings.text != blank -%}
          <div class="banner-text {{ section.settings.richtext_style }}" data-aos="fade-up" data-aos-delay="300" {{ section.shopify_attributes }}>
            {{ section.settings.text }}
          </div>
        {%- endif -%}
            
        {%- if section.settings.button_label_1 != blank -%}
        <div
          class="banner-buttons{% if section.settings.button_label_1 != blank  %} banner-buttons--multiple{% endif %} button-arrow"
          data-aos="fade-up" data-aos-delay="300"
          {{ section.shopify_attributes }}
        >
          {%- if section.settings.button_label_1 != blank -%}
            <a
              {% if section.settings.button_link_1 == blank %}
                role="link" aria-disabled="true"
              {% else %}
                href="{{ section.settings.button_link_1 }}"
              {% endif %}
              class="button button--primary"
            >
              {{- section.settings.button_label_1 | escape -}}
              {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
            </a>
          {%- endif -%}
        </div>
        {%- endif -%}
      </div>
    </div>
    {% comment %} End Content {% endcomment %}
  </div>
  {% comment %} End Image Banner {% endcomment %}
                
  {% comment %} Start Featured Collection {% endcomment %}
  <div id="animation" class="extract">
    <div
      class="collection collection-one section-{{ section.id }}-padding{% if section.settings.full_width %} collection--full-width{% endif %} page-width"
      data-aos="fade-up"
    >
      <div class="collection-content section-{{ section.id }}-desktop-margin section-{{ section.id }}-mobile-margin">
        {% comment %} Start Collection Products {% endcomment %}
        <slider-component class="{%- if section.settings.collection == blank -%}placeholder-image{% endif %} slider-mobile-gutter{% if section.settings.full_width %} slider-component-full-width{% endif %} slider-no-padding page-width{% if show_desktop_slider == false and section.settings.full_width == false %} page-width-desktop{% endif %}{% if show_desktop_slider %} slider-component-desktop{% endif %} {% if show_mobile_slider %} slider-component-mobile{% endif %} {% if show_mobile_slider == true and show_desktop_slider == false  %} slider-buttons-desktop-hide{% endif %} {% if show_mobile_slider == false and show_desktop_slider == true %} slider-buttons-mobile-hide{% endif %}">
          
          {%- if show_mobile_slider or show_desktop_slider -%}
            <div class="color-{{ section.settings.color_scheme_1 }} gradient disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden">
              <button
                type="button"
                class="slider-button slider-button--prev"
                name="previous"
                aria-label="{{ 'general.slider.previous_slide' | t }}"
                aria-controls="Slider-{{ section.id }}"
              >
                {% render 'icon-slider-arrows' %}
              </button>
              <button
                type="button"
                class="slider-button slider-button--next"
                name="next"
                aria-label="{{ 'general.slider.next_slide' | t }}"
                aria-controls="Slider-{{ section.id }}"
              >
                {% render 'icon-slider-arrows' %}
              </button>
            </div>
          {%- endif -%}
        {% comment %} Start Subcollection {% endcomment %}
        <ul
          id="Slider-{{ section.id }}"
          class="featured-collections-wrapper grid product-grid contains-card contains-card--product{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop{% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
          role="list"
          aria-label="{{ 'general.slider.name' | t }}"
        >
          {%- for block in section.blocks -%}
            <li
              id="Slide-{{ section.id }}-{{ forloop.index }}"
              class="grid-item{% if show_mobile_slider or show_desktop_slider %} slider-slide{% endif %}"
              {{ block.shopify_attributes }}
            >
              {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
              {%- assign placeholder_image = 'collection-' | append: placeholder_image_index -%}
              {% render 'card-collection-one',
                card_collection: block.settings.collection,
                media_aspect_ratio: section.settings.image_ratio,
                placeholder_image: placeholder_image
              %}
            </li>
          {%- endfor -%}
        </ul>
        {% comment %} End Subcollection {% endcomment %}
        </slider-component>
        {% comment %} End Collection Products {% endcomment %}
      </div>
    </div>
  </div>
  {% comment %} End Featured Collection {% endcomment %}
</div>
</div>

{% schema %}
{
  "name": "t:sections.image-banner-with-collections.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-banner-with-collections.settings.image.label"
    },
    {
      "type": "checkbox",
      "id": "hide_image",
      "default": false,
      "label": "t:sections.image-banner-with-collections.settings.hide_image.label"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "%",
      "label": "t:sections.image-banner-with-collections.settings.image_overlay_opacity.label",
      "info": "t:sections.image-banner-with-collections.settings.image_overlay_opacity.info",
      "default": 20
    },
    {
      "type": "range",
      "id": "image_height",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "rem",
      "label": "t:sections.image-banner-with-collections.settings.image_height.label",
      "default": 60
    },
    {
      "type": "checkbox",
      "id": "full_width_banner",
      "label": "t:sections.image-banner-with-featured-collection.settings.full_width_banner.label",
      "default": true
    },
    {
      "type": "text",
      "id": "caption",
      "default": "Add a tagline",
      "label": "t:sections.image-banner-with-collections.settings.text.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "richtext",
      "id": "heading",
      "default": "<p>Heading <em>Highlight</em></p>",
      "label": "t:sections.image-banner-with-collections.settings.heading.label",
      "info": "t:sections.image-banner-with-collections.settings.heading.info"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "extra-large",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "select",
      "id": "highlight_option",
      "options": [
        {
          "value": "italic",
          "label": "t:sections.image-banner-with-collections.settings.highlight_option.options__1.label"
        },
        {
          "value": "underline",
          "label": "t:sections.image-banner-with-collections.settings.highlight_option.options__2.label"
        },
        {
          "value": "underline-hand",
          "label": "t:sections.image-banner-with-collections.settings.highlight_option.options__3.label"
        },
        {
          "value": "circle-hand",
          "label": "t:sections.image-banner-with-collections.settings.highlight_option.options__4.label"
        },
        {
          "value": "circle",
          "label": "t:sections.image-banner-with-collections.settings.highlight_option.options__5.label"
        }
      ],
      "default": "underline-hand",
      "label": "t:sections.image-banner-with-collections.settings.highlight_option.label"
    },
    {
      "type": "color",
      "id": "word_animation_color",
      "label": "t:sections.image-banner-with-collections.settings.word_animation_color.label",
      "default": "#FFFFFF"
    },
    {
      "type": "richtext",
      "id": "text",
      "default": "<p>Give customers details about the banner image(s) or content on the template.</p>",
      "label": "t:sections.image-banner-with-collections.settings.text.label"
    },
    {
      "type": "select",
      "id": "richtext_style",
      "options": [
        {
          "value": "body",
          "label": "t:sections.image-banner-with-collections.settings.text_style.options__1.label"
        },
        {
          "value": "subtitle",
          "label": "t:sections.image-banner-with-collections.settings.text_style.options__2.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.image-banner-with-collections.settings.text_style.options__3.label"
        }
      ],
      "default": "body",
      "label": "t:sections.image-banner-with-collections.settings.text_style.label"
    },
    {
      "type": "text",
      "id": "button_label_1",
      "default": "Button label",
      "label": "t:sections.image-banner-with-collections.settings.button_label_1.label",
      "info": "t:sections.image-banner-with-collections.settings.button_label_1.info"
    },
    {
      "type": "url",
      "id": "button_link_1",
      "label": "t:sections.image-banner-with-collections.settings.button_link_1.label"
    },
    {
    "type": "select",
    "id": "desktop_content_position",
    "options": [
        {
        "value": "middle-left",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_position.options__1.label"
        },
        {
        "value": "middle-center",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_position.options__2.label"
        },
        {
        "value": "middle-right",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_position.options__3.label"
        }
    ],
        "default": "middle-left",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_position.label"
    },
    {
    "type": "select",
    "id": "desktop_content_alignment",
    "options": [
        {
        "value": "left",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_alignment.options__1.label"
        },
        {
        "value": "center",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_alignment.options__2.label"
        },
        {
        "value": "right",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_alignment.options__3.label"
        }
    ],
        "default": "left",
        "label": "t:sections.image-banner-with-collections.settings.desktop_content_alignment.label"
    },
    {
      "type": "header",
      "content": "t:sections.image-banner-with-collections.settings.header_collections.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-banner-with-collections.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.image-banner-with-collections.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.image-banner-with-collections.settings.image_ratio.options__3.label"
        }
      ],
      "default": "square",
      "label": "t:sections.image-banner-with-collections.settings.image_ratio.label"
    },
    {
        "type": "checkbox",
        "id": "enable_desktop_slider",
        "label": "t:sections.image-banner-with-collections.settings.enable_desktop_slider.label",
        "default": true
    },
    {
        "type": "range",
        "id": "columns_desktop",
        "min": 1,
        "max": 6,
        "step": 1,
        "default": 5,
        "label": "t:sections.image-banner-with-collections.settings.columns_desktop.label"
    },
    {
        "type": "range",
        "id": "desktop_margin_top",
        "min": 0,
        "max": 600,
        "step": 10,
        "unit": "px",
        "label": "t:sections.image-banner-with-collections.settings.desktop_margin_top.label",
        "default": 190
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "1",
      "label": "t:sections.image-banner-with-collections.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.image-banner-with-collections.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.image-banner-with-collections.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": true,
      "label": "t:sections.image-banner-with-collections.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.image-banner-with-collections.settings.disable_arrow_mobile.label"
    },
    {
        "type": "range",
        "id": "mobile_margin_top",
        "min": 0,
        "max": 400,
        "step": 10,
        "unit": "px",
        "label": "t:sections.image-banner-with-collections.settings.mobile_margin_top.label",
        "default": 110
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 12
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.image-banner-with-collections.settings.header_mobile_image_banner.content"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "t:sections.image-banner-with-collections.settings.image.label"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity_mobile",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "%",
      "label": "t:sections.image-banner-with-collections.settings.image_overlay_opacity_mobile.label",
      "info": "t:sections.image-banner-with-collections.settings.image_overlay_opacity_mobile.info",
      "default": 50
    },
    {
      "type": "range",
      "id": "image_height_mobile",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "rem",
      "label": "t:sections.image-banner-with-collections.settings.image_height_mobile.label",
      "default": 56
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-banner-with-collections.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-banner-with-collections.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-banner-with-collections.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.image-banner-with-collections.settings.mobile_content_alignment.label"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-3"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    }
  ],
  "blocks": [
    {
      "type": "featured_collection",
      "name": "t:sections.image-banner-with-collections.blocks.featured_collection.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.image-banner-with-collections.blocks.featured_collection.settings.collection.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-banner-with-collections.presets.name",
      "blocks": [
        {
          "type": "featured_collection"
        },
        {
          "type": "featured_collection"
        },
        {
          "type": "featured_collection"
        },
        {
          "type": "featured_collection"
        },
        {
          "type": "featured_collection"
        }
      ]
    }
  ]
}
{% endschema %}
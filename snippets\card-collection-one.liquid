{% comment %}
  Renders a collection card

  Accepts:
  - card_collection: {Object} Collection Liquid object
  - media_aspect_ratio: {String} Size of the product image card. Values are "square" and "portrait". Default is "square" (optional)
  - columns: {Number}
  - extend_height: {<PERSON><PERSON>an} Card height extends to available container space. Default: false (optional)
  - wrapper_class: {String} Wrapper class for card (optional)

  Usage:
  {% render 'card-collection' %}
{% endcomment %}

{%- liquid
  assign ratio = 1
  if card_collection.featured_image and media_aspect_ratio == 'portrait'
    assign ratio = 0.8
  elsif card_collection.featured_image and media_aspect_ratio == 'adapt'
    assign ratio = card_collection.featured_image.aspect_ratio
  endif
  if ratio == 0 or ratio == null
    assign ratio = 1
  endif
  assign card_style = settings.card_style
  if wrapper_class == null or wrapper_class == 'none'
    assign card_style = settings.collection_card_style
  endif
-%}

<div class="card-wrapper {% if wrapper_class and wrapper_class != 'none' %}{{ wrapper_class }}{% else %}collection-card-wrapper{% endif %}">
  <div
    class="
      card
      card--{{ card_style }}
      {% if card_collection.featured_image %} card--media{% else %} card--text{% endif %}
      {% if section.settings.collection_list_style == "collection-list-three" %} color-{{ section.settings.color_scheme_1 }} gradient {% endif %}
      {% if extend_height %} card--extend-height{% endif %}
      {% if card_collection.featured_image == nil and card_style == 'card' %} ratio{% endif %}
    "
    style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
  >
    <div
      class="card-inner global-media-settings {% if card_collection.featured_image or card_style == 'standard' %} ratio{% endif %}"
      style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
    >
      {%- if card_collection.featured_image -%}
        <div class="card-media">
          <div class="media media--transparent media--hover-effect">
            <a href="{{ card_collection.url }}" aria-label="{{ 'sections.featured_collection.view_all_label' | t: collection_name: section.settings.collection.title }}">
              <img
                srcset="
                  {%- if card_collection.featured_image.width >= 165 -%}{{ card_collection.featured_image | image_url: width: 165 }} 165w,{%- endif -%}
                  {%- if card_collection.featured_image.width >= 330 -%}{{ card_collection.featured_image | image_url: width: 330 }} 330w,{%- endif -%}
                  {%- if card_collection.featured_image.width >= 535 -%}{{ card_collection.featured_image | image_url: width: 535 }} 535w,{%- endif -%}
                  {%- if card_collection.featured_image.width >= 750 -%}{{ card_collection.featured_image | image_url: width: 750 }} 750w,{%- endif -%}
                  {%- if card_collection.featured_image.width >= 1000 -%}{{ card_collection.featured_image | image_url: width: 1000 }} 1000w,{%- endif -%}
                  {%- if card_collection.featured_image.width >= 1500 -%}{{ card_collection.featured_image | image_url: width: 1500 }} 1500w,{%- endif -%}
                  {%- if card_collection.featured_image.width >= 3000 -%}{{ card_collection.featured_image | image_url: width: 3000 }} 3000w,{%- endif -%}
                  {{ card_collection.featured_image | image_url }} {{ card_collection.featured_image.width }}w
                "
                src="{{ card_collection.featured_image | image_url: width: 1500 }}"
                sizes="
                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: columns }}px,
                  (min-width: 750px) {% if columns > 1 %}calc((100vw - 10rem) / 2){% else %}calc(100vw - 10rem){% endif %},
                  calc(100vw - 3rem)
                "
                alt=""
                height="{{ card_collection.featured_image.height }}"
                width="{{ card_collection.featured_image.width }}"
                loading="lazy"
                class="motion-reduce"
            ></a>
          </div>
        </div>
        {%- else -%}
          {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
        {%- endif -%}

      <div class="color-{{ section.settings.color_scheme_1 }} gradient card-content-1">
        <div class="color-{{ section.settings.color_scheme_1 }} gradient card-information-1">
          <a
            {% if card_collection == blank %}
              role="link" aria-disabled="true"
            {% else %}
              href="{{ card_collection.url }}"
            {% endif %}
            class="full-unstyled-link"
          >
            <h3 class="card-heading-q heading-bold">
              {%- if card_collection.title != blank -%}
                {{- card_collection.title | escape -}}
              {%- else -%}
                {{ 'onboarding.collection_title' | t }}
              {%- endif -%}
            </h3>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

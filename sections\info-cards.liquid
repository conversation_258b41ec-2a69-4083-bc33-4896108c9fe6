{{ 'section-info-cards.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {%- for block in section.blocks -%}
    .infocard__content-{{ block.id }} {
      background: {{ block.settings.infocards_background_color }};
      color: {{ block.settings.infocards_color }};
    }
    .infocard__content-{{ block.id }} .infocard__title,
    .infocard__content-{{ block.id }} .infocard__close {
      color: {{ block.settings.infocards_color }};
    }
  {%- endfor -%}

  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
  <div class="info-cards-section color-{{ section.settings.color_scheme }} gradient margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin section-{{ section.id }}-padding {{ section.settings.cards_style }} {% if section.settings.full_width == true %}page-width{% endif %}">
    <div class="infocards">
      {%- for block in section.blocks -%}
        <infocard-element tabindex="0" role="button" aria-expanded="false" class="infocard js-infocard" {{ block.shopify_attributes }} data-aos="fade-up">
          <div class="infocard__content infocard__content-{{ block.id }}">
            <button class="infocard__close js-close-button" aria-label="{{ 'accessibility.close' | t }}">
              <svg fill="currentColor" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                <path d="m4.59 59.41a2 2 0 0 0 2.83 0l24.58-24.58 24.59 24.58a2 2 0 0 0 2.83-2.83l-24.59-24.58 24.58-24.59a2 2 0 0 0 -2.83-2.83l-24.58 24.59-24.59-24.58a2 2 0 0 0 -2.82 2.82l24.58 24.59-24.58 24.59a2 2 0 0 0 0 2.82z" />
              </svg>
            </button>

            <div class="infocard__header">
              {% if block.settings.hide_image != true %}
                {%- if block.settings.image != blank -%}
                  <div class="infocard-image">
                    <div class="infocard-media">
                      {{
                        block.settings.image
                        | image_url: width: 900
                        | image_tag: loading: 'lazy', class: 'infocard__user-image js-animatable'
                      }}
                    </div>
                  </div>
                {%- else -%}
                  <div class="infocard-image">
                    <div class="infocard-media">
                      {{ 'collection-1' | placeholder_svg_tag: 'placeholder-svg' }}
                    </div>
                  </div>
                {%- endif -%}
              {%- endif -%}

              {%- if block.settings.product != blank -%}
                {{
                  block.settings.product.featured_image
                  | image_url: width: 900
                  | image_tag: loading: 'lazy',class: 'infocard__user-image js-animatable'
                }}
              {%- endif -%}

              <div class="infocard__user-info">
                {%- if block.settings.caption != blank -%}
                    <p class="infocard__subtitle js-animatable">{{ block.settings.caption | escape }}</p>
                {%- endif -%}
                {%- if block.settings.title != blank -%}
                  <h2 class="infocard__title js-animatable">{{ block.settings.title | escape }}</h2>
                {%- endif -%} 
              </div>
            </div>

            
            <div class="infocard__bio js-animatable">{{ block.settings.text }}
               {%- if block.settings.link_label != blank -%}
                <a
                  {% if block.settings.link_label == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.link_label }}"
                  {% endif %}
                  class="button-arrow button button--primary"
                >
                  {{- block.settings.link_label | escape -}}
                  {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
                </a>
              {%- endif -%}
            </div>
        

          </div>
        </infocard-element>
      {%- endfor -%}
    </div>
  </div>
</div>


{% schema %}
{
  "name": "t:sections.infocards.name",
  "class": "section infocards",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.image-with-text.settings.full_width.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.multicolumn.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "cards_style",
      "options": [
        {
          "value": "style-1",
          "label": "t:sections.infocards.settings.cards_style.options__1.label"
        },
        {
          "value": "style-2",
          "label": "t:sections.infocards.settings.cards_style.options__2.label"
        },
        {
          "value": "style-3",
          "label": "t:sections.infocards.settings.cards_style.options__3.label"
        }
      ],
      "default": "style-2",
      "label": "t:sections.infocards.settings.cards_style.label"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
  ],
  "blocks": [
  {
    "type": "infocard",
    "name": "t:sections.infocards.blocks.infocard.name",
    "limit": 4,
    "settings": [
      {
        "type": "image_picker",
        "id": "image",
        "label": "t:sections.infocards.blocks.infocard.settings.image.label"
      },
      {
        "type": "checkbox",
        "id": "hide_image",
        "default": false,
        "label": "t:sections.infocards.blocks.infocard.settings.hide_image.label"
      },
      {
        "type": "product",
        "id": "product",
        "label": "t:sections.testimonials.blocks.column.settings.product.label"
      },
      {
        "type": "text",
        "id": "caption",
        "default": "Caption",
        "label": "t:sections.infocards.blocks.infocard.settings.caption.label"
      },
      {
        "type": "text",
        "id": "title",
        "default": "Info Cards",
        "label": "t:sections.infocards.blocks.infocard.settings.title.label"
      },
      {
        "type": "richtext",
        "id": "text",
        "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
        "label": "t:sections.infocards.blocks.infocard.settings.text.label"
      },
      {
        "type": "text",
        "id": "link_label",
        "label": "t:sections.infocards.blocks.infocard.settings.link_label.label"
      },
      {
        "type": "url",
        "id": "link",
        "label": "t:sections.infocards.blocks.infocard.settings.link.label"
      },
      {
        "type": "color",
        "id": "infocards_background_color",
        "label": "t:sections.infocards.blocks.infocard.settings.infocards_background_color.label",
        "default": "#F2F2F2"
      },
      {
        "type": "color",
        "id": "infocards_color",
        "label": "t:sections.infocards.blocks.infocard.settings.infocards_color.label",
        "default": "#1D1D1D"
      }
    ]
  }
],
 "presets": [
  {
    "name": "Info cards",
    "blocks": [
      {
        "type": "infocard"
      },
      {
        "type": "infocard"
      },
      {
        "type": "infocard"
      },
      {
        "type": "infocard"
      }
    ]
  }
]
}
{% endschema %}

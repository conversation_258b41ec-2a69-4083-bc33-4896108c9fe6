{% comment %}
  Renders facets (filtering and sorting)

  Accepts:
  - results: {Object} Collection or Search object
  - enable_filtering: {<PERSON><PERSON><PERSON>} Show filtering when true
  - enable_sorting: {<PERSON><PERSON><PERSON>} Show sorting when true
  - filter_type: {String} Type of filter
  - product_count: {<PERSON><PERSON><PERSON>} Show count when true

  Usage:
  {% render 'facets', results: collection, enable_filtering: true, enable_sorting: true, filter_type: 'vertical', product_count: false %}
{% endcomment %}

{{ 'component-show-more.css' | asset_url | stylesheet_tag }}
{{ 'component-swatch-input.css' | asset_url | stylesheet_tag }}
{{ 'component-swatch.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign sort_by = results.sort_by | default: results.default_sort_by
  assign total_active_values = 0
  if results.url
    assign results_url = results.url
  else
    assign terms = results.terms | escape
    assign results_url = '?q=' | append: terms | append: '&options%5Bprefix%5D=last&sort_by=' | append: sort_by
  endif
-%}

<div class="facets-container{% if filter_type == 'drawer' %} facets-container-drawer{% endif %} facets-{{ filter_type }} filter-layout-boxed page-width {% if section.settings.enable_switcher == false %} switcher-disabled{% endif %}">
  {%- if filter_type == 'horizontal' and section.settings.enable_switcher == true -%}
    <div class="layout-switcher mobile">
    <ul class="mobile-layout-options">
        <li data-columns="1" class="{% if section.settings.columns_mobile == 1 %}active{% endif %}">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h18v18H0z"></path>
            </svg>
          </button>
        </li>
        <li data-columns="2" class="{% if section.settings.columns_mobile == 2 %}active{% endif %}">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h8v8H0zM0 10h8v8H0zM10 0h8v8h-8zM10 10h8v8h-8z"></path>
            </svg>
          </button>
        </li>
    </ul>
    </div>
  {%- endif -%}
  {%- if filter_type == 'vertical' or filter_type == 'horizontal' -%}
    <facet-filters-form class="facets small-hide">
      <form
        id="FacetFiltersForm"
        class="{% if filter_type == 'horizontal' %}facets__form{% else %}facets__form-vertical{% endif %}"
      >
        {%- if results.terms -%}
          <input type="hidden" name="q" value="{{ results.terms | escape }}">
          <input name="options[prefix]" type="hidden" value="last">
        {%- endif -%}

        {% if enable_filtering %}
          <div
            id="FacetsWrapperDesktop"
            {% if filter_type == 'horizontal' %}
              class="facets__wrapper"
            {% endif %}
          >
            {%- if filter_type == 'horizontal' and results.filters != empty -%}
              <p class="facets__heading caption-large text-body h2" id="verticalTitle" tabindex="-1">
                {{ 'products.facets.filter_by_label' | t }}
              </p>
            {%- endif -%}
            {% comment %} Pills are right below the title for filter type vertical {% endcomment %}
            {%- if filter_type == 'vertical' -%}
              <div class="active-facets active-facets-desktop">
                <div class="active-facets-vertical-filter">
                  <facet-remove class="active-facets__button-wrapper">
                    <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
                      <span>{{ 'products.facets.clear_all' | t }}</span>
                    </a>
                  </facet-remove>
                </div>
                {%- for filter in results.filters -%}
                  {%- for value in filter.active_values -%}
                    <facet-remove>
                      <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
                        <span class="active-facets__button-inner">
                          {{ filter.label }}: {{ value.label | escape }}
                          {% render 'icon-close-small' %}
                          <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                        </span>
                      </a>
                    </facet-remove>
                  {%- endfor -%}
                  {% if filter.type == 'price_range' %}
                    {%- if filter.min_value.value != null or filter.max_value.value != null -%}
                      <facet-remove>
                        <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
                          <span class="active-facets__button-inner">
                            {%- if filter.min_value.value -%}
                              {{ filter.min_value.value | money }}
                            {%- else -%}
                              {{ 0 | money }}
                            {%- endif -%}
                            -
                            {%- if filter.max_value.value -%}
                              {{ filter.max_value.value | money }}
                            {%- else -%}
                              {{ filter.range_max | money }}
                            {%- endif -%}
                            {% render 'icon-close-small' %}
                            <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                          </span>
                        </a>
                      </facet-remove>
                    {%- endif -%}
                  {% endif %}
                {%- endfor -%}
              </div>
            {%- endif -%}
            {% comment %} Sort is the first tabbable element when filter type is vertical {% endcomment %}
            {%- if section.settings.enable_sorting and section.settings.filter_type == 'vertical' -%}
              <facet-filters-form class="facets facets-vertical-sort small-hide no-js-hidden">
                <form class="facets-vertical-form" id="FacetSortForm">
                  <div class="facet-filters sorting caption">
                    <div class="facet-filters__field">
                      <div class="select">
                        {%- assign sort_by = results.sort_by | default: results.default_sort_by -%}
                        <select
                          name="sort_by"
                          class="facet-filters__sort select__select caption-large"
                          id="SortBy"
                          aria-describedby="a11y-refresh-page-message"
                        >
                          {%- for option in results.sort_options -%}
                            <option
                              value="{{ option.value | escape }}"
                              {% if option.value == sort_by %}
                                selected="selected"
                              {% endif %}
                            >
                              {{ option.name | escape }}
                            </option>
                          {%- endfor -%}
                        </select>

                        {% render 'icon-caret' %}
                      </div>
                    </div>
                    <noscript>
                      <button type="submit" class="facets__button-no-js button button--secondary">
                        {{ 'products.facets.sort_button' | t }}
                      </button>
                    </noscript>
                  </div>
                  {% if section.settings.product_count %}
                    <div class="product-count-vertical" role="status">
                      <span class="product-count__text text-body">
                        <span id="ProductCountDesktop">
                          {%- if results.results_count -%}
                            {{
                              'templates.search.results_with_count'
                              | t: terms: results.terms, count: results.results_count
                            }}
                          {%- elsif results.products_count == results.all_products_count -%}
                            {{ 'products.facets.product_count_simple' | t: count: results.products_count }}
                          {%- else -%}
                            {{
                              'products.facets.product_count'
                              | t: product_count: results.products_count, count: results.all_products_count
                            }}
                          {%- endif -%}
                        </span>
                      </span>
                      <div class="loading-overlay-spinner">
                        <svg
                          aria-hidden="true"
                          focusable="false"
                          class="spinner"
                          viewBox="0 0 66 66"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                        </svg>
                      </div>
                    </div>
                  {% endif %}
                </form>
              </facet-filters-form>
            {%- endif -%}

            <script src="{{ 'show-more.js' | asset_url }}" defer="defer"></script>
            {% comment %} Filters for vertical filter {% endcomment %}
            {%- for filter in results.filters -%}
              {% liquid
                assign total_active_values = total_active_values | plus: filter.active_values.size
                assign presentation = filter.presentation | default: default_presentation

                if presentation == 'image'
                  assign show_more_number = 12
                  assign visual_layout_class = 'facets-layout facets-layout-grid facets-layout-grid--' | append: presentation
                else
                  assign show_more_number = 10
                  assign visual_layout_class = 'facets-layout facets-layout-list facets-layout-list--' | append: presentation
                endif
              %}
              {% case filter.type %}
                {% when 'boolean', 'list' %}
                {% if filter_type != 'horizontal' %} 
                <collapsible-row class="product__accordion accordion">
                {% endif %} 
                  <details
                    id="Details-{{ forloop.index }}-{{ section.id }}"
                    class="{% if filter_type == 'horizontal' %}disclosure-has-popup facets__disclosure{% else %} facets__disclosure-vertical{% endif %} js-filter"
                    data-index="{{ forloop.index }}"
                    {% if filter_type == 'vertical' and open_filter == 'first' and forloop.index == 1 %}
                      open
                    {% elsif filter_type == 'vertical' and open_filter == 'all' %}
                      open
                    {% endif %}
                  >
                    <summary
                      class="facets__summary caption-large focus-offset"
                      aria-label="{{ filter.label }} ({{ 'products.facets.filters_selected.one' | t: count: filter.active_values.size }})"
                    >
                      <div>
                        <span>
                          {{- filter.label | escape }}
                          {%- if filter_type == 'vertical' -%}
                            <span class="facets__selected no-js-hidden{% if filter.active_values.size == 0 %} hidden{% endif %}">
                              ({{ filter.active_values.size }})</span
                            >
                          {%- endif -%}
                        </span>
                        {% render 'icon-caret' %}
                      </div>
                    </summary>
                    <div
                      id="Facet-{{ forloop.index }}-{{ section.id }}"
                      class="collapsible__content parent-display {% if filter_type == 'horizontal' %}facets__display{% else %}facets__display-vertical{% endif %}"
                    >
                      {%- if filter_type != 'vertical' -%}
                        <div class="facets__header">
                          <span class="facets__selected no-js-hidden">
                            {{- 'products.facets.filters_selected' | t: count: filter.active_values.size -}}
                          </span>
                          <facet-remove>
                            <a href="{{ filter.url_to_remove }}" class="facets__reset link underlined-link">
                              {{ 'products.facets.reset' | t }}
                            </a>
                          </facet-remove>
                        </div>
                      {%- endif -%}
                      <fieldset class="facets-wrap parent-wrap {% if filter_type == 'vertical' %} facets-wrap-vertical{% endif %}">
                        <legend class="visually-hidden">{{ filter.label | escape }}</legend>
                        {%- liquid
                          assign sorted_values = filter.values
                          # Keep the selected values grouped together when operator is AND
                          if filter.operator == 'AND'
                            assign active_filter_values = filter.values | where: 'active', true
                            assign inactive_filter_values = filter.values | where: 'active', false
                            assign sorted_values = active_filter_values | concat: inactive_filter_values
                          endif
                        -%}
                        <ul
                          class="{%- if presentation == 'image' -%}with-image{% endif %} {{ visual_layout_class }}{% if filter_type == 'vertical' %} facets__list--vertical{% else %} facets__list{% endif %} list-unstyled"
                          role="list"
                        >
                          {%- for value in sorted_values -%}
                            {% liquid
                              assign input_id = 'Filter-' | append: filter.param_name | escape | append: '-' | append: forloop.index
                              assign is_disabled = false
                              if value.count == 0 and value.active == false
                                assign is_disabled = true
                              endif
                            %}
                            {%- capture label_class -%}
                            facets__label facet-checkbox{% if is_disabled %} disabled{% endif %}{% if value.active %} active{% endif %}
                            {%- endcapture -%}

                            {%- capture text_value -%}
                              <span class="facet-checkbox__text" aria-hidden="true">
                                <span class="facet-checkbox__text-label">{{- value.label | escape }}</span> ({{- value.count -}})
                              </span>
                              <span class="visually-hidden">
                                {{- value.label | escape }} (
                                {%- if value.count == 1 -%}
                                  {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                {%- else -%}
                                  {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                {%- endif -%}
                                )
                              </span>
                            {%- endcapture -%}
                            <li class="list-menu__item facets__item{% if forloop.index > show_more_number and filter_type == 'vertical' %} show-more-item hidden{% endif %}">
                              {%- if presentation == 'swatch' -%}
                                <div class="{{ label_class }}">
                                  <div class="swatch-input-wrapper">
                                    {% render 'swatch-input',
                                      id: input_id,
                                      type: 'checkbox',
                                      name: value.param_name,
                                      title: value.label,
                                      value: value.value,
                                      product_form_id: 'FacetFiltersForm',
                                      swatch: value.swatch,
                                      checked: value.active,
                                      disabled: is_disabled
                                    %}
                                  </div>

                                  {{ text_value }}
                                </div>
                              {%- else -%}
                                <label
                                  for="{{ input_id }}"
                                  class="facet-checkbox{% if value.count == 0 and value.active == false %} facet-checkbox--disabled{% endif %}"
                                >
                                  <input
                                    type="checkbox"
                                    name="{{ value.param_name }}"
                                    value="{{ value.value }}"
                                    id="{{ input_id }}"
                                    {% if value.active %}
                                      checked
                                    {% endif %}
                                    {% if is_disabled %}
                                      disabled
                                    {% endif %}
                                  >

                                  {%- if presentation == 'image' -%}
                                    <div class="facets__image-wrapper">
                                      {%- if value.image -%}
                                        {{
                                          value.image
                                          | image_url: width: 300
                                          | image_tag: class: 'facets__image', alt: value.alt
                                        }}
                                      {%- endif -%}
                                    </div>
                                  {%- else -%}
                                    <svg
                                      class="facets-circle"
                                      width="1.6rem"
                                      height="1.6rem"
                                      viewBox="0 0 16 16"
                                      aria-hidden="true"
                                      focusable="false"
                                    >
                                      <circle cx="8" cy="8" r="7.5" stroke="currentColor" fill="none" stroke-width="1"></circle>
                                    </svg>

                                    <svg
                                      aria-hidden="true"
                                      class="icon icon-checkmark"
                                      width="1.1rem"
                                      height="0.7rem"
                                      viewBox="0 0 11 7"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1"
                                        stroke="currentColor"
                                        stroke-width="1.75"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    </svg>
                                  {%- endif -%}

                                  <span aria-hidden="true">{{ value.label | escape }} ({{ value.count }})</span>
                                  <span class="visually-hidden">
                                    {{- value.label | escape }} (
                                    {%- if value.count == 1 -%}
                                      {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                    {%- else -%}
                                      {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                    {%- endif -%}
                                    )</span
                                  >
                                </label>
                              {% endif %}
                            </li>
                          {%- endfor -%}
                        </ul>
                        {% comment %} No show more for no JS {% endcomment %}
                        <ul
                          class="{% if filter_type != 'vertical' %} facets__list{% endif %} no-js-list list-unstyled no-js"
                          role="list"
                        >
                          {%- liquid
                            assign sorted_values = filter.values
                            # Keep the selected values grouped together when operator is AND
                            if filter.operator == 'AND'
                              assign active_filter_values = filter.values | where: 'active', true
                              assign inactive_filter_values = filter.values | where: 'active', false
                              assign sorted_values = active_filter_values | concat: inactive_filter_values
                            endif
                          -%}
                          {%- for value in sorted_values -%}
                            {% liquid
                              assign input_id = 'Filter-' | append: filter.param_name | escape | append: '-' | append: forloop.index
                              assign is_disabled = false
                              if value.count == 0 and value.active == false
                                assign is_disabled = true
                              endif
                            %}
                            {%- capture label_class -%}
                            facets__label facet-checkbox{% if is_disabled %} disabled{% endif %}{% if value.active %} active{% endif %}
                            {%- endcapture -%}

                            {%- capture text_value -%}
                              <span class="facet-checkbox__text" aria-hidden="true">
                                <span class="facet-checkbox__text-label">{{- value.label | escape }}</span> ({{- value.count -}})
                              </span>
                              <span class="visually-hidden">
                                {{- value.label | escape }} (
                                {%- if value.count == 1 -%}
                                  {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                {%- else -%}
                                  {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                {%- endif -%}
                                )
                              </span>
                            {%- endcapture -%}
                            <li class="list-menu__item facets__item">
                              {%- if presentation == 'swatch' -%}
                                <div class="{{ label_class }}">
                                  <div class="swatch-input-wrapper">
                                    {% render 'swatch-input',
                                      id: input_id,
                                      type: 'checkbox',
                                      name: value.param_name,
                                      title: value.label,
                                      value: value.value,
                                      product_form_id: 'FacetFiltersForm',
                                      swatch: value.swatch,
                                      checked: value.active,
                                      disabled: is_disabled
                                    %}
                                  </div>

                                  {{ text_value }}
                                </div>
                              {%- else -%}
                                <label
                                  for="{{ input_id }}"
                                  class="facet-checkbox{% if value.count == 0 and value.active == false %} facet-checkbox--disabled{% endif %}"
                                >
                                  <input
                                    type="checkbox"
                                    name="{{ value.param_name }}"
                                    value="{{ value.value }}"
                                    id="{{ input_id }}"
                                    {% if value.active %}
                                      checked
                                    {% endif %}
                                    {% if is_disabled %}
                                      disabled
                                    {% endif %}
                                  >

                                  {%- if presentation == 'image' -%}
                                    <div class="facets__image-wrapper">
                                      {%- if value.image -%}
                                        {{
                                          value.image
                                          | image_url: width: 300
                                          | image_tag: class: 'facets__image', alt: value.alt
                                        }}
                                      {%- endif -%}
                                    </div>
                                  {%- else -%}
                                    <svg
                                      class="facets-circle"
                                      width="1.6rem"
                                      height="1.6rem"
                                      viewBox="0 0 16 16"
                                      aria-hidden="true"
                                      focusable="false"
                                    >
                                      <circle cx="8" cy="8" r="7.5" stroke="currentColor" fill="none" stroke-width="1"></circle>
                                    </svg>

                                    <svg
                                      aria-hidden="true"
                                      class="icon icon-checkmark"
                                      width="1.1rem"
                                      height="0.7rem"
                                      viewBox="0 0 11 7"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1"
                                        stroke="currentColor"
                                        stroke-width="1.75"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    </svg>
                                  {%- endif -%}

                                  <span aria-hidden="true">{{ value.label | escape }} ({{ value.count }})</span>
                                  <span class="visually-hidden">
                                    {{- value.label | escape }} (
                                    {%- if value.count == 1 -%}
                                      {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                    {%- else -%}
                                      {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                    {%- endif -%}
                                    )</span
                                  >
                                </label>
                              {% endif %}
                            </li>
                          {%- endfor -%}
                        </ul>
                      </fieldset>
                      {%- if filter.values.size > 10 and filter_type == 'vertical' -%}
                        <show-more-button>
                          <button
                            class="button-show-more link underlined-link no-js-hidden"
                            id="Show-More-{{ forloop.index }}-{{ section.id }}"
                            type="button"
                          >
                            <span class="label-show-more label-text"
                              ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t -}}
                            </span>
                            <span class="label-show-less label-text hidden"
                              ><span aria-hidden="true">- </span>{{ 'products.facets.show_less' | t -}}
                            </span>
                          </button>
                        </show-more-button>
                      {%- endif %}
                    </div>
                  </details>
                {% if filter_type != 'horizontal' %}                      
                  </collapsible-row>
                {% endif %} 
                {% when 'price_range' %}
                  {% liquid
                    assign currencies_using_comma_decimals = 'ANG,ARS,BRL,BYN,BYR,CLF,CLP,COP,CRC,CZK,DKK,EUR,HRK,HUF,IDR,ISK,MZN,NOK,PLN,RON,RUB,SEK,TRY,UYU,VES,VND' | split: ','
                    assign uses_comma_decimals = false
                    if currencies_using_comma_decimals contains cart.currency.iso_code
                      assign uses_comma_decimals = true
                    endif
                  %}
                {% if filter_type != 'horizontal' %}                        
                <collapsible-row class="product__accordion accordion">   
                {% endif %}  
                  <details
                    id="Details-{{ forloop.index }}-{{ section.id }}"
                    class="{% if filter_type == 'horizontal' %}disclosure-has-popup facets__disclosure{% else %} facets__disclosure-vertical{% endif %} js-filter"
                    data-index="{{ forloop.index }}"
                    {% if filter_type == 'vertical' and open_filter == 'first' and forloop.index == 1 %}
                      open
                    {% elsif filter_type == 'vertical' and open_filter == 'all' %}
                      open
                    {% endif %}
                  >
                    <summary class="facets__summary caption-large focus-offset">
                      <div>
                        <span>{{ filter.label | escape }}</span>
                        {% render 'icon-caret' %}
                      </div>
                    </summary>
                    <div
                      id="Facet-{{ forloop.index }}-{{ section.id }}"
                      class="collapsible__content {% if filter_type == 'horizontal' %}facets__display{% else %}facets__display-vertical{% endif %}"
                    >
                      <div class="{% if filter_type == 'horizontal' %}facets__header{% else %}facets__header-vertical{% endif %}">
                        {%- assign max_price_amount = filter.range_max | money | strip_html | escape -%}
                        <span class="facets__selected">
                          {{- 'products.facets.max_price' | t: price: max_price_amount -}}
                        </span>
                        {%- if filter_type != 'vertical' -%}
                          <facet-remove>
                            <a href="{{ filter.url_to_remove }}" class="facets__reset link underlined-link">
                              {{ 'products.facets.reset' | t }}
                            </a>
                          </facet-remove>
                        {%- endif -%}
                      </div>
                      <price-range class="facets__price">
                        <span class="field-currency">{{ cart.currency.symbol }}</span>
                        <div class="field">
                          <input
                            class="field-input"
                            name="{{ filter.min_value.param_name }}"
                            id="Filter-{{ filter.label | escape }}-GTE"
                            {%- if filter.min_value.value -%}
                              {%- if uses_comma_decimals -%}
                                value="{{ filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                              {%- else -%}
                                value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"
                              {%- endif %}
                            {%- endif -%}
                            type="number"
                            placeholder="0"
                            min="0"
                            {%- if uses_comma_decimals -%}
                              max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                            {%- else -%}
                              max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                            {% endif %}
                          >
                          <label class="field-label" for="Filter-{{ filter.label | escape }}-GTE">
                            {{- 'products.facets.from' | t -}}
                          </label>
                        </div>
                        {%- if filter_type != 'vertical' -%}
                          <span class="field-currency">{{ cart.currency.symbol }}</span>
                        {%- endif -%}
                        <div class="field">
                          <input
                            class="field-input"
                            name="{{ filter.max_value.param_name }}"
                            id="Filter-{{ filter.label | escape }}-LTE"
                            {%- if filter.max_value.value -%}
                              {%- if uses_comma_decimals -%}
                                value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                              {%- else -%}
                                value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"
                              {%- endif %}
                            {%- endif -%}
                            type="number"
                            min="0"
                            {%- if uses_comma_decimals -%}
                              placeholder="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                              max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                            {%- else -%}
                              placeholder="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                              max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                            {% endif %}
                          >
                          <label class="field-label" for="Filter-{{ filter.label | escape }}-LTE">
                            {{- 'products.facets.to' | t -}}
                          </label>
                        </div>
                      </price-range>
                    </div>
                  </details>
                  {% if filter_type != 'horizontal' %}  
                </collapsible-row>
                {% endif %}  
              {% endcase %}
            {%- endfor -%}
            <noscript>
              <button type="submit" class="facets__button-no-js button button--secondary">
                {{ 'products.facets.filter_button' | t }}
              </button>
            </noscript>
          </div>

          {% comment %} Pills after filtes on filter type horizontal {% endcomment %}
          {%- if filter_type == 'horizontal' -%}
            <div class="active-facets active-facets-desktop">
              {%- for filter in results.filters -%}
                {%- for value in filter.active_values -%}
                  <facet-remove>
                    <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
                      <span class="active-facets__button-inner">
                        {{ filter.label }}: {{ value.label | escape }}
                        {% render 'icon-close-small' %}
                        <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                      </span>
                    </a>
                  </facet-remove>
                {%- endfor -%}
                {% if filter.type == 'price_range' %}
                  {%- if filter.min_value.value != null or filter.max_value.value != null -%}
                    <facet-remove>
                      <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
                        <span class="active-facets__button-inner">
                          {%- if filter.min_value.value -%}
                            {{ filter.min_value.value | money }}
                          {%- else -%}
                            {{ 0 | money }}
                          {%- endif -%}
                          -
                          {%- if filter.max_value.value -%}
                            {{ filter.max_value.value | money }}
                          {%- else -%}
                            {{ filter.range_max | money }}
                          {%- endif -%}
                          {% render 'icon-close-small' %}
                          <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                        </span>
                      </a>
                    </facet-remove>
                  {%- endif -%}
                {% endif %}
              {%- endfor -%}
              <facet-remove class="active-facets__button-wrapper">
                <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
                  <span>{{ 'products.facets.clear_all' | t }}</span>
                </a>
              </facet-remove>
            </div>
          {%- endif -%}
        {% endif %}

        {% if results.current_vendor or results.current_type %}
          <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
        {% endif %}

        {%- if filter_type == 'horizontal' -%}
          {% comment %} Sorting and product count are the last elements when filter type is horizontal {% endcomment %}
          {%- if enable_sorting -%}
            <div class="facet-filters sorting caption">
              <div class="facet-filters__field">
                <h2 class="facet-filters__label caption-large text-body">
                  <label for="SortBy">{{ 'products.facets.sort_by_label' | t }}</label>
                </h2>
                <div class="select">
                  {%- assign sort_by = results.sort_by | default: results.default_sort_by -%}
                  <select
                    name="sort_by"
                    class="facet-filters__sort select__select caption-large"
                    id="SortBy"
                    aria-describedby="a11y-refresh-page-message"
                  >
                    {%- for option in results.sort_options -%}
                      <option
                        value="{{ option.value | escape }}"
                        {% if option.value == sort_by %}
                          selected="selected"
                        {% endif %}
                      >
                        {{ option.name | escape }}
                      </option>
                    {%- endfor -%}
                  </select>
                  {% render 'icon-caret' %}
                </div>
              </div>

              <noscript>
                <button type="submit" class="facets__button-no-js button button--secondary">
                  {{ 'products.facets.sort_button' | t }}
                </button>
              </noscript>
            </div>
          {%- endif -%}

          {% if section.settings.product_count %}
            <div class="product-count light" role="status">
              <h2 class="product-count__text text-body">
                <span id="ProductCountDesktop">
                  {%- if results.results_count -%}
                    {{ 'templates.search.results_with_count' | t: terms: results.terms, count: results.results_count }}
                  {%- elsif results.products_count == results.all_products_count -%}
                    {{ 'products.facets.product_count_simple' | t: count: results.products_count }}
                  {%- else -%}
                    {{
                      'products.facets.product_count'
                      | t: product_count: results.products_count, count: results.all_products_count
                    }}
                  {%- endif -%}
                </span>
              </h2>
              <div class="loading-overlay-spinner">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </div>
          {%- endif -%}
        {%- endif -%}
      </form>
    </facet-filters-form>
    {% comment %} Sorting for vertical filter are grouped with filter when no JS{% endcomment %}
    {%- if enable_sorting and filter_type == 'vertical' -%}
      <facet-filters-form class="small-hide">
        <form class="no-js">
          <div class="facet-filters sorting caption">
            <div class="facet-filters__field">
              <div class="select">
                {%- assign sort_by = results.sort_by | default: results.default_sort_by -%}
                <select
                  name="sort_by"
                  class="facet-filters__sort select__select caption-large"
                  id="SortBy"
                  aria-describedby="a11y-refresh-page-message"
                >
                  {%- for option in results.sort_options -%}
                    <option
                      value="{{ option.value | escape }}"
                      {% if option.value == sort_by %}
                        selected="selected"
                      {% endif %}
                    >
                      {{ option.name | escape }}
                    </option>
                  {%- endfor -%}
                </select>
                {% render 'icon-caret' %}
              </div>
            </div>

            <noscript>
              <button type="submit" class="facets__button-no-js button button--secondary">
                {{ 'products.facets.sort_button' | t }}
              </button>
            </noscript>
          </div>

          {% if results.current_vendor or results.current_type %}
            <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
          {% endif %}

          {%- if results.terms -%}
            <input type="hidden" name="q" value="{{ results.terms | escape }}">
            <input name="options[prefix]" type="hidden" value="last">
          {%- endif -%}
        </form>
      </facet-filters-form>
    {%- endif -%}
  {%- endif -%}

  {% comment %}  Drawer and mobile filter {% endcomment %}
  <menu-drawer
    class="mobile-facets__wrapper{% if filter_type == 'horizontal' or filter_type == 'vertical' %} medium-hide large-up-hide{% endif %}"
    data-breakpoint="mobile"
  >
    <details class="mobile-facets__disclosure disclosure-has-popup">
      <summary class="mobile-facets__open-wrapper focus-offset">
        <span class="mobile-facets__open{% if filter_type == 'drawer' and enable_filtering == false %} medium-hide large-up-hide{% endif %} global-media-settings">
          {% render 'icon-filter' %}
          <span class="mobile-facets__open-label button-label medium-hide large-up-hide">
            {%- if enable_filtering and enable_sorting -%}
              {{ 'products.facets.filter_and_sort' | t }}
            {%- elsif enable_filtering -%}
              {{ 'products.facets.filter_button' | t }}
            {%- elsif enable_sorting -%}
              {{ 'products.facets.sort_button' | t }}
            {%- endif -%}
          </span>
          <span class="mobile-facets__open-label button-label small-hide">
            {%- if enable_filtering -%}
              {{ 'products.facets.filter_button' | t }}
            {%- endif -%}
          </span>
        </span>
        <span tabindex="0" class="mobile-facets__close mobile-facets__close--no-js">{%- render 'icon-close' -%}</span>
      </summary>
      <facet-filters-form>
        <form id="FacetFiltersFormMobile" class="mobile-facets">
          <div id="FilterDrawer" class="mobile-facets__inner">
            <div class="mobile-facets__header" data-aos="fade-up" data-aos-delay="200">
              <div class="mobile-facets__header-inner">
                <p class="mobile-facets__heading medium-hide large-up-hide h2">
                  {%- if enable_filtering and enable_sorting -%}
                    {{ 'products.facets.filter_and_sort' | t }}
                  {%- elsif enable_filtering -%}
                    {{ 'products.facets.filter_button' | t }}
                  {%- elsif enable_sorting -%}
                    {{ 'products.facets.sort_button' | t }}
                  {%- endif -%}
                </p>
                <p class="mobile-facets__heading small-hide h2">
                  {%- if enable_filtering -%}
                    {{ 'products.facets.filter_button' | t }}
                  {%- endif -%}
                </p>
                {% if section.settings.product_count %}
                  <p class="mobile-facets__count heading-bold">
                    {%- if results.results_count -%}
                      {{
                        'templates.search.results_with_count'
                        | t: terms: results.terms, count: results.results_count
                      }}
                    {%- elsif results.products_count == results.all_products_count -%}
                      {{ 'products.facets.product_count_simple' | t: count: results.products_count }}
                    {%- else -%}
                      {{
                        'products.facets.product_count'
                        | t: product_count: results.products_count, count: results.all_products_count
                      }}
                    {%- endif -%}
                  </p>
                {% endif %}
              </div>
            </div>
            <div class="mobile-facets__main">
              {%- if enable_filtering -%}
                {%- for filter in results.filters -%}
                  {% liquid
                    assign presentation = filter.presentation | default: default_presentation
                    if presentation == 'image'
                      assign visual_layout_class = 'facets-layout facets-layout-grid facets-layout-grid--' | append: presentation
                    else
                      assign visual_layout_class = 'facets-layout facets-layout-list facets-layout-list--' | append: presentation
                    endif
                  %}
                  {% case filter.type %}
                    {% when 'boolean', 'list' %}
                    <collapsible-row class="product__accordion accordion">
                      <details
                        id="Details-Mobile-{{ forloop.index }}-{{ section.id }}"
                        class="mobile-facets__details facets__disclosure-vertical js-filter"
                        data-index="mobile-{{ forloop.index }}"
                        {% if forloop.index == 1 %}
                          open
                        {% endif %}
                        data-aos="fade-up" data-aos-delay="300"
                      >
                        <summary
                          class="facets__summary caption-large focus-offset"
                          aria-label="{{ filter.label }} ({{ 'products.facets.filters_selected.one' | t: count: filter.active_values.size }})"
                        >
                          <div>
                            <span>
                              {{- filter.label | escape }}
                              {%- if filter_type == 'vertical' -%}
                                <span class="facets__selected no-js-hidden{% if filter.active_values.size == 0 %} hidden{% endif %}">
                                  ({{ filter.active_values.size }})</span
                                >
                              {%- endif -%}
                            </span>
                            {% render 'icon-caret' %}
                          </div>
                        </summary>
                         <div class="collapsible__content" 
                                 id="CollapsibleAccordion-{{ block.id }}-{{ section.id }}"
                                  role="region"
                                  aria-labelledby="Summary-{{ block.id }}-{{ section.id }}"
                              >
                        <div
                          id="FacetMobile-{{ forloop.index }}-{{ section.id }}"
                          class="parent-display {% if filter_type == 'horizontal' %}facets__display-vertical{% endif %}"
                        >
                          <fieldset class="facets-wrap parent-wrap {% if filter_type == 'vertical' %} facets-wrap-vertical{% endif %}">
                            <legend class="visually-hidden">{{ filter.label | escape }}</legend>
                            <ul
                              class="{{ visual_layout_class }} mobile-facets__list {% if filter_type != 'vertical' %} facets__list{% endif %} list-unstyled no-js-hidden"
                              role="list"
                            >
                              {%- liquid
                                assign sorted_values = filter.values
                                # Keep the selected values grouped together when operator is AND
                                if filter.operator == 'AND'
                                  assign active_filter_values = filter.values | where: 'active', true
                                  assign inactive_filter_values = filter.values | where: 'active', false
                                  assign sorted_values = active_filter_values | concat: inactive_filter_values
                                endif
                              -%}
                              {%- for value in sorted_values -%}
                                {% liquid
                                  assign input_id = 'Filter-' | append: filter.param_name | escape | append: '-mobile-' | append: forloop.index
                                  assign is_disabled = false
                                  if value.count == 0 and value.active == false
                                    assign is_disabled = true
                                  endif
                                %}

                                {%- capture label_class -%}
                              facets__label mobile-facets__label{% if is_disabled %} disabled{% endif %}{% if value.active %} active{% endif %}
                              {%- endcapture -%}

                                {%- capture text_value -%}
                                <span class="facet-checkbox__text" aria-hidden="true">
                                  <span class="facet-checkbox__text-label">{{- value.label | escape }}</span> ({{- value.count -}})
                                </span>
                                <span class="visually-hidden">
                                  {{- value.label | escape }} (
                                  {%- if value.count == '1' -%}
                                    {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                  {%- else -%}
                                    {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                  {%- endif -%}
                                  )
                                </span>
                              {%- endcapture -%}
                                <li class="mobile-facets__item list-menu__item facets__item{% if forloop.index > 10 and filter_type == 'vertical' %} show-more-item hidden{% endif %}">
                                  {% if presentation == 'swatch' %}
                                    <div class="{{ label_class }}">
                                      <div class="swatch-input-wrapper">
                                        {% render 'swatch-input',
                                          id: input_id,
                                          type: 'checkbox',
                                          name: value.param_name,
                                          title: value.label,
                                          value: value.value,
                                          product_form_id: 'FacetFiltersFormMobile',
                                          swatch: value.swatch,
                                          checked: value.active,
                                          disabled: is_disabled
                                        %}
                                      </div>

                                      {{ text_value }}
                                    </div>
                                  {% else %}
                                    <label for="{{ input_id }}" class="{{ label_class }}">
                                      <input
                                        class="mobile-facets__checkbox"
                                        type="checkbox"
                                        name="{{ value.param_name }}"
                                        value="{{ value.value }}"
                                        id="{{ input_id }}"
                                        {% if value.active %}
                                          checked
                                        {% endif %}
                                        {% if is_disabled %}
                                          disabled
                                        {% endif %}
                                      >

                                      {%- if presentation == 'image' -%}
                                        <div class="facets__image-wrapper">
                                          {%- if value.image -%}
                                            {{
                                              value.image
                                              | image_url: width: 300
                                              | image_tag: class: 'facets__image', alt: value.alt
                                            }}
                                          {%- endif -%}
                                        </div>
                                      {%- else -%}
                                        <span class="mobile-facets__highlight"></span>
                                        <svg
                                          class="facets-circle"
                                          width="1.6rem"
                                          height="1.6rem"
                                          viewBox="0 0 16 16"
                                          aria-hidden="true"
                                          focusable="false"
                                        >
                                          <circle cx="8" cy="8" r="7.5" stroke="currentColor" fill="none" stroke-width="1"></circle>
                                        </svg>

                                        <svg
                                          aria-hidden="true"
                                          class="icon icon-checkmark"
                                          width="1.1rem"
                                          height="0.7rem"
                                          viewBox="0 0 11 7"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                      {%- endif %}

                                      {{ text_value }}
                                    </label>
                                  {% endif %}
                                </li>
                              {%- endfor -%}
                            </ul>
                            {% comment %} No show more for no JS {% endcomment %}
                            <ul
                              class="{% if filter_type != 'vertical' %} facets__list{% endif %} no-js-list list-unstyled no-js"
                              role="list"
                            >
                              {%- for value in filter.values -%}
                                <li class="list-menu__item facets__item">
                                  <label
                                    for="Filter-{{ filter.param_name | escape }}-{{ forloop.index }}-no-js"
                                    class="facet-checkbox{% if value.count == 0 and value.active == false %} facet-checkbox--disabled{% endif %}"
                                  >
                                    <input
                                      type="checkbox"
                                      name="{{ value.param_name }}"
                                      value="{{ value.value }}"
                                      id="Filter-{{ filter.param_name | escape }}-{{ forloop.index }}-no-js"
                                      {% if value.active %}
                                        checked
                                      {% endif %}
                                      {% if value.count == 0 and value.active == false %}
                                        disabled
                                      {% endif %}
                                    >

                                    <svg
                                      class="facets-circle"
                                      width="1.6rem"
                                      height="1.6rem"
                                      viewBox="0 0 16 16"
                                      aria-hidden="true"
                                      focusable="false"
                                    >
                                      <circle cx="8" cy="8" r="7.5" stroke="currentColor" fill="none" stroke-width="1"></circle>
                                    </svg>

                                    <svg
                                      aria-hidden="true"
                                      class="icon icon-checkmark"
                                      width="1.1rem"
                                      height="0.7rem"
                                      viewBox="0 0 11 7"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1"
                                        stroke="currentColor"
                                        stroke-width="1.75"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    </svg>

                                    <span aria-hidden="true">{{ value.label | escape }} ({{ value.count }})</span>
                                    <span class="visually-hidden">
                                      {{- value.label | escape }} (
                                      {%- if value.count == '1' -%}
                                        {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                      {%- else -%}
                                        {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                      {%- endif -%}
                                      )</span
                                    >
                                  </label>
                                </li>
                              {%- endfor -%}
                            </ul>
                          </fieldset>
                          {%- if filter.values.size > 10 and filter_type == 'vertical' -%}
                            <show-more-button>
                              <button
                                class="button-show-more link underlined-link no-js-hidden"
                                id="Show-More-{{ forloop.index }}-{{ section.id }}"
                                type="button"
                              >
                                <span class="label-show-more label-text"
                                  ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t -}}
                                </span>
                                <span class="label-show-less label-text hidden"
                                  ><span aria-hidden="true">- </span>{{ 'products.facets.show_less' | t -}}
                                </span>
                              </button>
                            </show-more-button>
                          {%- endif %}
                        </div>                    
                        <div class="facets__header">
                          <facet-remove>
                            <a href="{{ filter.url_to_remove }}" class="facets__reset link underlined-link">
                              {{ 'products.facets.reset' | t }}
                            </a>
                          </facet-remove>
                        </div>
                      </div>
                      </details>
                    </collapsible-row>

                    {% when 'price_range' %}
                      {% liquid
                        assign currencies_using_comma_decimals = 'ANG,ARS,BRL,BYN,BYR,CLF,CLP,COP,CRC,CZK,DKK,EUR,HRK,HUF,IDR,ISK,MZN,NOK,PLN,RON,RUB,SEK,TRY,UYU,VES,VND' | split: ','
                        assign uses_comma_decimals = false
                        if currencies_using_comma_decimals contains cart.currency.iso_code
                          assign uses_comma_decimals = true
                        endif
                      %}  
                     <collapsible-row class="product__accordion accordion">
                      <details
                        id="Details-Mobile-{{ forloop.index }}-{{ section.id }}"
                        class="mobile-facets__details facets__disclosure-vertical js-filter"
                        data-index="mobile-{{ forloop.index }}"
                        {% if filter_type == 'vertical' and forloop.index == 1 %}
                          open
                        {% endif %}
                        data-aos="fade-up" data-aos-delay="300"
                      >
                        <summary class="facets__summary caption-large focus-offset">
                          <div>
                            <span>{{ filter.label | escape }}</span>
                            {% render 'icon-caret' %}
                          </div>
                        </summary>
                        <div class="collapsible__content accordion__content rte" >
                        <div
                          id="FacetMobile-{{ forloop.index }}-{{ section.id }}"
                        >
                          <price-range class="facets__price">
                            <span class="field-currency">{{ cart.currency.symbol }}</span>
                            <div class="field">
                              <input
                                class="field-input"
                                name="{{ filter.min_value.param_name }}"
                                id="Mobile-Filter-{{ filter.label | escape }}-GTE"
                                {%- if filter.min_value.value -%}
                                  {%- if uses_comma_decimals -%}
                                    value="{{ filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                  {%- else -%}
                                    value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"
                                  {%- endif %}
                                {%- endif -%}
                                type="number"
                                placeholder="0"
                                min="0"
                                {%- if uses_comma_decimals -%}
                                  max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                {%- else -%}
                                  max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                                {% endif %}
                              >
                              <label class="field-label" for="Mobile-Filter-{{ filter.label | escape }}-GTE">
                                {{- 'products.facets.from' | t -}}
                              </label>
                            </div>
                            <span class="field-currency">{{ cart.currency.symbol }}</span>
                            <div class="field">
                              <input
                                class="field-input"
                                name="{{ filter.max_value.param_name }}"
                                id="Filter-{{ filter.label | escape }}-LTE"
                                {%- if filter.max_value.value -%}
                                  {%- if uses_comma_decimals -%}
                                    value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                  {%- else -%}
                                    value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"
                                  {%- endif %}
                                {%- endif -%}
                                type="number"
                                min="0"
                                {%- if uses_comma_decimals -%}
                                  placeholder="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                  max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                {%- else -%}
                                  placeholder="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                                  max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                                {% endif %}
                              >
                              <label class="field-label" for="Mobile-Filter-{{ filter.label | escape }}-LTE">
                                {{- 'products.facets.to' | t -}}
                              </label>
                            </div>
                          </price-range>
                        </div>
                        <div class="facets__header">
                          <facet-remove>
                            <a href="{{ filter.url_to_remove }}" class="facets__reset link underlined-link">
                              {{ 'products.facets.reset' | t }}
                            </a>
                          </facet-remove>
                        </div>  
                        </div>
                      </details>
                    </collapsible-row>
                  {% endcase %}
                {%- endfor -%}
              {%- endif -%}

              {%- if enable_sorting -%}
                <div
                  class="mobile-facets__details js-filter{% if filter_type == 'drawer' %} medium-hide large-up-hide{% endif %}"
                  data-index="mobile-{{ forloop.index }}"
                >
                  <div class="mobile-facets__summary" data-aos="fade-up" data-aos-delay="300">
                    <div class="mobile-facets__sort">
                      <label for="SortBy-mobile">{{ 'products.facets.sort_by_label' | t }}</label>
                      <div class="select">
                        <select
                          name="sort_by"
                          class="select__select"
                          id="SortBy-mobile"
                          aria-describedby="a11y-refresh-page-message"
                        >
                          {%- for option in results.sort_options -%}
                            <option
                              value="{{ option.value | escape }}"
                              {% if option.value == sort_by %}
                                selected="selected"
                              {% endif %}
                            >
                              {{ option.name | escape }}
                            </option>
                          {%- endfor -%}
                        </select>
                        {% render 'icon-caret' %}
                      </div>
                    </div>
                  </div>
                </div>
              {%- endif -%}

              <div class="mobile-facets__footer" data-aos="fade-up" data-aos-delay="350">
                <facet-remove class="mobile-facets__clear-wrapper">
                  <a href="{{ results_url }}" class="mobile-facets__clear underlined-link">
                    {{- 'products.facets.clear_all' | t -}}
                  </a>
                </facet-remove>
                <button
                  type="button"
                  class="no-js-hidden button button--primary"
                  onclick="this.closest('.mobile-facets__wrapper').querySelector('summary').click()"
                >
                  {{ 'products.facets.apply' | t }}
                </button>
                <noscript
                  ><button class="button button--primary">{{ 'products.facets.apply' | t }}</button></noscript
                >
              </div>
            </div>

            {% if results.current_vendor or results.current_type %}
              <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
            {% endif %}

            {%- if results.terms -%}
              <input type="hidden" name="q" value="{{ results.terms | escape }}">
              <input name="options[prefix]" type="hidden" value="last">
            {%- endif -%}
          </div>
        </form>
      </facet-filters-form>
    </details>
  </menu-drawer>

  <div class="active-facets active-facets-mobile medium-hide large-up-hide">
    {%- for filter in results.filters -%}
      {%- for value in filter.active_values -%}
        <facet-remove>
          <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
            <span class="active-facets__button-inner">
              {{ filter.label }}: {{ value.label | escape }}
              {% render 'icon-close-small' %}
              <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
            </span>
          </a>
        </facet-remove>
      {%- endfor -%}

      {%- if filter.type == 'price_range' -%}
        {%- if filter.min_value.value != null or filter.max_value.value != null -%}
          <facet-remove>
            <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
              <span class="active-facets__button-inner">
                {%- if filter.min_value.value -%}
                  {{ filter.min_value.value | money }}
                {%- else -%}
                  {{ 0 | money }}
                {%- endif -%}
                -
                {%- if filter.max_value.value -%}
                  {{ filter.max_value.value | money }}
                {%- else -%}
                  {{ filter.range_max | money }}
                {%- endif -%}
                {% render 'icon-close-small' %}
                <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
              </span>
            </a>
          </facet-remove>
        {%- endif -%}
      {%- endif -%}
    {%- endfor -%}
    <facet-remove class="active-facets__button-wrapper">
      <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
        <span>{{ 'products.facets.clear_all' | t }}</span>
      </a>
    </facet-remove>
  </div>
  {% comment %} Sort, product count and filter pills at the end when filter is type of Drawer for the correct tabbing order {% endcomment %}
  {%- if enable_sorting and filter_type == 'drawer' -%}
    <facet-filters-form class="facets small-hide">
      <form id="FacetSortDrawerForm" class="facets__form">
        <div class="facet-filters sorting caption small-hide {% if filter_type == 'drawer' %}horizontal global-media-settings{% endif %}">
          <div class="facet-filters__field">
            <div class="select">
              {%- assign sort_by = results.sort_by | default: results.default_sort_by -%}
              <select
                name="sort_by"
                class="facet-filters__sort select__select caption-large"
                id="SortBy"
                aria-describedby="a11y-refresh-page-message"
              >
                {%- for option in results.sort_options -%}
                  <option
                    value="{{ option.value | escape }}"
                    {% if option.value == sort_by %}
                      selected="selected"
                    {% endif %}
                  >
                    {{ option.name | escape }}
                  </option>
                {%- endfor -%}
              </select>
              {% render 'icon-caret' %}
            </div>
          </div>

          <noscript>
            <button type="submit" class="facets__button-no-js button button--secondary">
              {{ 'products.facets.sort_button' | t }}
            </button>
          </noscript>
        </div>

        {% if results.current_vendor or results.current_type %}
          <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
        {% endif %}

        {%- if results.terms -%}
          <input type="hidden" name="q" value="{{ results.terms | escape }}">
          <input name="options[prefix]" type="hidden" value="last">
        {%- endif -%}
      </form>
    </facet-filters-form>
  {%- endif -%}
  {% if section.settings.product_count %}
    <div
      class="product-count light{% if filter_type == 'vertical' or filter_type == 'horizontal' %} medium-hide large-up-hide{% endif %}"
      role="status"
    >
      <span class="product-count__text text-body">
        <span id="ProductCount">
          {%- if results.results_count -%}
            {{ 'templates.search.results_with_count' | t: terms: results.terms, count: results.results_count }}
          {%- elsif results.products_count == results.all_products_count -%}
            {{ 'products.facets.product_count_simple' | t: count: results.products_count }}
          {%- else -%}
            {{
              'products.facets.product_count'
              | t: product_count: results.products_count, count: results.all_products_count
            }}
          {%- endif -%}
        </span>
      </span>
      <div class="loading-overlay-spinner">
        <svg
          aria-hidden="true"
          focusable="false"
          class="spinner"
          viewBox="0 0 66 66"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
        </svg>
      </div>
    </div>
  {% endif %}

   {%- if filter_type == 'drawer' and section.settings.enable_switcher == true -%}
    <div class="layout-switcher desktop">
      <ul class="layout-options">
        <li data-columns="2" class="">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h8v8H0zM0 10h8v8H0zM10 0h8v8h-8zM10 10h8v8h-8z"></path>
            </svg>
          </button>
        </li>
        <li data-columns="3" class="">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h4v4H0zM0 7h4v4H0zM0 14h4v4H0zM7 0h4v4H7zM7 7h4v4H7zM7 14h4v4H7zM14 0h4v4h-4zM14 7h4v4h-4zM14 14h4v4h-4z"></path>
            </svg>
          </button>
        </li>
        <li data-columns="4" class="">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h4v4H0zM5 0h4v4H5zM10 0h4v4h-4zM15 0h3v4h-3zM0 5h4v4H0zM5 5h4v4H5zM10 5h4v4h-4zM15 5h3v4h-3zM0 10h4v4H0zM5 10h4v4H5zM10 10h4v4h-4zM15 10h3v4h-3zM0 15h4v3H0zM5 15h4v3H5zM10 15h4v3h-4zM15 15h3v3h-3z"></path>
            </svg>
          </button>
        </li>
      </ul>
    </div>

    <div class="layout-switcher mobile">
    <ul class="mobile-layout-options">
        <li data-columns="1" class="{% if section.settings.columns_mobile == 1 %}active{% endif %}">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h18v18H0z"></path>
            </svg>
          </button>
        </li>
        <li data-columns="2" class="{% if section.settings.columns_mobile == 2 %}active{% endif %}">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h8v8H0zM0 10h8v8H0zM10 0h8v8h-8zM10 10h8v8h-8z"></path>
            </svg>
          </button>
        </li>
    </ul>
    </div>
  {%- endif -%} 
                                        
  {%- if filter_type == 'drawer' -%}
    <facet-filters-form class="facets facets-pill small-hide">
      <form id="FacetFiltersPillsForm" class="facets__form">
        <div class="active-facets active-facets-desktop">
          {%- for filter in results.filters -%}
            {%- for value in filter.active_values -%}
              <facet-remove>
                <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
                  <span class="active-facets__button-inner">
                    {{ filter.label }}: {{ value.label | escape }}
                    {% render 'icon-close-small' %}
                    <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                  </span>
                </a>
              </facet-remove>
            {%- endfor -%}

            {%- if filter.type == 'price_range' -%}
              {%- if filter.min_value.value != null or filter.max_value.value != null -%}
                <facet-remove>
                  <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
                    <span class="active-facets__button-inner">
                      {%- if filter.min_value.value -%}
                        {{ filter.min_value.value | money }}
                      {%- else -%}
                        {{ 0 | money }}
                      {%- endif -%}
                      -
                      {%- if filter.max_value.value -%}
                        {{ filter.max_value.value | money }}
                      {%- else -%}
                        {{ filter.range_max | money }}
                      {%- endif -%}
                      {% render 'icon-close-small' %}
                      <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                    </span>
                  </a>
                </facet-remove>
              {%- endif -%}
            {%- endif -%}
          {%- endfor -%}
          <facet-remove class="active-facets__button-wrapper">
            <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
              <span>{{ 'products.facets.clear_all' | t }}</span>
            </a>
          </facet-remove>
        </div>
      </form>
    </facet-filters-form>
  {%- endif -%}  

  {%- if filter_type == 'horizontal' and section.settings.enable_switcher == true -%}
    <div class="layout-switcher desktop">
      <ul class="layout-options">
        <li data-columns="2" class="">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h8v8H0zM0 10h8v8H0zM10 0h8v8h-8zM10 10h8v8h-8z"></path>
            </svg>
          </button>
        </li>
        <li data-columns="3" class="">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h4v4H0zM0 7h4v4H0zM0 14h4v4H0zM7 0h4v4H7zM7 7h4v4H7zM7 14h4v4H7zM14 0h4v4h-4zM14 7h4v4h-4zM14 14h4v4h-4z"></path>
            </svg>
          </button>
        </li>
        <li data-columns="4" class="">
          <button type="button" aria-label="{{ 'accessibility.layout_switcher' | t }}">
            <svg role="presentation" width="18" viewBox="0 0 18 18" fill="none">
              <path fill="currentColor" d="M0 0h4v4H0zM5 0h4v4H5zM10 0h4v4h-4zM15 0h3v4h-3zM0 5h4v4H0zM5 5h4v4H5zM10 5h4v4h-4zM15 5h3v4h-3zM0 10h4v4H0zM5 10h4v4H5zM10 10h4v4h-4zM15 10h3v4h-3zM0 15h4v3H0zM5 15h4v3H5zM10 15h4v3h-4zM15 15h3v3h-3z"></path>
            </svg>
          </button>
        </li>
      </ul>
    </div>
  {%- endif -%}
</div>

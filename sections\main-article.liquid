{{ 'section-blog-post.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<article class="color-{{ section.settings.color_scheme }} gradient section-{{ section.id }}-padding article-template" itemscope itemtype="http://schema.org/BlogPosting" data-aos="fade-up">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when '@app' -%}
          {% render block %}
      
      {%- when 'featured_image' -%}
        {%- if article.image -%}
          <div class="article-template-hero-container page-width" {{ block.shopify_attributes }}>
            <div
              class="article-template-hero-{{ block.settings.image_height }} media"
              itemprop="image"
              {% if block.settings.image_height == 'adapt' and article.image %}
                style="padding-bottom: {{ 1 | divided_by: article.image.aspect_ratio | times: 100 }}%;"
              {% endif %}
            >
              <img
                srcset="
                  {% if article.image.width >= 350 %}{{ article.image | image_url: width: 350 }} 350w,{% endif %}
                  {% if article.image.width >= 750 %}{{ article.image | image_url: width: 750 }} 750w,{% endif %}
                  {% if article.image.width >= 1100 %}{{ article.image | image_url: width: 1100 }} 1100w,{% endif %}
                  {% if article.image.width >= 1500 %}{{ article.image | image_url: width: 1500 }} 1500w,{% endif %}
                  {% if article.image.width >= 2200 %}{{ article.image | image_url: width: 2200 }} 2200w,{% endif %}
                  {% if article.image.width >= 3000 %}{{ article.image | image_url: width: 3000 }} 3000w,{% endif %}
                  {{ article.image | image_url }} {{ article.image.width }}w
                "
                sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px) calc(100vw - 10rem), 100vw"
                src="{{ article.image | image_url: width: 1100 }}"
                loading="eager"
                fetchpriority="high"
                width="{{ article.image.width }}"
                height="{{ article.image.height }}"
                alt="{{ article.image.alt | escape }}"
              >
            </div>
          </div>
        {%- endif -%}

      <div class="article-content color-{{ section.settings.color_scheme }} gradient page-width page-width--narrow">
      {%- when 'title' -%}
        <header {{ block.shopify_attributes }}>
          {%- if block.settings.blog_show_date -%}
            <span class="circle-divider caption-with-letter-spacing" itemprop="dateCreated pubdate datePublished">
              {{- article.published_at | time_tag: format: 'date' -}}
            </span>
          {%- endif -%}
          {%- if block.settings.blog_show_author -%}
            <span class="caption-with-letter-spacing" itemprop="author" itemscope itemtype="http://schema.org/Person">
              <span itemprop="name">{{ article.author }}</span>
            </span>
          {%- endif -%}
          <h1 class="article-template-title heading-bold" itemprop="headline">{{ article.title | escape }}</h1>
        </header>

      {%- when 'share' -%}
        <div class="article-template-social-sharing" {{ block.shopify_attributes }}>
          {% assign share_url = request.origin | append: article.url %}
          {% render 'share-button-article', block: block, share_link: share_url %}
        </div>

      {%- when 'content' -%}
        <div
          class="article-template-content rte"
          itemprop="articleBody"
          {{ block.shopify_attributes }}
        >
          {{ article.content }}
        </div>

      {%- when 'tags' -%}
        <div class="tags" {{ block.shopify_attributes }}>
          {%- for tag in blog.all_tags -%}
            <a class="link" href="{{ blog.url }}/tagged/{{ tag | handle }}">{{ tag }}</a>
            {%- unless forloop.last %} {% endunless %}
          {%- endfor -%}
        </div>

      {%- when 'buttons' -%}
        <div class="previous-next-buttons" {{ block.shopify_attributes }}>
          <a class="link left animate-arrow" href="{{ blog.previous_article }}" class="btn">
            {%- render 'icon-arrow' %} {{- 'blogs.article.previous_post' | t }}</a
          >
          <a class="link right animate-arrow" href="{{ blog.next_article }}" class="btn"
            >{{- 'blogs.article.next_post' | t }} {% render 'icon-arrow' -%}
          </a>
        </div>
      </div>
    {%- endcase -%}
  {%- endfor -%}

  <div class="article-template-back element-margin-top center">
    <a href="{{ blog.url }}" class="article-template-link button button--secondary animate-arrow">
      {% render 'icon-arrow' %}
      {{ 'blogs.article.back_to_blog' | t: title: blog.title }}
    </a>
  </div>
  {%- if blog.comments_enabled? -%}
    <div class="article-template-comment-wrapper">
      <div id="comments" class="page-width page-width--narrow">
        {%- if article.comments_count > 0 -%}
          {%- assign anchorId = '#Comments-' | append: article.id -%}

          <h2 id="Comments-{{ article.id }}" tabindex="-1">
            {{ 'blogs.article.comments' | t: count: article.comments_count }}
          </h2>
          {% paginate article.comments by 5 %}
            <div class="article-template-comments">
              {%- if comment.status == 'pending' and comment.content -%}
                <article id="{{ comment.id }}" class="article-template-comments-comment">
                  {{ comment.content }}
                  <footer class="right">
                    <span class="circle-divider caption-with-letter-spacing">{{ comment.author }}</span>
                  </footer>
                </article>
              {%- endif -%}

              {%- for comment in article.comments -%}
                <article id="{{ comment.id }}" class="article-template-comments-comment">
                  {{ comment.content }}
                  <footer class="right">
                    <span class="circle-divider caption-with-letter-spacing">{{ comment.author }}</span
                    ><span class="caption-with-letter-spacing">
                      {{- comment.created_at | time_tag: format: 'date' -}}
                    </span>
                  </footer>
                </article>
              {%- endfor -%}
              {% render 'pagination', paginate: paginate, anchor: anchorId %}
            </div>
          {% endpaginate %}
        {%- endif -%}
        {% form 'new_comment', article %}
          {%- liquid
            assign post_message = 'blogs.article.success'
            if blog.moderated? and comment.status == 'unapproved'
              assign post_message = 'blogs.article.success_moderated'
            endif
          -%}
          <h2>{{ 'blogs.article.comment_form_title' | t }}</h2>
          {%- if form.errors -%}
            <div class="form-message" role="alert">
              <h3 class="form-status caption-large text-body" tabindex="-1" autofocus>
                {% render 'icon-error' %}
                {{ 'templates.contact.form.error_heading' | t }}
              </h3>
            </div>
            <ul class="form-status-list caption-large">
              {%- for field in form.errors -%}
                <li>
                  <a href="#CommentForm-{{ field }}" class="link">
                    {%- if form.errors.translated_fields[field] contains 'author' -%}
                      {{ 'blogs.article.name' | t }}
                    {%- elsif form.errors.translated_fields[field] contains 'body' -%}
                      {{ 'blogs.article.message' | t }}
                    {%- else -%}
                      {{ form.errors.translated_fields[field] }}
                    {%- endif -%}
                    {{ form.errors.messages[field] }}
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          {%- elsif form.posted_successfully? -%}
            <div class="form-status-list form-message" role="status">
              <h3 class="form-status" tabindex="-1" autofocus>
                {% render 'icon-success' %}
                {{ post_message | t }}
              </h3>
            </div>
          {%- endif -%}

          <div
            {% if blog.moderated? == false %}
              class="article-template-comments-fields"
            {% endif %}
          >
            <div class="article-template-comment-fields">
              <div class="field field--with-error">
                <input
                  type="text"
                  name="comment[author]"
                  id="CommentForm-author"
                  class="field-input"
                  autocomplete="name"
                  value="{{ form.author }}"
                  aria-required="true"
                  required
                  {% if form.errors contains 'author' %}
                    aria-invalid="true"
                    aria-describedby="CommentForm-author-error"
                  {% endif %}
                  placeholder="{{ 'blogs.article.name' | t }}"
                >
                <label class="field-label" for="CommentForm-author">
                  {{- 'blogs.article.name' | t }}
                  <span aria-hidden="true">*</span></label
                >
                {%- if form.errors contains 'author' -%}
                  <small id="CommentForm-author-error">
                    <span class="form-message">
                      {%- render 'icon-error' -%}
                      {{- 'blogs.article.name' | t }}
                      {{ form.errors.messages.author }}.</span
                    >
                  </small>
                {%- endif -%}
              </div>
              <div class="field field--with-error">
                <input
                  type="email"
                  name="comment[email]"
                  id="CommentForm-email"
                  autocomplete="email"
                  class="field-input"
                  value="{{ form.email }}"
                  autocorrect="off"
                  autocapitalize="off"
                  aria-required="true"
                  required
                  {% if form.errors contains 'email' %}
                    aria-invalid="true"
                    aria-describedby="CommentForm-email-error"
                  {% endif %}
                  placeholder="{{ 'blogs.article.email' | t }}"
                >
                <label class="field-label" for="CommentForm-email">
                  {{- 'blogs.article.email' | t }}
                  <span aria-hidden="true">*</span></label
                >
                {%- if form.errors contains 'email' -%}
                  <small id="CommentForm-email-error">
                    <span class="form-message">
                      {%- render 'icon-error' -%}
                      {{- 'blogs.article.email' | t }}
                      {{ form.errors.messages.email }}.</span
                    >
                  </small>
                {%- endif -%}
              </div>
            </div>
            <div class="field field--with-error">
              <textarea
                rows="5"
                name="comment[body]"
                id="CommentForm-body"
                class="text-area field-input"
                aria-required="true"
                required
                {% if form.errors contains 'body' %}
                  aria-invalid="true"
                  aria-describedby="CommentForm-body-error"
                {% endif %}
                placeholder="{{ 'blogs.article.message' | t }}"
              >{{ form.body }}</textarea>
              <label class="form-label field-label" for="CommentForm-body">
                {{- 'blogs.article.message' | t }}
                <span aria-hidden="true">*</span></label
              >
            </div>
            {%- if form.errors contains 'body' -%}
              <small id="CommentForm-body-error">
                <span class="form-message">
                  {%- render 'icon-error' -%}
                  {{- 'blogs.article.message' | t }}
                  {{ form.errors.messages.body }}.</span
                >
              </small>
            {%- endif -%}
          </div>
          {%- if blog.moderated? -%}
            <p class="article-template-comment-warning">
              <span aria-hidden="true">*</span> {{ 'blogs.article.moderated' | t }}
            </p>
          {%- endif -%}
          <input type="submit" class="button" value="{{ 'blogs.article.post' | t }}">
        {% endform %}
      </div>
    </div>
  {%- endif -%}
</article>

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Article",
    "articleBody": {{ article.content | strip_html | json }},
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": {{ request.origin | append: page.url | json }}
    },
    "headline": {{ article.title | json }},
    {% if article.excerpt != blank %}
      "description": {{ article.excerpt | strip_html | json }},
    {% endif %}
    {% if article.image %}
      "image": [
        {{ article | image_url: width: 1920 | prepend: "https:" | json }}
      ],
    {% endif %}
    "datePublished": {{ article.published_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
    "dateCreated": {{ article.created_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
    "author": {
      "@type": "Person",
      "name": {{ article.author | json }}
    },
    "publisher": {
      "@type": "Organization",
      "name": {{ shop.name | json }}
    }
  }
</script>

{% schema %}
{
  "name": "t:sections.main-article.name",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "featured_image",
      "name": "t:sections.main-article.blocks.featured_image.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "image_height",
          "options": [
            {
              "value": "adapt",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__1.label"
            },
            {
              "value": "small",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__2.label"
            },
            {
              "value": "medium",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__3.label"
            },
            {
              "value": "large",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__4.label"
            }
          ],
          "default": "adapt",
          "label": "t:sections.main-article.blocks.featured_image.settings.image_height.label",
          "info": "t:sections.main-article.blocks.featured_image.settings.image_height.info"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-article.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "blog_show_date",
          "default": true,
          "label": "t:sections.main-article.blocks.title.settings.blog_show_date.label"
        },
        {
          "type": "checkbox",
          "id": "blog_show_author",
          "default": false,
          "label": "t:sections.main-article.blocks.title.settings.blog_show_author.label"
        }
      ]
    },
    {
      "type": "content",
      "name": "t:sections.main-article.blocks.content.name",
      "limit": 1
    },
    {
      "type": "tags",
      "name": "t:sections.main-article.blocks.tags.name",
      "limit": 1
    },
    {
      "type": "buttons",
      "name": "t:sections.main-article.blocks.buttons.name",
      "limit": 1
    },
    {
      "type": "share",
      "name": "t:sections.main-article.blocks.share.name",
      "limit": 2,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.main-article.blocks.share.settings.text.label",
          "default": "Share"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-article.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-article.blocks.share.settings.title_info.content"
        }
      ]
    }
  ]
}
{% endschema %}

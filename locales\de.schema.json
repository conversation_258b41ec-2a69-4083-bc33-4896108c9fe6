{"settings_schema": {"global": {"settings": {"header__border": {"content": "<PERSON><PERSON><PERSON>"}, "header__shadow": {"content": "<PERSON><PERSON><PERSON>"}, "header__exclusions": {"content": "Ausschlüsse"}, "global_shadow_opacity": {"label": "Transparenz"}, "global_shadow_blur": {"label": "Unschärfe"}, "global_border_radius": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "show_button_arrow": {"label": "Pfeil im Button anzeigen"}, "button_style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Modern"}, "options__3": {"label": "Elegant"}, "label": "Schaltflächenstil"}, "global_shadow_horizontal_offset": {"label": "Horizontaler Versatz"}, "global_shadow_vertical_offset": {"label": "<PERSON><PERSON><PERSON><PERSON> V<PERSON>z"}, "global_border_thickness": {"label": "<PERSON><PERSON>"}, "global_border_opacity": {"label": "Transparenz"}, "exclude_drawer": {"label": "Schubladen ausschließen (Mobil)"}, "exclude_popup": {"label": "Popups ausschließen"}, "exclude_inputs": {"label": "Eingaben ausschließen"}, "image_padding": {"label": "Bildpolsterung"}, "text_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Textausrichtung"}, "card_icons_size": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Symbolgröße"}}}, "global_design": {"name": "Globales Design"}, "breadcrumbs": {"name": "Brotkrumen", "settings": {"show_breadcrumb_nav": {"label": "Brotkrumennavigation anzeigen"}, "breadcrumbs_style": {"options__1": {"label": "Rand"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Hi<PERSON>grund"}, "label": "Stil"}}}, "animations": {"name": "<PERSON><PERSON>", "settings": {"deactivate_animation": {"label": "Animation be<PERSON> deaktivieren"}, "deactivate_menu_animation": {"label": "Navigationsmenü-Animation deaktivieren"}, "page_scroll_indicator": {"label": "Seitenscroll-Indikator aktivieren"}, "scroll_indicator_color": {"label": "Farbe des Scroll-Indikators"}, "heading__1": {"content": "Seiten-Loader"}, "page_loader_enable": {"label": "Loader aktivieren"}, "loader_background_color": {"label": "Loader-Hintergrundfarbe"}, "loader_text_color": {"label": "Loader-Textfarbe"}, "page_loader_text": {"label": "Seiten-Loader-Text"}, "page_loader_style": {"options__1": {"label": "Immer"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Loader-Text erscheint"}}}, "color_swatches": {"name": "Farb<PERSON><PERSON>", "settings": {"info": {"content": "<PERSON><PERSON><PERSON> [Theme<PERSON>-<PERSON><PERSON><PERSON>](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches) um mehr zu erfahren"}, "swatch_enable": {"label": "Farbmuster aktivieren"}, "color_map": {"label": "Farben:", "info": "Fügen Sie Farbvariantennamen und Farbwerte in diesem Format hinzu: 'Schwarz: #000000'. Eine Farbe pro Zeile. Sie können auch ein Bild hinzufügen, indem Sie den Bild-Handle in diesem Format verwenden: 'Natürlich: natural.png'."}}}, "cards": {"name": "Produktkarten", "settings": {"card_metafield_key": {"label": "Metafeld-Schlüssel der Karte"}, "enable_tooltip": {"label": "Tooltip aktivieren"}}}, "quick_view": {"name": "Schnellansicht", "settings": {"quick_view_product_gallery_width": {"label": "Breite der Produktgalerie"}, "quick_view_height": {"label": "Höhe der Schnellansicht"}}}, "collection_cards": {"name": "Sammlungskarten"}, "blog_cards": {"name": "Blogkarten"}, "badges": {"name": "Abzeichen", "settings": {"position": {"options__1": {"label": "Unten links"}, "options__2": {"label": "Unten rechts"}, "options__3": {"label": "Oben links"}, "options__4": {"label": "<PERSON><PERSON> rechts"}, "label": "Position auf Karten"}, "badge_discount": {"label": "Rabattprozentsatz anzeigen"}, "header": {"content": "Benutzerdefiniertes Abzeichen"}, "info": {"content": "<PERSON><PERSON><PERSON> [Themen-Dokumentation](https://manathemes.com/docs/flux-theme/how-to-guides/product-card-badges) um mehr zu erfahren"}, "custom_badge_text": {"label": "Benutzerdefinierter Abzeichen-Text"}, "custom_badge_tag": {"label": "Benutzerdefinierter Abzeichen-Tag", "info": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> zu verwenden, den <PERSON>e zu den Produkt-Tags hinzugefügt haben."}, "custom_badge_color": {"label": "Textfarbe"}, "custom_badge_background": {"label": "Hintergrundfarbe"}}}, "colors": {"name": "Farbschema", "settings": {"background": {"label": "Hi<PERSON>grund"}, "background_gradient": {"label": "<PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Hintergrundver<PERSON><PERSON> erset<PERSON>t den Hintergrund, wo mö<PERSON>ch."}, "text": {"label": "Text"}, "button_background": {"label": "Einfarbiger Schaltflächenhintergrund"}, "button_label": {"label": "Einfarbige Schaltflächenbeschriftung"}, "secondary_button_label": {"label": "Umrandete Schaltfläche"}, "color_link": {"label": "Linkfarbe"}, "shadow": {"label": "<PERSON><PERSON><PERSON>"}}}, "colors_add": {"name": "Zusätzliche Farben", "settings": {"heading__1": {"content": "Spezielle Hintergründe"}, "background_color_3": {"label": "Hintergrundfarbe des Inhaltsfelds", "info": "Wird als Hintergrundfarbe für Inhaltsfelder innerhalb von Abschnitten verwendet."}, "background_color_2": {"label": "Dekorationshintergrund", "info": "Wird als Dekorationshintergrundfarbe in verschiedenen Abschnitten verwendet."}, "heading__3": {"content": "Akzentfarben"}, "accent_background_color_1": {"label": "Akzenthintergrundfarbe 1", "info": "Wird als primäre Akzenthintergrundfarbe verwendet."}, "accent_color_1": {"label": "Akzenttextfarbe 1", "info": "Wird als primäre Akzenttextfarbe verwendet."}, "accent_color_2": {"label": "Akzenthintergrundfarbe 2", "info": "Wird als sekundäre Akzenthintergrundfarbe verwendet."}, "accent_text_color_2": {"label": "Akzenttextfarbe 2", "info": "Wird als sekundäre Akzenttextfarbe verwendet."}, "heading__4": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "border_color_1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Wird als Rahmenfarbe verwendet."}, "border_color_2": {"label": "Rahmenfarbe 2", "info": "Wird als sekundäre Rahmenfarbe verwendet."}, "heading__5": {"content": "Spezielle Schaltflächen"}, "button_quick_add_background_color": {"label": "Hintergrundfarbe des Schnellhinzufügen-Buttons", "info": "Wird für die Standardposition (eleganter Kollektionstil) verwendet"}, "button_quick_add_text_color": {"label": "Textfarbe des Schnellhinzufügen-Buttons", "info": "Wird für die Standardposition (eleganter Kollektionstil) verwendet"}, "button_quick_add_background_color_hover": {"label": "Hintergrundfarbe des Schnellhinzufügen-Buttons im Hover-Zustand", "info": "Wird für die Standardposition (eleganter Kollektionstil) verwendet"}, "button_quick_add_text_color_hover": {"label": "Textfarbe des Schnellhinzufügen-Buttons im Hover-Zustand", "info": "Wird für die Standardposition (eleganter Kollektionstil) verwendet"}, "heading__6": {"content": "Andere Farben"}, "countdown_background_top": {"label": "Countdown-<PERSON><PERSON><PERSON><PERSON><PERSON> erster"}, "countdown_text_top": {"label": "Countdown-Text erster"}, "countdown_background_bottom": {"label": "Countdown-<PERSON><PERSON>g<PERSON><PERSON> zweiter"}, "countdown_text_bottom": {"label": "Countdown-Text zweiter"}, "opacity_color": {"label": "Globale Opazitätsfarbe"}, "color_link": {"label": "Linkfarbe"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_h1": {"label": "H1 zum Logo und Titel zuweisen"}, "logo_width": {"label": "Breite des Logos für Desktop"}, "logo_width_mobile": {"label": "Breite des mobilen Logos"}, "favicon": {"label": "Favicon-Bild", "info": "Wird auf 32 x 32 Pixel skaliert"}}}, "brand_information": {"name": "Markeninformation", "settings": {"paragraph": {"content": "Fügen Sie eine Markenbeschreibung zum Footer Ihres Geschäfts hinzu."}, "brand_headline": {"label": "Überschrift"}, "brand_description": {"label": "Beschreibung"}, "brand_image": {"label": "Bild"}, "brand_image_width": {"label": "Bildbreite"}}}, "typography": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"type_header_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit Ihres Geschäfts auswirken. [Erfahren Sie mehr über Systemschriftarten.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "type_header_weight": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Schriftstärke"}, "heading_scale": {"label": "Schriftgrößenskala"}, "header__1": {"content": "Überschriften"}, "header__2": {"content": "Text"}, "type_body_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit Ihres Geschäfts auswirken. [Erfahren Sie mehr über Systemschriftarten.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "body_scale": {"label": "Schriftgrößenskala"}, "header__3": {"content": "Navigationsmenü"}, "navigation_font": {"options__1": {"label": "Überschriften"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "text_style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Großbuchstaben"}, "label": "Textstil"}, "navigation_scale": {"label": "Hauptmenü-Schriftgröße"}, "subnavigation_scale": {"label": "Untermenü-Schriftgröße"}}}, "buttons": {"name": "Schaltflächen"}, "variant_pills": {"name": "Variant-<PERSON><PERSON>", "paragraph": "Varianten-Pillen sind eine Möglichkeit, Ihre Produktvarianten anzuzeigen. [Weitere Informationen](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Eingaben"}, "content_containers": {"name": "Inhaltscontainer"}, "popups": {"name": "Dropdowns und Pop-ups", "paragraph": "Betrifft Bereiche wie Navigationsdropdowns, Pop-up-Modalitäten und Warenkorb-Pop-ups."}, "media": {"name": "Medien"}, "drawers": {"name": "Schubladen"}, "styles": {"name": "Symbole", "settings": {"accent_icons": {"options__3": {"label": "Kontur-Schaltfläche"}, "options__4": {"label": "Text"}, "label": "Farbe"}}}, "social-media": {"name": "Soziale Medien", "settings": {"header": {"content": "Soziale Ko<PERSON>n"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}}}, "search_input": {"name": "Suchverhalten", "settings": {"header": {"content": "Suchvorschläge"}, "predictive_search_enabled": {"label": "Suchvorschläge aktivieren"}, "predictive_search_show_vendor": {"label": "Produktanbieter anzeigen", "info": "Sichtbar bei aktivierten Suchvorschlägen."}, "predictive_search_show_price": {"label": "Produktpreis anzeigen", "info": "Sichtbar bei aktivierten Suchvorschlägen."}}}, "currency_format": {"name": "Währungsformat", "settings": {"content": "Währungscodes", "paragraph": "Warenkorb- und Kassenpreise zeigen immer Währungscodes an. Beispiel: $1,00 USD.", "currency_code_enabled": {"label": "Währungscodes anzeigen"}}}, "cart": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "Warenkorb-Typ", "drawer": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "Seite"}, "notification": {"label": "Popup-Benachrichtigung"}}, "cart_icon": {"label": "Warenkorb-Symbol", "bag": {"label": "<PERSON><PERSON>"}, "cart": {"label": "<PERSON><PERSON><PERSON>"}}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_cart_note": {"label": "Warenkorbnotiz aktivieren"}, "cart_note_open": {"label": "Warenkorbnotiz beim <PERSON>"}, "header_shipping": {"content": "Kostenlose Versandnachricht"}, "enable_free_shipping_message": {"label": "Kostenlose Versandnachricht aktivieren"}, "free_shipping_message": {"label": "Nachricht", "info": "Verwenden Sie den Platzhalter *amount*, um die berechnete Zahl anzuzeigen"}, "free_shipping_success": {"label": "Erfolgsmeldung"}, "header_promo": {"content": "Promo-Nachricht"}, "enable_promo_message": {"label": "Promo-Nachricht aktivieren"}, "promo_message": {"label": "Nachricht"}, "header_cross_sell": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "enable_cross_sell": {"label": "Cross-Sell aktivieren"}, "info": {"content": "<PERSON>suchen Sie die [Themen-Dokumentation](https://manathemes.com/docs/flux-theme/flux-theme-settings/cart), um mehr zu erfahren"}, "cross_sell_product": {"label": "Cross-Sell-Sammlung"}, "cross_sell_label": {"label": "Cross-Sell-Titel"}, "header_terms": {"content": "Allgemeine Geschäftsbedingungen"}, "enable_terms": {"label": "AGB-Anforderung aktivieren"}, "terms_label": {"label": "Bezeichnung"}, "header_empty_cart": {"content": "<PERSON><PERSON>"}, "cart_drawer": {"header": "Warenkorb-Schublade", "collection": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>, wenn die Warenkorb-Schublade leer ist."}}, "enable_empty_cart_message": {"label": "<PERSON><PERSON>-Nachricht aktivieren"}, "empty_cart_message": {"label": "<PERSON><PERSON><PERSON>"}, "button_link": {"label": "Schaltflächen-Link"}, "header_buttons": {"content": "Schaltflächen"}, "disable_cart_button": {"label": "Schaltfläche 'Warenkorb anzeigen' deaktivieren"}, "disable_checkout_button": {"label": "Schaltfläche 'Zur Kasse' deaktivieren"}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Seitenbreite"}, "spacing_sections": {"label": "Abstand zwischen Vorlagenabschnitten"}, "header__grid": {"content": "<PERSON><PERSON>"}, "paragraph__grid": {"content": "Betrifft Bereiche mit mehreren Spalten oder Zeilen."}, "spacing_grid_horizontal": {"label": "Horizontaler Abstand"}, "spacing_grid_vertical": {"label": "<PERSON><PERSON><PERSON><PERSON> Abstand"}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Desktop-Layout", "padding_top": "Obere Polsterung", "padding_bottom": "Untere Polsterung", "padding_left": "Linker Innenabstand", "padding_right": "Rechter Innenabstand"}, "section_margin_heading": "Mobiles Layout", "spacing": "Abstand", "margin_spacing": {"options__1": {"label": "-"}, "options__2": {"label": "+"}, "label": "<PERSON>berer Rand"}, "margin_top": "Randwert", "header_color_box": {"content": "<PERSON><PERSON>"}, "colors_box": {"label": "Farbgebung des Inhaltskastens"}, "colors": {"option_1": {"label": "Hintergrund 1 - Text 1"}, "option_2": {"label": "Hintergrund 1 - Text 2"}, "option_3": {"label": "Hintergrund 2 - Text 1"}, "option_4": {"label": "Hintergrund 2 - Text 2"}, "option_5": {"label": "Akzent-Hintergrund 1 - Akzent-Text 1"}, "option_6": {"label": "Akzent-Hintergrund 2 - Akzent-Text 2"}, "label": "Farbschema", "info": "Um das Farbschema zu ändern, aktualisieren Sie Ihre [Designeinstellungen](/editor?context=theme&category=colors).", "has_cards_info": "Um das Farbschema zu ändern, aktualisieren Sie Ihre Designeinstellungen."}, "text_color": {"option_none": {"label": "<PERSON><PERSON> (wie im Farbschema festgelegt)"}, "option_1": {"label": "Textfarbe 1"}, "option_2": {"label": "Textfarbe 2"}, "option_3": {"label": "Akzentfarbe 1"}, "option_4": {"label": "Akzentfarbe 2"}, "label": "Textfarbe", "info": "Hier können Sie die Textfarbe unabhängig vom Farbschema ändern."}, "heading_style": {"label": "Überschriftenstil", "options__1": {"label": "Standard"}, "options__2": {"label": "Großbuchstaben"}}, "heading_size": {"label": "Größe der Überschrift", "options__1": {"label": "Extra groß"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON>"}}, "text_block_size": {"label": "Textgröße", "options__1": {"label": "Extra groß"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON>"}}, "price_size": {"label": "Preise Größe", "options__1": {"label": "Extra groß"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON>"}}, "heading_tag": {"label": "Überschrift-Tag", "info": "Geben Sie Überschriftcode-Typen für SEO und Suchmaschinen zu Kriechzwecken an.", "options__1": {"label": "H1"}, "options__2": {"label": "H2"}, "options__3": {"label": "H3"}, "options__4": {"label": "H4"}, "options__5": {"label": "H5"}, "options__6": {"label": "H6"}}, "text_size": {"label": "Größe der Untertitel", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "text_style": {"label": "Stil der Untertitel", "options__1": {"label": "Standard"}, "options__2": {"label": "Großbuchstaben"}}, "ignore_spacing": {"label": "Abstand zwischen Vorlagenabschnitten ignorieren"}, "disable_arrow_mobile": {"label": "Schaltflächen für den Schieberegler ausblenden"}, "animate_slider": {"label": "Bild anim<PERSON>en"}, "gradient_position": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position des Hintergrundverlaufs"}, "countdown-text": {"label": "Text"}, "countdown-date": {"label": "Datum", "info": "Beispiel für das Format: 30. Sep 2025"}, "countdown-time": {"label": "Uhrzeit", "info": "Beispiel für das Format: 9:00"}, "countdown-date-time-style": {"label": "Countdown-Stil", "options__1": {"label": "Option 1"}, "options__2": {"label": "Option 2"}}, "countdown-text-position": {"label": "Textposition", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Links"}}, "large-countdown": {"label": "Großer Countdown"}, "countdown_finished_message": {"label": "Nachricht bei Countdown-Ende", "info": "<PERSON><PERSON>, wird der Timer ausgeblendet, wenn er 0 erreicht"}, "countdown_timer_tag": {"label": "Nur auf Produkten mit dem Tag 'Timer' anzeigen"}}, "announcement-bar": {"name": "Info/Social-Leiste", "settings": {"header_layout": {"content": "Layout"}, "announcement_position": {"label": "Leistenlayout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal links"}, "options__3": {"label": "Vertikal rechts"}}, "sticky": {"content": "Klebeleiste", "info": "Wenn Sie die Option Klebeleiste wählen, passen Si<PERSON> bitte den oberen Abstand im Kopfbereich entsprechend an."}, "enable_announcement_bar_desktop_sticky": {"label": "Auf Desktop kleben"}, "enable_announcement_bar_mobile_sticky": {"label": "Auf Mobilgerät kleben"}, "header_vertical_bar": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "vertical_position": {"label": "Position"}, "header_top_bar": {"content": "Optionen für die oberste Leiste"}, "text": {"label": "Text"}, "link": {"label": "Link"}, "text_animation": {"label": "Textanimation aktivieren"}, "show_countdown": {"label": "Countdown anzeigen", "info": "Durch Aktivieren des Countdowns wird der Text in den Ankündigungsblöcken ersetzt"}, "countdown": {"content": "Countdown-Timer"}, "show_social_content": {"content": "Soziale Medien"}, "show_social_info": {"info": "Um Ihre Social-Media-Konten anzuzeigen, verknüpfen Si<PERSON> sie in Ihren [Designeinstellungen](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Soziale Medien Symbole anzeigen"}, "country_selector_content": {"content": "Länder-/Regionen-Auswahl"}, "country_selector_info": {"info": "Um ein Land/eine Region hinzuzufügen, gehen Sie zu Ihren [Markteinstellungen](/admin/settings/markets)."}, "enable_country_selector": {"label": "Länder-/Regionen-Auswahl aktivieren"}}, "presets": {"name": "Info/Social-Leiste"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "Bereichsmargen wie im Theme gestalten"}}, "presets": {"name": "Apps"}}, "featured-collections": {"name": "Ausgewählte Kollektionen", "settings": {"featured_collection_1": {"label": "Modern"}, "featured_collection_2": {"label": "Elegant"}, "collection_style": {"label": "Stil", "options__1": {"label": "Elegant"}, "options__2": {"label": "Modern"}, "options__3": {"label": "<PERSON><PERSON>"}}, "layout": {"label": "Desktop-Layout", "options__1": {"label": "Zuerst Kollektion"}, "options__2": {"label": "Zuerst Text"}}, "desktop_content_position": {"label": "Desktop-Inhaltsposition", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}}, "show_text_box": {"label": "Textfeld anzeigen"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}, "info": "Fügen Sie Bilder hinzu, indem Sie Ihre Kollektionen bearbeiten. [Erfahren Si<PERSON> mehr](https://help.shopify.com/manual/products/collections)"}, "desktop_content_alignment": {"label": "Desktop-Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_content_overlap": {"label": "Überlappung hinzufügen"}, "desktop_collections_alignment": {"label": "Desktop-Kollektionen-Ausrichtung", "options__1": {"label": "<PERSON><PERSON>st nach unten"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON> nach unten"}}, "full_width": {"label": "Layout auf volle Breite einstellen"}, "header_mobile": {"content": "Mobiles Layout"}, "layout_mobile": {"label": "Mobiles Layout", "options__1": {"label": "Zuerst Text"}, "options__2": {"label": "Zuerst Kollektion"}}}, "blocks": {"heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "caption": {"name": "Untertitel", "settings": {"heading": {"label": "Untertitel"}, "text": {"label": "Text"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächen-URL"}}}}, "presets": {"name": "Ausgewählte Kollektionen"}}, "subcollections": {"name": "Unterkollektionen", "settings": {"info": {"content": "<PERSON><PERSON><PERSON> [Themen-<PERSON><PERSON><PERSON>](https://manathemes.com/docs/flux-theme/how-to-guides/how-to-set-up-subcollections-on-collection-pages) um mehr zu erfahren"}, "title": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "enable_desktop_slider": {"label": "Desktop-Slider aktivieren"}, "collections_to_show": {"label": "Maximale Anzahl der anzuzeigenden Unterkollektionen"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "subcollection_list_style": {"label": "Stil der Sammlungsliste", "options__1": {"label": "Elegant"}, "options__2": {"label": "Modern"}, "options__3": {"label": "<PERSON><PERSON>"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}, "info": "Fügen Sie Bilder hinzu, indem Sie Ihre Kollektionen bearbeiten. [Erfahren Si<PERSON> mehr](https://help.shopify.com/manual/products/collections)"}, "header_mobile": {"content": "Mobiles Layout"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}}, "presets": {"name": "Unterkollektionen"}}, "collection-list": {"name": "Kollektionsliste", "settings": {"title": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "enable_desktop_slider": {"label": "Desktop-Slider aktivieren"}, "collections_to_show": {"label": "Maximale Anzahl der anzuzeigenden Unterkollektionen"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "collection_list_style": {"label": "Stil der Sammlungsliste", "options__1": {"label": "Elegant"}, "options__2": {"label": "Modern"}, "options__3": {"label": "<PERSON><PERSON>"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}, "info": "Fügen Sie Bilder hinzu, indem Sie Ihre Kollektionen bearbeiten. [Erfahren Si<PERSON> mehr](https://help.shopify.com/manual/products/collections)"}, "header_mobile": {"content": "Mobiles Layout"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}}, "blocks": {"featured_collection": {"name": "Kollektion", "settings": {"collection": {"label": "Kollektion"}}}}, "presets": {"name": "Kollektionsliste"}}, "collection-tabs": {"name": "Sammlungstabs", "settings": {"collection_style": {"label": "Sammlungsstil", "options__1": {"label": "Modern"}, "options__2": {"label": "Elegant"}}, "content_alignment": {"label": "Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "blocks": {"collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "Sammlung auswählen"}, "tab_heading": {"label": "Tab-Überschrift"}}}}, "presets": {"name": "Sammlungstabs"}}, "two-images-text": {"name": "Bilder mit Text", "settings": {"image": {"label": "Bild eins"}, "image_2": {"label": "Bild zwei"}, "layout": {"label": "Desktop-Layout", "options__1": {"label": "Zuerst Bild"}, "options__2": {"label": "Zuerst Text"}}, "header_mobile": {"content": "Mobiles Layout"}, "layout_mobile": {"label": "Mobiles Layout", "options__1": {"label": "Zuerst Bild"}, "options__2": {"label": "Zuerst Text"}}}, "blocks": {"heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "caption": {"name": "Untertitel", "settings": {"heading": {"label": "Untertitel"}, "text": {"label": "Text"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "image": {"name": "Bild", "settings": {"image": {"label": "Bild"}}}, "image_1": {"name": "Bildüberlappung", "settings": {"image_1": {"label": "Bild"}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächen-URL"}}}}, "presets": {"name": "Bilder mit Text"}}, "location-map": {"name": "Standortkarte", "settings": {"info": {"content": "<PERSON><PERSON><PERSON> [Themen-<PERSON><PERSON><PERSON>](https://manathemes.com/docs/flux-theme/how-to-guides/locations-map) um mehr zu erfahren"}, "title": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "content": {"label": "Text", "info": "Inhalt für den Abschnitt Textbereich."}, "header_contact": {"content": "Kontaktformular"}, "show_contact_form": {"label": "Kontaktformular anzeigen"}, "hide_phone_field": {"label": "Telefonfeld ausblenden"}, "api_key": {"label": "Google Maps API-Schlüssel", "info": "Die Aktivierung der Google Maps- und Geocoding-APIs ist für die Funktion der Karte erforderlich."}, "zoom_level": {"label": "Zoomstufe", "info": "Zoomstufe für die Karte (1-18)."}, "header": {"content": "<PERSON><PERSON>"}, "address": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> Sie die Adresse ein, die Si<PERSON> auf der Karte anzeigen möchten."}, "marker_content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Inhalt für das Popup des Markers."}, "layout": {"label": "Layout", "options__1": {"label": "Volle Breite"}, "options__2": {"label": "Eingegrenzt"}, "options__3": {"label": "Inhalt links, Karte rechts"}, "options__4": {"label": "Karte links, Inhalt rechts"}}}, "presets": {"name": "Standortkarte"}}, "countdown": {"name": "Aktion mit Timer", "settings": {"image": {"label": "Bild eins"}, "image_2": {"label": "Bild zwei"}, "layout": {"label": "Desktop-Layout", "options__1": {"label": "Zuerst Bild"}, "options__2": {"label": "Zuerst Text"}}, "desktop_content_position": {"label": "Desktop-Inhaltsposition", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}}, "desktop_content_alignment": {"label": "Desktop-Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}, "header_mobile": {"content": "Mobiles Layout"}, "layout_mobile": {"label": "Mobiles Layout", "options__1": {"label": "Zuerst Bild"}, "options__2": {"label": "Zuerst Text"}}, "mobile_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}}, "blocks": {"heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "caption": {"name": "Untertitel", "settings": {"heading": {"label": "Untertitel"}, "text": {"label": "Text"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "image": {"name": "Bild", "settings": {"image": {"label": "Bild"}}}, "countdown-timer": {"name": "Countdown-Timer"}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächen-URL"}}}}, "presets": {"name": "Aktion mit Timer"}}, "image-gallery": {"name": "Bildergalerie", "settings": {"title": {"label": "Überschrift"}, "caption": {"label": "Unterüberschrift"}, "scroll_height_mobile": {"label": "Scrollhöhe"}}, "blocks": {"text": {"name": "Bild"}}, "presets": {"name": "Bildergalerie"}}, "contact-form": {"name": "Kontaktformular", "settings": {"contact_style": {"label": "Layout", "options__1": {"label": "<PERSON>lt<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_height": {"label": "Bannerhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "image_height_mobile": {"label": "Bannerhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "button_label_1": {"label": "Schaltflächenaufschrift"}, "button_link_1": {"label": "Schaltflächenlink"}, "text": {"label": "Info zur Seitenleiste"}, "header_mobile": {"content": "Mobiles Layout"}, "desktop_enable_gradient": {"label": "Hintergrunddeko aktivieren"}, "mobile_enable_gradient": {"label": "Hintergrunddeko aktivieren"}}, "presets": {"name": "Kontaktformular"}}, "custom-liquid": {"name": "Benutzerdefinierte Liquid-Templates", "settings": {"custom_liquid": {"label": "Benutzerdefinierte Liquid-Templates", "info": "Fügen Sie App-Snippets oder anderen Liquid-Code hinzu, um erweiterte Anpassungen vorzunehmen."}}, "presets": {"name": "Benutzerdefinierte Liquid-Templates"}}, "featured-blog": {"name": "Ausgewählte Blogbeiträge", "settings": {"caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Anzahl der anzuzeigenden Blogbeiträge"}, "columns_desktop": {"label": "Anzahl der Spalten auf Desktop"}, "show_view_all": {"label": "\"Alle anzeigen\"-<PERSON>ton aktivieren, wenn der Blog mehr Beiträge enthält als angezeigt werden"}, "show_image": {"label": "Vorgestelltes Bild anzeigen"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "show_excerpt": {"label": "Auszug anzeigen"}, "blog_style": {"label": "Blog-Stil", "options__1": {"label": "Modern"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Elegant"}, "options__4": {"label": "Cover"}}, "header_mobile": {"content": "Layout für Mobilgeräte"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}}, "presets": {"name": "Ausgewählte Blogbeiträge"}}, "featured-collection": {"name": "Hervorgehobene Sammlung", "settings": {"caption": {"label": "Untertitel"}, "title": {"label": "Überschrift"}, "description": {"label": "Beschreibung"}, "show_description": {"label": "Sammlungsbeschreibung des Admins anzeigen"}, "description_style": {"label": "Beschreibungsstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "collection_style": {"label": "Sammlungsstil", "options__1": {"label": "Modern"}, "options__3": {"label": "Elegant"}}, "products_to_show": {"label": "Maximale anzuzeigende Produkte"}, "columns_desktop": {"label": "Anzahl der Spalten auf Desktop"}, "show_view_all": {"label": "\"Alle anzeigen\" aktivieren, wenn die Sammlung mehr Produkte enthält als angezeigt werden"}, "view_all_style": {"label": "\"Alle anzeigen\"-Stil", "options__1": {"label": "Link"}, "options__2": {"label": "Schaltfläche"}}, "enable_desktop_slider": {"label": "Ka<PERSON>ell auf Desktop aktivieren"}, "full_width": {"label": "Produkte in voller Breite anzeigen"}, "header": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}}, "show_secondary_image": {"label": "Zweites Bild beim Schweben anzeigen"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-A<PERSON> hinzu."}, "enable_quick_buy": {"label": "Schnellansicht aktivieren", "info": "Optimal für Popup- oder Schubladen-Warenkorbtyp."}, "quick_add_position": {"label": "Position des Schnellkauf-Buttons", "options__1": {"label": "Overlay"}, "options__2": {"label": "Standard"}}, "header_overlap": {"content": "Überlappen", "info": "Sie können die ausgewählte Sammlung über dem vorherigen Abschnitt mit einem Überlappungseffekt platzieren. Dadurch werden die Überschrift und die Beschreibung ausgeblendet, um ein saubereres Erscheinungsbild zu erzielen."}, "enable_overlap": {"label": "Überlappung aktivieren"}, "margin_top": {"label": "Überlappung oben"}, "mobile_margin_top": {"label": "Mobile Überlappung"}, "header_mobile": {"content": "Layout für Mobilgeräte"}, "columns_mobile": {"label": "Anzahl der Spalten auf Mobilgeräten", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "disable_quick_add": {"label": "Schnellkauf auf Mobilgeräten deaktivieren, wenn auf Desktop aktiviert"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}, "header_banner_height": {"content": "Bannerhöhe"}, "banner_height_desktop": {"label": "Desktop-Höhe"}, "banner_height_mobile": {"label": "Mobile-Höhe"}}, "presets": {"name": "Hervorgehobene Sammlung"}}, "featured-product": {"name": "Hervorgehobenes Produkt", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Standard"}, "options__3": {"label": "Großbuchstaben"}}}}, "vendor": {"name": "<PERSON><PERSON><PERSON>"}, "title": {"name": "Überschrift"}, "product-meta": {"name": "Inventar und Bewertung"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "countdown-timer": {"name": "Countdown-Timer"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "<PERSON><PERSON>", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Pills"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Kaufen-Schaltflächen", "settings": {"show_dynamic_checkout": {"label": "Dynamische Checkout-Schaltflächen anzeigen", "info": "Mit den auf Ihrem Geschäft verfügbaren Zahlungsmethoden sehen Kunden ihre bevorzugte Option, z. B. PayPal. [Mehr erfahren](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "variant": {"content": "Varianten-Auswahl"}, "swatch_shape": {"label": "Swatch-Stil", "info": "<PERSON><PERSON>e die Dokumentation [Mehr erfahren](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches)", "options__1": {"label": "Kreis"}, "options__2": {"label": "Quadrat"}, "options__3": {"label": "<PERSON><PERSON>"}}}}, "sku": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (SKU)", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "<PERSON><PERSON> <PERSON> einen Link in Social-Media-Beiträgen teilen, wird das vorgestellte Bild der Seite als Vorschaubild angezeigt. [Mehr erfahren](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Geschäftstitel und eine Beschreibung sind mit dem Vorschaubild enthalten. [Mehr erfahren](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}}}, "custom_liquid": {"name": "Benutzerdefinierte Liquid-Vorlage", "settings": {"custom_liquid": {"label": "Benutzerdefinierte Liquid-Vorlage"}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-App hinzu. [Mehr erfahren](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}}, "settings": {"product": {"label": "Produkt"}, "product_background_color": {"label": "Hintergrundfarbe"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "header": {"content": "Medien", "info": "Mehr über [Medientypen](https://help.shopify.com/manual/products/product-media)"}, "media_position": {"label": "Position der Medien auf Desktop", "info": "Die Position wird automatisch für Mobilgeräte optimiert.", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "hide_variants": {"label": "Medien nicht ausgewählter Varianten auf Desktop ausblenden"}, "enable_video_looping": {"label": "Video-Schleife aktivieren"}, "desktop_enable_gradient": {"label": "Dekorativen Hintergrund aktivieren"}, "header_mobile": {"content": "Layout für Mobilgeräte"}, "mobile_enable_gradient": {"label": "Dekorativen Hintergrund aktivieren"}}, "presets": {"name": "Hervorgehobenes Produkt"}}, "footer": {"name": "Footer", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Überschrift"}, "menu": {"label": "<PERSON><PERSON>", "info": "Zeigt nur Hauptmenüpunkte an."}}}, "brand_information": {"name": "Markeninformation", "settings": {"brand_headline": {"label": "Überschrift"}, "brand_description": {"label": "Beschreibung"}, "brand_image": {"label": "Bild"}, "brand_image_width": {"label": "Bildbreite"}, "header__2": {"content": "Newsletter"}, "newsletter_enable": {"label": "E-Mail-Anmeldung anzeigen"}, "header__1": {"content": "Social Media Icons"}, "show_social": {"label": "Social Media Icons anzeigen", "info": "Um Ihre Social-Media-Konten anzuzeigen, verlinken Si<PERSON> diese in Ihren [Design-Einstellungen](/editor?context=theme&category=social%20media)."}, "social_icons_size": {"options__1": {"label": "Extra klein"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Größe der Social-Icons"}}}, "text": {"name": "Text", "settings": {"heading": {"label": "Überschrift"}, "subtext": {"label": "Untertext"}}}, "image": {"name": "Bild", "settings": {"image": {"label": "Bild"}, "image_width": {"label": "Bildbreite"}, "image_headline": {"label": "Überschrift"}, "image_description": {"label": "Beschreibung"}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "button_style_secondary_1": {"label": "Verwende Umriss-Schaltflächenstil"}, "show_text_under": {"label": "Text darunter anzeigen"}}}}, "settings": {"newsletter_enable": {"label": "E-Mail-Anmeldung anzeigen"}, "newsletter_heading": {"label": "Überschrift"}, "header__1": {"content": "E-Mail-Anmeldung", "info": "Abonnenten werden automatisch Ihrer \"Akzeptiert Marketing\"-Kundenliste hinzugefügt. [Mehr erfahren](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Symbole für soziale Medien", "info": "Um Ihre Social-Media-Konten anzuzeigen, verlinken Si<PERSON> diese in Ihren [Themeneinstellungen](/editor?context=theme&category=social%20media)."}, "socials_heading": {"label": "Überschrift"}, "show_social": {"label": "Symbole für soziale Medien anzeigen"}, "header__3": {"content": "Land/Region-Auswahl"}, "header__4": {"info": "Um ein Land/eine Region hinzuzufügen, gehen Sie zu Ihren [Markteinstellungen](/admin/settings/markets)."}, "enable_country_selector": {"label": "Land/Region-Auswahl aktivieren"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__6": {"info": "Um eine Sprache hinzuzufügen, gehen Sie zu Ihren [Spracheinstellungen](/admin/settings/languages)."}, "enable_language_selector": {"label": "Sprachauswahl aktivieren"}, "header__10": {"content": "Powered by Shopify"}, "show_powered_by_link": {"label": "Powered by Shopify anzeigen"}, "header__7": {"content": "Zahlungsmethoden"}, "payment_enable": {"label": "Zahlungssymbole anzeigen"}, "header__8": {"content": "Verlinkungen zu Geschäftsrichtlinien", "info": "Um Geschäftsrichtlinien hinzuzufügen, gehen Si<PERSON> zu <PERSON>hren [Richtlinieneinstellungen](/admin/settings/legal)."}, "show_policy": {"label": "Verlinkungen zu Geschäftsrichtlinien anzeigen"}, "margin_top": {"label": "Desktop-/Mobile-Oberer Rand"}, "margin_bottom": {"label": "<PERSON><PERSON> unterer <PERSON>"}, "centered_content": {"label": "Inhalt auf dem Handy zentrieren"}, "header__9": {"content": "Shop folgen", "info": "Zeigen Sie eine Schaltfläche \"Shop folgen\" für Ihren Shop auf der Shop-App an. [Mehr erfahren](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Shop folgen aktivieren"}, "header_back_to_top_desktop": {"content": "Schaltfläche \"Zurück nach oben\" auf dem Desktop"}, "back_to_top_desktop": {"label": "Schaltfläche \"Zurück nach oben\" aktivieren"}, "back_to_top_bottom": {"label": "Vertikale Position"}, "back_to_top_right": {"label": "Horizontale Position"}, "header_back_to_top_mobile": {"content": "Schaltfläche \"Zurück nach oben\" auf dem Handy"}, "back_to_top_mobile": {"label": "Schaltfläche \"Zurück nach oben\" aktivieren"}, "footer_border": {"label": "Fußzeilenoberrand aktivieren"}, "make_columns_even": {"label": "Spalten gleichmäßig machen"}}}, "header": {"name": "Header", "blocks": {"mega_promotion": {"name": "Mega-Promotion", "settings": {"info": {"content": "<PERSON><PERSON><PERSON> [Themen-<PERSON><PERSON><PERSON>](https://manathemes.com/docs/flux-theme/how-to-guides/mega-menu-promotions) um mehr zu erfahren"}, "mega_promotion_item": {"label": "Menüpunkt", "info": "<PERSON><PERSON><PERSON> Sie den Namen des Mega-Menüpunkts ein, zu dem die Promotionskarte hinzugefügt werden soll."}, "mega_promotion_image": {"label": "Bild"}, "mega_promotion_caption": {"label": "Untertitel"}, "mega_promotion_title": {"label": "Titel"}, "mega_promotion_link_label": {"label": "Link-Beschriftung"}, "mega_promotion_link": {"label": "Link-URL"}}}, "mega_image_menu": {"name": "Mega-Bild-Menü", "settings": {"heading_1": {"content": "Element 1"}, "heading_2": {"content": "Element 2"}, "heading_3": {"content": "Element 3"}, "heading_4": {"content": "Element 4"}, "heading_5": {"content": "Element 5"}, "heading_6": {"content": "Element 6"}, "heading_7": {"content": "Element 7"}, "heading_8": {"content": "Element 8"}}}}, "settings": {"header_layout": {"content": "Header-Layout"}, "header_additional_links": {"content": "Zusätzliche Links"}, "button_label": {"label": "Link Beschriftung 1"}, "button_link": {"label": "Link 1"}, "button_label_one": {"label": "Link Beschriftung 2"}, "button_link_one": {"label": "Link 2"}, "disable_additional_links": {"label": "Zusätzliche Links aktivieren"}, "logo_help": {"content": "Bearbeiten Sie Ihr Logo in [Themeneinstellungen](/editor?context=theme&category=logo)."}, "desktop_header_layout": {"label": "Desktop-Header-Layout", "options__1": {"label": "Zweizeilig"}, "options__2": {"label": "Logo-Menü-Icons"}, "options__3": {"label": "Menü-Logo-Icons"}, "options__4": {"label": "Logo-Zentrum-Menü-Icons"}}, "show_search_icon": {"label": "Alternative Stil", "info": "Diese Einstellung gilt nur, wenn 'Zwei Reihen' ausgewählt ist. Sie ermöglicht die Auswahl eines alternativen Stils für dieses Layout."}, "menu": {"label": "<PERSON><PERSON>"}, "featured_collection_1": {"label": "Kollektion 1"}, "featured_collection_2": {"label": "Kollektion 2"}, "menu_type_desktop": {"label": "Desktop-Menütyp", "info": "Der Menütyp wird automatisch für Mobilgeräte optimiert.", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Mega-Menü"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "all_items_mega": {"label": "Mega-Menü auf alle Menüpunkte anwenden", "info": "Wenn das Mega-Menü ausgewählt ist. Standardmäßig werden nur Menüpunkte mit 3 Ebenen in Mega-Menüs umgewandelt"}, "submenu_animation_position": {"label": "Mega-Menü-Animation", "options__1": {"label": "Standard"}, "options__2": {"label": "Von unten nach oben"}, "options__3": {"label": "<PERSON> links nach rechts"}}, "sticky_header_type": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> nach oben"}, "options__3": {"label": "<PERSON>mmer sichtbar"}, "options__4": {"label": "Immer sichtbar, Logo-Größe reduzieren"}}, "country_selector_content": {"content": "Länder-/Regionen-Auswahl"}, "country_selector_info": {"info": "Um ein Land/eine Region hinzuzufügen, gehen Sie zu Ihren [Markteinstellungen](/admin/settings/markets)."}, "enable_country_selector": {"label": "Länder-/Regionen-Auswahl aktivieren"}, "enable_fixed_header_type": {"label": "Alternativer <PERSON><PERSON> auf der Startseite"}, "enable_fixed_header_type_collection": {"label": "Alternativer Header auf der Kategorieseite"}, "enable_fixed_header_type_all": {"label": "<PERSON>r <PERSON><PERSON> auf allen Se<PERSON>n"}, "enable_fixed_header_transparent": {"label": "Alternativen Header transparent machen"}, "enable_header_full_width": {"label": "Header in voller Breite aktivieren"}, "fixed_header_type_margin": {"label": "Desktop-Margin-Top"}, "fixed_header_type_margin_mobile": {"label": "<PERSON><PERSON>-Top"}, "header_transparent": {"content": "Alternativen Kopfzeile"}, "transparent_menu": {"label": "Benutzerdefinierte Seiten", "info": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Menü, das mit den Seiten verknüpft ist, auf denen Sie die alternative Kopfzeile anwenden möchten."}, "header_sticky": {"content": "Fixierte Kopfzeile"}, "header_highlight": {"content": "Menüelement hervorheben"}, "enable_item_highlight": {"label": "Hervor<PERSON><PERSON><PERSON> von Menüpunkten aktivieren"}, "item_highlight_text": {"label": "Text für Menüpunkthervorhebung"}, "item_highlight_position": {"label": "Position der hervorgehobenen Menüpunkte", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Zweiter Menüpunkt"}, "options__3": {"label": "Dritte<PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Fünfter Menüpunkt"}, "options__6": {"label": "Sechster Menüpunkt"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "<PERSON><PERSON><PERSON> Menüpunkt"}, "options__9": {"label": "Neunter Menüpunkt"}, "options__10": {"label": "<PERSON><PERSON>nter Menüpunkt"}}, "adjust_item_highlight_position": {"label": "Position der Hervorhebung anpassen"}, "item_highlight_background_color": {"label": "Hintergrundfarbe der Hervorhebung"}, "item_highlight_color": {"label": "Textfarbe der Hervorhebung"}, "margin_bottom": {"label": "Untere Marge"}, "header_icons_deco": {"content": "Kopfzeilen-Symboldekoration"}, "header_icons_decoration": {"label": "Symboldekoration", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kreis"}, "options__3": {"label": "<PERSON><PERSON>"}}, "header_mobile": {"content": "Mobiles Layout"}, "mobile_desktop_header_layout": {"label": "Mobiles Header-Layout", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Links"}}}}, "image-banner": {"name": "Bildbanner", "settings": {"image": {"label": "Bild"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung", "info": "Sie können die Deckkraftfarbe global in den [Themeneinstellungen](/editor?context=theme&category=colors) ändern."}, "image_height": {"label": "Bannerhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_content_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON> mittig"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON> mittig"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "Unten mittig"}, "options__9": {"label": "Unten rechts"}, "label": "Position des Desktop-Inhalts"}, "show_text_box": {"label": "Textfeld auf Desktop anzeigen"}, "box_padding_top": {"label": "Obere Textfeld-Polsterung"}, "box_padding_bottom": {"label": "Untere Textfeld-Polsterung"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung des Desktop-Inhalts"}, "show_image_circle": {"label": "Bildkreis ausblenden"}, "ignore_image_circle_animation": {"label": "Animation des Bildkreises deaktivieren"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn der Container angezeigt wird."}, "header": {"content": "Mobiles Layout"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung des mobilen Inhalts"}, "stack_images_on_mobile": {"label": "Bilder auf Mobilgeräten stapeln"}, "show_text_below": {"label": "Container auf Mobilgeräten anzeigen"}, "adapt_height_first_image": {"label": "Abschnittshöhe an Größe des ersten Bilds anpassen", "info": "Überschreibt die Einstellung der Bannerhöhe, wenn aktiviert."}}, "blocks": {"image": {"name": "Kreisbild", "settings": {"image": {"label": "Bild"}}}, "heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift", "info": "Setzen Sie ein wichtiges Wort in Ihrer Überschrift kursiv, um es hervorzuheben, und wählen Sie dann eine Hervorhebungsoption aus den untenstehenden Möglichkeiten."}, "word_animation_color": {"label": "Hervorhebungsfarbe"}, "highlight_option": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> Eins"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__4": {"label": "Kreis Eins"}, "options__5": {"label": "Kreis Zwei"}, "label": "Hervorhebungsoption"}}}, "caption": {"name": "Untertitel", "settings": {"heading": {"label": "Beschriftung"}, "text": {"label": "Text"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "Text"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}, "label": "Textstil"}}}, "countdown-timer": {"name": "Countdown-Timer", "settings": {"countdown_small": {"label": "Kleinen Countdown erstellen"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Beschriftung der ersten Schaltfläche", "info": "<PERSON>sen Sie die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "Link der ersten Schaltfläche"}, "button_style_secondary_1": {"label": "Stil der Rand-Schaltfläche verwenden"}, "button_label_2": {"label": "Beschriftung der zweiten Schaltfläche", "info": "<PERSON>sen Sie die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_2": {"label": "Link der zweiten Schaltfläche"}, "button_style_secondary_2": {"label": "Stil der Rand-Schaltfläche verwenden"}}}}, "presets": {"name": "Bildbanner"}}, "image-banner-with-featured-collection": {"name": "Bildbanner-Kollektion", "settings": {"image": {"label": "Bild"}, "hide_image": {"label": "Einfarbigen Hintergrund verwenden"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung", "info": "Sie können die Deckkraft global über die [Designeinstellungen](/editor?context=theme&category=colors) ändern."}, "content_text_color": {"label": "Textfarbe des Inhalts"}, "content_button_background_color": {"label": "Hintergrundfarbe des Inhalts"}, "content_button_text_color": {"label": "Textfarbe des Inhalts"}, "image_height": {"label": "Bannerhöhe"}, "image_height_mobile": {"label": "Bannerhöhe"}, "full_width_banner": {"label": "Layout auf volle Breite setzen"}, "desktop_content_position": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Inhaltsposition"}, "show_text_box": {"label": "Textfeld auf Desktop anzeigen"}, "box_padding_top": {"label": "Obere Polsterung des Textfelds"}, "box_padding_bottom": {"label": "Untere Polsterung des Textfelds"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Inhaltsausrichtung"}, "header_mobile_image_banner": {"content": "Mobiles Layout - Bildbanner"}, "image_mobile": {"label": "Bild"}, "image_overlay_opacity_mobile": {"label": "Bildüberlagerungs-Deckkraft", "info": "Sie können die Deckkraftfarbe global in den [Theme-Einstellungen](/editor?context=theme&category=colors) ändern."}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}, "stack_images_on_mobile": {"label": "Bilder auf Mobilgeräten stapeln"}, "show_text_below": {"label": "Container auf Mobilgeräten anzeigen"}, "adapt_height_first_image": {"label": "Abschnittshöhe an die Größe des ersten Bildes anpassen", "info": "Überschreibt die Einstellung für die Bannerhöhe, wenn aktiviert."}, "header_featured_collection": {"content": "Vorgestellte Sammlung"}, "enable_collection": {"label": "Vorgestellte Sammlung aktivieren"}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Maximale Anzahl von Produkten zum Anzeigen"}, "columns_desktop": {"label": "Anzahl der Spalten auf Desktop"}, "show_view_all": {"label": "„Alle anzeigen“ aktivieren, wenn die Sammlung mehr Produkte enthält als angezeigt"}, "view_all_style": {"label": "„Alle anzeigen“-Stil", "options__1": {"label": "Link"}, "options__2": {"label": "Schaltfläche"}}, "enable_desktop_slider": {"label": "Ka<PERSON>ell auf Desktop aktivieren"}, "full_width": {"label": "Produkte in voller Breite anzeigen"}, "header": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Zweites Bild beim Überfahren anzeigen"}, "show_vendor": {"label": "Verkäufer anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-A<PERSON> hinzu."}, "enable_quick_buy": {"label": "Schnellansicht aktivieren", "info": "Optimal mit Popup- oder Schubladen-Warenkorbtyp."}, "quick_add_position": {"label": "<PERSON><PERSON>ell hinzufügen Position", "options__1": {"label": "Überlagern"}, "options__2": {"label": "Standard"}}, "header_overlap": {"content": "Überlappen"}, "desktop_margin_top": {"label": "Desktop-Überlappung"}, "mobile_margin_top": {"label": "Mobile Überlappung"}, "header_mobile_featured_collection": {"content": "Mobiles Layout - Vorgestellte Sammlung"}, "columns_mobile": {"label": "Anzahl der Spalten auf Mobilgeräten", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "disable_quick_add": {"label": "<PERSON><PERSON><PERSON> hinzufügen auf Mobilgeräten deaktivieren, wenn auf Desktop aktiviert"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}}, "blocks": {"heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "caption": {"name": "Unterüberschrift", "settings": {"heading": {"label": "Unterüberschrift"}, "text": {"label": "Text"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}, "label": "Textstil"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Beschriftung der ersten Schaltfläche", "info": "<PERSON>sen Sie die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "Link der ersten Schaltfläche"}}}}, "presets": {"name": "Bildbanner-Kollektion"}}, "image-banner-with-collections": {"name": "Bildbanner mit Sammlungsliste", "settings": {"image": {"label": "Bild"}, "hide_image": {"label": "Einfarbigen Hintergrund verwenden"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung", "info": "Sie können die Deckkraftfarbe global in den [Theme-Einstellungen](/editor?context=theme&category=colors) ändern."}, "content_text_color": {"label": "Textfarbe des Inhalts"}, "content_button_background_color": {"label": "Hintergrundfarbe des Inhalts"}, "content_button_text_color": {"label": "Textfarbe des Inhalts"}, "image_height": {"label": "Bannerhöhe"}, "image_height_mobile": {"label": "Bannerhöhe"}, "desktop_content_position": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position des Inhalts auf dem Desktop"}, "show_text_box": {"label": "Textfeld auf dem Desktop anzeigen"}, "box_padding_top": {"label": "Abstand oben im Textfeld"}, "box_padding_bottom": {"label": "Abstand unten im Textfeld"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung des Inhalts auf dem Desktop"}, "header_mobile_image_banner": {"content": "Mobiles Layout"}, "image_mobile": {"label": "Bild"}, "image_overlay_opacity_mobile": {"label": "Deckkraft der Bildüberlagerung", "info": "Sie können die Deckkraftfarbe global in den [Theme-Einstellungen](/editor?context=theme&category=colors) ändern."}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung des Inhalts auf dem Handy"}, "stack_images_on_mobile": {"label": "Bilder auf dem <PERSON>y stapeln"}, "show_text_below": {"label": "Container auf dem Handy anzeigen"}, "adapt_height_first_image": {"label": "Höhe der Sektion an die erste Bildgröße anpassen", "info": "Überschreibt die Bannerhöhe, wenn aktiviert."}, "header_featured_collection": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "enable_collection": {"label": "Empfohlene <PERSON>lung aktivieren"}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Maximale Anzahl anzuzeigender Produkte"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "show_view_all": {"label": "\"Alle anzeigen\" aktivieren, wenn die Sammlung mehr Produkte enthält"}, "view_all_style": {"label": "\"Alle anzeigen\" Stil", "options__1": {"label": "Link"}, "options__2": {"label": "Schaltfläche"}}, "enable_desktop_slider": {"label": "Ka<PERSON>ell auf dem Desktop aktivieren"}, "full_width": {"label": "Produkte in voller Breite anzeigen"}, "header": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}}, "show_secondary_image": {"label": "Zweites Bild beim Hover anzeigen"}, "show_vendor": {"label": "Verkäufer anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-A<PERSON> hinzu."}, "enable_quick_buy": {"label": "Schnellansicht aktivieren", "info": "Optimal mit Popup oder Warenkorb-Typ Schublade."}, "quick_add_position": {"label": "Position der Schnellansicht", "options__1": {"label": "Überlagerung"}, "options__2": {"label": "Standard"}}, "header_overlap": {"content": "Überlappen"}, "desktop_margin_top": {"label": "Überlappung auf dem Desktop"}, "mobile_margin_top": {"label": "Überlappung auf dem Handy"}, "header_mobile_featured_collection": {"content": "Mobiles Layout - <PERSON><PERSON><PERSON><PERSON>"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Handy", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "disable_quick_add": {"label": "Schnellkauf auf dem Handy deaktivieren, wenn auf dem Desktop aktiviert"}, "swipe_on_mobile": {"label": "Wischen auf dem Handy aktivieren"}, "heading": {"label": "Überschrift", "info": "Setzen Sie ein wichtiges Wort kursiv, um es hervorzuheben, und wählen Si<PERSON> dann eine Hervorhebungsoption unten aus."}, "caption": {"label": "Bildunterschrift"}, "text": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}, "label": "Textstil"}, "button_label_1": {"label": "Beschriftung der ersten Schaltfläche", "info": "<PERSON><PERSON> Feld leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "Link der ersten Schaltfläche"}, "highlight_option": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> Eins"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__4": {"label": "Kreis Eins"}, "options__5": {"label": "Kreis Zwei"}, "label": "Hervorhebungsoption"}, "word_animation_color": {"label": "Hervorhebungsfarbe"}, "header_collections": {"content": "Kollektionen"}, "disable_arrow_mobile": {"label": "Slider-Pfeile auf Mobilgeräten ausblenden"}}, "blocks": {"featured_collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Bildbanner mit Sammlungsliste"}}, "banner-two-columns": {"name": "Banner-Spalten", "settings": {"banner_layout": {"label": "Layout", "options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}, "full_width": {"label": "Layout auf volle Breite setzen"}, "accessibility": {"content": "Barrierefreiheit", "label": "Slideshow-Beschreibung", "info": "Beschreiben Sie die Diashow für Kunden, die Bildschirmlesegeräte verwenden."}}, "blocks": {"slide": {"name": "Banner", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "subheading": {"label": "Text"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}, "box_align": {"label": "Position des Desktop-Inhalts", "info": "Position ist automatisch für Mobilgeräte optimiert.", "options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON> mittig"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON> mittig"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "Unten mittig"}, "options__9": {"label": "Unten rechts"}}, "show_text_box": {"label": "Textfeld auf Desktop anzeigen"}, "text_alignment": {"label": "Ausrichtung des Desktop-Inhalts", "option_1": {"label": "Links"}, "option_2": {"label": "<PERSON><PERSON>"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn der Container angezeigt wird."}, "text_alignment_mobile": {"label": "Ausrichtung des mobilen Inhalts", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Banner-Spalten"}}, "sticky-banners": {"name": "Funky banners", "settings": {"banner_height": {"label": "Bildhöhe", "options__1": {"label": "An das erste Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}, "full_width": {"label": "Layout auf volle Breite setzen"}, "accessibility": {"content": "Barrierefreiheit", "label": "Slideshow-Beschreibung", "info": "Beschreiben Sie die Diashow für Kunden, die Bildschirmlesegeräte verwenden."}}, "blocks": {"slide": {"name": "Banner", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "subheading": {"label": "Text"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}, "box_align": {"label": "Position des Desktop-Inhalts", "info": "Position ist automatisch für Mobilgeräte optimiert.", "options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON> mittig"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON> mittig"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "Unten mittig"}, "options__9": {"label": "Unten rechts"}}, "show_text_box": {"label": "Textfeld auf Desktop anzeigen"}, "text_alignment": {"label": "Ausrichtung des Desktop-Inhalts", "option_1": {"label": "Links"}, "option_2": {"label": "<PERSON><PERSON>"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn der Container angezeigt wird."}, "text_alignment_mobile": {"label": "Ausrichtung des mobilen Inhalts", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Sticky banners"}}, "image-with-text": {"name": "Bild mit Text", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Vertikale Beschriftung"}, "height": {"options__1": {"label": "An das Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildhöhe"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildbreite auf Desktop", "info": "Das Bild wird automatisch für Mobilgeräte optimiert."}, "layout": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Text zu<PERSON>t"}, "label": "Platzierung des Bilds auf Desktop", "info": "Bild zuerst ist das Standardlayout auf Mobilgeräten."}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung des Desktop-Inhalts"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Position des Desktop-Inhalts"}, "content_layout": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Überlappung"}, "label": "Layout des Inhalts"}, "desktop_enable_gradient": {"label": "Dekorationshintergrund aktivieren"}, "header_mobile": {"content": "Mobil-Layout"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung des mobilen Inhalts"}, "mobile_enable_gradient": {"label": "Dekorationshintergrund aktivieren"}, "full_width": {"label": "Layout begrenzt machen"}}, "blocks": {"heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "caption": {"name": "Untertitel", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Textgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "text": {"name": "Text", "settings": {"text": {"label": "Inhalt"}, "text_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}}}}, "image": {"name": "Bild", "settings": {"image": {"label": "Bild", "info": "<PERSON>ür das optimale Erscheinungsbild stellen Si<PERSON> bitte sicher, dass die Bildabmessungen 300x300 Pixel betragen."}, "mobile_disable_image": {"label": "Bild auf Mobilgeräten deaktivieren"}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung", "info": "Die Beschriftung leer lassen, um die Schaltfläche auszublenden."}, "button_link": {"label": "Schaltflächenlink"}}}}, "presets": {"name": "Bild mit Text"}}, "image-hotspots": {"name": "Bild-Hotspots", "settings": {"image": {"label": "Bild"}, "image_size": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "label": "Bildgröße"}, "heading": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "text_style": {"label": "Untertitel Textstil", "options__1": {"label": "Standard"}, "options__2": {"label": "Großbuchstaben"}}, "text_size": {"label": "Untertitel Textgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "product": {"label": "Produkt"}, "layout": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Text zu<PERSON>t"}, "label": "Bildplatzierung"}, "layout_mobile": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Text zu<PERSON>t"}, "label": "Bildplatzierung"}, "header_colors": {"content": "<PERSON><PERSON>"}, "tooltip_background_color": {"label": "<PERSON><PERSON><PERSON>"}, "full_width": {"label": "Layout vollständige Breite machen"}, "hide_content": {"label": "Inhalt ausblenden"}}, "blocks": {"tooltip": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "Titel"}, "link": {"label": "Produktlink"}, "product": {"label": "Produkt"}, "content": {"label": "Inhalt"}, "top": {"label": "Obere Position"}, "left": {"label": "Linke Position"}}}}, "presets": {"name": "Bild-Hotspots"}}, "quick-info-bar": {"name": "Schnellinfo-Le<PERSON><PERSON>", "settings": {"first_column_content": {"content": "Inhalt der ersten Spalte"}, "image_1": {"label": "Bild"}, "heading_1": {"label": "Überschrift"}, "caption_1": {"label": "Untertitel"}, "second_column_content": {"content": "Inhalt der zweiten Spalte"}, "image_2": {"label": "Bild"}, "heading_2": {"label": "Überschrift"}, "caption_2": {"label": "Untertitel"}, "third_column_content": {"content": "Inhalt der dritten Spalte"}, "image_3": {"label": "Bild"}, "heading_3": {"label": "Überschrift"}, "caption_3": {"label": "Untertitel"}, "fourth_column_content": {"content": "Inhalt der vierten Spalte"}, "image_4": {"label": "Bild"}, "heading_4": {"label": "Überschrift"}, "caption_4": {"label": "Untertitel"}, "info_bar_options": {"content": "Optionen der Schnellinfo-Leiste"}, "image_size": {"options_1": {"label": "<PERSON>"}, "options_2": {"label": "<PERSON><PERSON><PERSON>"}, "options_3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildgröße"}, "desktop_bar_position": {"options_1": {"label": "<PERSON><PERSON><PERSON>"}, "options_2": {"label": "Links"}, "options_3": {"label": "<PERSON><PERSON>"}, "options_4": {"label": "<PERSON><PERSON> ohne Ü<PERSON>lap<PERSON>ng"}, "label": "Position der Leiste auf dem Desktop"}, "bar_full_width": {"label": "Leiste vollständig in der Breite anzeigen"}, "add_border": {"label": "Rahmen zur Sektion hinzufügen"}, "full_width_background": {"label": "Hintergrund der Sektion auf volle Breite machen"}}, "presets": {"name": "Schnellinfo-Le<PERSON><PERSON>"}}, "multirow": {"name": "Mehrzeilig", "settings": {"image": {"label": "Bild"}, "image_height": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildhöhe"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildbreite auf Desktop", "info": "Das Bild wird automatisch für Mobilgeräte optimiert."}, "button_style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kontur-Button"}, "label": "Button-Stil"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Inhaltsausrichtung"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Desktop-Inhaltsposition", "info": "Die Position wird automatisch für Mobilgeräte optimiert."}, "image_layout": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>s"}, "options__3": {"label": "Links ausgerichtet"}, "options__4": {"label": "Rechts ausgerichtet"}, "label": "Desktop-Bildplatzierung", "info": "Die Platzierung wird automatisch für Mobilgeräte optimiert."}, "ignore_row_spacing": {"label": "Zeilenabstand ignorieren"}, "full_width": {"label": "Layout in voller Breite anzeigen"}, "content_gradient_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON> rechts"}, "options__3": {"label": "Unten links"}, "options__4": {"label": "Unten rechts"}, "label": "Position der Inhaltsdekoration"}, "ignore_content_gradient": {"label": "Inhaltsdekoration ignorieren"}, "ignore_content_overlap": {"label": "Inhaltsüberlappung ignorieren"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}, "header_mobile": {"content": "Mobiles Layout"}}, "blocks": {"row": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "heading_size": {"options__1": {"label": "<PERSON><PERSON> groß"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Überschriftgröße"}, "text": {"label": "Text"}, "button_label": {"label": "Button-Beschriftung"}, "button_link": {"label": "Button-Link"}}}}, "presets": {"name": "Mehrzeilig"}}, "main-account": {"name": "Ko<PERSON>"}, "main-activate-account": {"name": "Kontoaktivierung"}, "main-addresses": {"name": "<PERSON><PERSON><PERSON>"}, "main-article": {"name": "Blogbeitrag", "blocks": {"featured_image": {"name": "Hervorgehobenes Bild", "settings": {"image_height": {"label": "Höhe des hervorgehobenen Bildes", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwenden Sie für beste Ergebnisse ein Bild im Seitenverhältnis 16:9. [Weitere Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Titel", "settings": {"blog_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "blog_show_author": {"label": "<PERSON><PERSON> <PERSON>"}}}, "content": {"name": "Inhalt"}, "tags": {"name": "Tags"}, "buttons": {"name": "Vorheriger/Nächster Beitrag"}, "share": {"name": "Teilen", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "<PERSON><PERSON> einen Link in sozialen Medien posten, wird das vorgestellte Bild der Seite als Vorschau-Bild angezeigt. [Weitere Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Ein Store-Titel und eine Beschreibung werden mit dem Vorschaubild angezeigt. [Weitere Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}}}, "main-blog": {"name": "Blogbeiträge", "settings": {"header": {"content": "Blogbeitragskarte"}, "make_first_post_featured": {"label": "Zeige den ersten Blogbeitrag oben als Highlight an"}, "show_image": {"label": "Hervorgehobenes Bild anzeigen"}, "tags": {"label": "Tags anzeigen"}, "tag_label": {"label": "<PERSON><PERSON>n"}, "tag_default": {"label": "Alle Beiträge"}, "show_page_title": {"label": "Seitentitel anzeigen"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "paragraph": {"content": "Ändern Sie Auszüge, indem Sie Ihre Blogbeiträge bearbeiten. [Weitere Informationen](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "layout": {"label": "Desktop-Layout", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}, "options__3": {"label": "3 Spalten"}, "info": "Beiträge werden auf Mobilgeräten gestapelt."}, "blog_style": {"label": "Blogstil", "options__1": {"label": "Modern"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Elegant"}}, "image_height": {"label": "Hervorgehobene Bildhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwenden Sie für beste Ergebnisse ein Bild im Seitenverhältnis 3:2. [Weitere Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Zwischensumme", "blocks": {"subtotal": {"name": "Zwischensummenpreis"}, "buttons": {"name": "<PERSON><PERSON> geh<PERSON>"}, "text-with-image": {"name": "Text mit Bild", "settings": {"image": {"label": "Bild"}, "image_width": {"label": "Bildbreite"}, "hide_image": {"label": "Bild au<PERSON>"}, "text": {"label": "Text"}, "text_style": {"label": "Text", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Großbuchstaben"}}, "centered_content": {"label": "Inhalt zentrieren"}}}}}, "main-cart-items": {"name": "Artikel", "settings": {"desktop_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}, "header_mobile": {"content": "Mobiles Layout"}, "mobile_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}}}, "main-404": {"name": "404 Seite", "settings": {"heading": {"label": "Überschrift"}, "text": {"label": "Text"}, "image": {"label": "Bild"}}}, "main-collection-banner": {"name": "Hauptkollektionsbanner", "settings": {"paragraph": {"content": "Fügen Sie eine Beschreibung oder ein Bild hinzu, indem Sie Ihre Kollektion bearbeiten. [Erfahren Si<PERSON> mehr](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Kollektion Beschreibung anzeigen"}, "show_breadcrumbs": {"label": "Brotkrumen anzeigen"}, "collection_style": {"label": "Stil", "options__1": {"label": "Zwei Spalten"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Cover"}}, "desktop_content_alignment": {"label": "Ausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "header_style_three": {"content": "Deckstil-Deckkraft"}, "image_height": {"label": "Bannerhöhe", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "show_text_box": {"label": "Textfeld anzeigen"}, "text_box_color": {"options__1": {"label": "Hell"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Hell"}, "label": "Textfeld Farbschema"}, "header_style_one_two": {"content": "Zwei Spalten/Farbiger Hintergrund"}, "banner_background_color": {"label": "Hintergrundfarbe"}, "padding_heading": {"content": "Abstand"}, "all_products_collection_header": {"content": "Alle Produkte Kollektion", "info": "Hier können Sie das Bild und die Überschrift für die Standard-Alle-Produkte-Kollektion festlegen"}, "image": {"label": "Bild"}, "fallback_heading": {"label": "Überschrift"}}, "presets": {"name": "Hauptkollektionsbanner"}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"header__layout": {"content": "Layout"}, "collection_layout": {"label": "Produktraster-Layout", "options__1": {"label": "Modern"}, "options__2": {"label": "Elegant"}}, "product_card_style": {"label": "Stil der Produktkarte", "options__1": {"label": "Standard"}, "options__3": {"label": "Elegant"}}, "add_border": {"label": "<PERSON><PERSON><PERSON> zum Produktbild hinzufügen"}, "products_per_page": {"label": "Produkte pro Seite"}, "pagination": {"label": "Paginierungsstil", "options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON> <PERSON>"}}, "load_button": {"label": "Ladenschaltfläche"}, "columns_desktop": {"label": "Anzahl der Spalten auf Desktop"}, "enable_filtering": {"label": "Filterung aktivieren", "info": "Passen Sie Filter mit der Such- und Entdeckungs-App an. [<PERSON>rf<PERSON><PERSON>hr](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_switcher": {"label": "Schalter aktivieren"}, "filter_type": {"label": "Desktop-Filterlayout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "info": "Die Schublade ist das Standardlayout auf Mobilgeräten."}, "open_filter": {"label": "Vertikaler Filterstil", "options__1": {"label": "<PERSON><PERSON><PERSON> offen"}, "options__2": {"label": "Alle offen"}}, "enable_sorting": {"label": "Sortierung aktivieren"}, "filter_layout": {"label": "Filterlayout", "options__1": {"label": "Eingefasst"}, "options__2": {"label": "Volle Breite"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}}, "show_secondary_image": {"label": "Zweites Bild beim Überfahren anzeigen"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-App hinzu. [Erfahren <PERSON> mehr](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "header__1": {"content": "Filterung und Sortierung"}, "header__3": {"content": "Produktkarte"}, "enable_tags": {"label": "Filterung aktivieren", "info": "Passen Sie Filter mit der Such- und Entdeckungs-App an. [<PERSON>rf<PERSON><PERSON>hr](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_quick_buy": {"label": "Schnellansicht aktivieren", "info": "Optimal bei Popup- oder Schubladen-Warenkorbtyp."}, "enable_quick_add": {"label": "Schnelles Hinzufügen zum Warenkorb aktivieren"}, "list_color_variants_in_collection": {"label": "Produktfarbvarianten in der Kollektion anzeigen", "info": "<PERSON><PERSON><PERSON>, dass Sie für alle Ihre Produktvarianten ausgewählte Bilder festgelegt haben"}, "list_size_variants_in_collection": {"label": "Produktgrößenvarianten in der Kollektion anzeigen"}, "hide_unavailable": {"label": "Nicht verfügbare und ausverkaufte Produkte ausblenden"}, "quick_add_position": {"label": "Position für Schnellkauf", "options__1": {"label": "Overlay"}, "options__2": {"label": "Standard"}}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf Mobilgeräten", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "disable_quick_add": {"label": "Schnellkauf auf Mobilgeräten deaktivieren, wenn auf Desktop aktiviert"}, "product_count": {"label": "Produktanzahl aktivieren", "info": "Die Zahl beinhaltet keine Produktvarianten"}}, "blocks": {"promo_row": {"name": "Promo-Reihe", "settings": {"text": {"label": "Promo nach Produkt platzieren:", "info": "<PERSON><PERSON><PERSON> eine ganze <PERSON> ein"}, "image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "subheading": {"label": "Text"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}, "banner_height": {"label": "Bildhöhe", "options__1": {"label": "An erstem Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "banner_layout": {"label": "Banner-Layout", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "show_text_box": {"label": "Textfeld anzeigen"}, "image_overlay_opacity": {"label": "Deckkraft des Bildüberzugs"}}}}}, "main-list-collections": {"name": "Sammlungsseitenliste", "settings": {"title": {"label": "Überschrift"}, "sort": {"label": "Sammlungen sortieren nach:", "options__1": {"label": "Alphabetisch, A-Z"}, "options__2": {"label": "Alphabetisch, Z-A"}, "options__3": {"label": "<PERSON><PERSON>, <PERSON><PERSON>"}, "options__4": {"label": "Da<PERSON>, Alt zu Neu"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> zu Niedrig"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}, "info": "<PERSON>ügen Sie Bilder hinz<PERSON>, indem Sie Ihre Sammlungen bearbeiten. [Erfahren Si<PERSON> mehr](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Anzahl der Spalten auf Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf Mobilgeräten", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "main-login": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-order": {"name": "Bestellung"}, "main-page": {"name": "Seite"}, "main-password-footer": {"name": "Fußzeile für Passwort"}, "main-password-header": {"name": "Kopfzeile für Passwort", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Bearbeiten Sie Ihr Logo in den Theme-Einstellungen."}}}, "main-product": {"name": "Produktinformationen", "blocks": {"spacer": {"name": "Trennzeichen", "settings": {"margin_top": {"label": "<PERSON>berer Rand"}}}, "tabs": {"name": "Tabs", "settings": {"centered_tabs": {"label": "Tab-Navigation zentrieren"}, "remove_border_tabs": {"label": "Untere Grenze der Tab-Navigation ausblenden"}, "header_item_1": {"content": "Element 1"}, "header_item_2": {"content": "Element 2"}, "header_item_3": {"content": "Element 3"}, "heading_1": {"label": "Überschrift"}, "heading_2": {"label": "Überschrift"}, "heading_3": {"label": "Überschrift"}, "row_content_1": {"label": "Inhalt"}, "row_content_2": {"label": "Inhalt"}, "row_content_3": {"label": "Inhalt"}}}, "payment_enable": {"name": "Zahlungssymbole", "settings": {"header": {"content": "Auf Mobilgerät / Desktop ausblenden", "info": "Anwendungsfall: Unterschiedliche Positionierung für Desktop und Mobilgerät. Beispiel: Positionieren Sie diesen Block auf dem Desktop und blenden Sie ihn auf Mobilgeräten aus. Verwenden Sie dann eine weitere Instanz desselben Blocks, um ihn auf Mobilgeräten zu positionieren und auf dem Desktop auszublenden."}, "hide_mobile": {"label": "Block auf Mobilgeräten ausblenden"}, "hide_desktop": {"label": "Block auf dem Desktop ausblenden"}, "header_quick_view": {"content": "Schnellansicht-Modalfenster", "info": "<PERSON><PERSON><PERSON><PERSON>, ob der Block in der Schnellansicht angezeigt oder ausgeblendet werden soll"}, "hide_quick_view": {"label": "Block in der Schnellansicht ausblenden"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "show_text_background": {"label": "Textabstand"}}}, "image": {"name": "Bild", "settings": {"image": {"label": "Bild"}, "image_link": {"label": "Link"}, "text_1": {"label": "Text"}, "text_1_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "vendor": {"name": "<PERSON><PERSON><PERSON>"}, "title": {"name": "Titel", "settings": {"column": {"label": "Position", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Unter Galerie"}}, "margin_top": {"label": "<PERSON>berer Rand"}}}, "price": {"name": "Pre<PERSON>", "settings": {"text": {"label": "Text"}}}, "waiting_list": {"name": "Wartelist<PERSON>", "settings": {"paragraph": {"content": "Wartelisten-Anmeldung für nicht vorrätige Produkte"}, "waiting_list_title": {"label": "Titel"}, "waiting_list_tagline": {"label": "<PERSON><PERSON><PERSON>"}, "waiting_list_notice": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "waiting_list_button": {"label": "Schaltfläche"}}}, "product-meta": {"name": "Inventar und Bewertung", "settings": {"header_inventory": {"content": "Bestand"}, "show_product_inventory": {"label": "Bestand anzeigen"}, "text_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__3": {"label": "Großbuchstaben"}}, "inventory_threshold": {"label": "Geringer Bestands-Schwellenwert", "info": "Wählen Sie 0, um immer auf Lager anzuzeigen, wenn verfügbar."}, "show_inventory_quantity": {"label": "Bestandsmenge anzeigen"}, "header_sku": {"content": "SKU"}, "show_product_sku": {"label": "SKU anzeigen"}, "header_rating": {"content": "Bewertung"}, "info": {"content": "<PERSON><PERSON><PERSON> [Themen-Doku<PERSON>](https://manathemes.com/docs/flux-theme/how-to-guides/product-ratings-and-reviews) um mehr zu erfahren"}, "show_product_rating": {"label": "Bewertung anzeigen"}}}, "dynamic_card_icons": {"name": "Dynamische Symbole", "settings": {"info": {"content": "<PERSON><PERSON><PERSON> [Theme<PERSON>-<PERSON><PERSON><PERSON>](https://manathemes.com/docs/flux-theme/how-to-guides/dynamic-icons-with-text) um mehr zu erfahren"}, "card_metafield_text": {"label": "Text"}, "card_metafield_key": {"label": "Metafeldschlüssel der Karte"}, "card_metafield_image_size": {"label": "Bildgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Extra groß"}}, "card_metafield_layout": {"label": "Layout", "options__1": {"label": "Breit"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Inline"}}, "card_metafield_icon_title_font_weight": {"label": "Schriftgewicht des Symboltitels", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Normal"}}, "card_metafield_border": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "card_metafield_enable_border_radius": {"label": "Rahmenradius aktivieren - Kreis"}, "icons_tooltip": {"label": "Symboltitel als Tooltip anzeigen"}}}, "inventory": {"name": "Lagerbestands<PERSON><PERSON>", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "inventory_threshold": {"label": "Schwelle für niedrigen Lagerbestand", "info": "Wählen Sie 0, um immer als vorrätig anzuzeigen, wenn verfügbar."}, "show_inventory_quantity": {"label": "Bestandsanzahl anzeigen"}}}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "<PERSON><PERSON>", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Pills"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "countdown-timer": {"name": "Countdown-Timer"}, "buy_buttons": {"name": "Kaufschaltflächen", "settings": {"show_dynamic_checkout": {"label": "Dynamische Checkout-Schaltflächen anzeigen", "info": "Mit den auf Ihrem Shop verfügbaren Zahlungsmethoden sehen Kunden ihre bevorzugte Option, wie PayPal. [Weitere Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Formular für Empfängerinformationen von Geschenkkarten anzeigen", "info": "Ermöglicht es Käufern, Geschenkkarten an einem festgelegten Datum zusammen mit einer persönlichen Nachricht zu senden. [Erfahren <PERSON> mehr](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}, "hide_unavailable": {"label": "Nicht verfügbare und ausverkaufte Varianten ausblenden"}, "variant": {"content": "Varianten-Auswahl"}, "swatch_shape": {"label": "Swatch-Stil", "info": "<PERSON><PERSON>e die Dokumentation [Mehr erfahren](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches)", "options__1": {"label": "Kreis"}, "options__2": {"label": "Quadrat"}, "options__3": {"label": "<PERSON><PERSON>"}}}}, "pickup_availability": {"name": "Verfügbarkeit der Abholung"}, "description": {"name": "Beschreibung"}, "sku": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (SKU)", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "delivery_estimator": {"name": "Lieferzeitabschätzung", "settings": {"info": {"content": "<PERSON><PERSON><PERSON> [Themen-<PERSON><PERSON><PERSON>](https://manathemes.com/docs/flux-theme/how-to-guides/delivery-estimator) um mehr zu erfahren"}, "delivery_estimator_text": {"label": "Text zur Lieferzeitabschätzung"}, "earliest_delivery": {"label": "Früheste Lieferung", "info": "Minimale Anzahl von Tagen für die Lieferung, z. B.: 2"}, "latest_delivery": {"label": "Späteste Lieferung", "info": "Maximale Anzahl von Tagen für die Lieferung, z. B.: 5"}, "text_style": {"label": "Textstil", "options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "share": {"name": "Teilen", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "<PERSON><PERSON> in Social-Media-Beiträgen einen Link hinzufügen, wird das vorgestellte Bild der Seite als Vorschau-Bild angezeigt. [Erfah<PERSON> mehr](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Ein Geschäftstitel und eine Beschreibung werden mit dem Vorschau-Bild angezeigt. [Erfah<PERSON> mehr](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "share_top_right_corner": {"label": "<PERSON><PERSON> oben rechts positionieren"}}}, "custom_liquid": {"name": "Benutzerdefinierte Liquid-Tags", "settings": {"custom_liquid": {"label": "Benutzerdefiniertes Liquid", "info": "Fügen Sie App-Snippets oder anderen Liquid-Code hinzu, um erweiterte Anpassungen vorzunehmen."}}}, "collapsible_tab": {"name": "Ausklappbare Zeile", "settings": {"heading": {"info": "Fügen Sie eine Überschrift hinzu, die den Inhalt erklärt.", "label": "Überschrift"}, "no_padding": {"label": "<PERSON><PERSON>"}, "show_spacer": {"label": "Abstandshalter anzeigen"}, "image": {"label": "Bild"}, "content": {"label": "Zeileninh<PERSON>t"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von der Seite"}, "icon": {"label": "Symbol", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Einkaufskorb"}, "options__3": {"label": "Herzenstasche"}, "options__4": {"label": "<PERSON><PERSON>"}, "options__5": {"label": "Herzenkasten"}, "options__6": {"label": "<PERSON><PERSON>l"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Sprechblase"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "Zwischenablage"}, "options__11": {"label": "Tropfen"}, "options__12": {"label": "Halber Tropfen"}, "options__13": {"label": "Briefumschlag"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Tropfenpipette"}, "options__16": {"label": "Ausrufezeichen"}, "options__17": {"label": "Geschenk"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "Weltkugel"}, "options__20": {"label": "<PERSON><PERSON>"}, "options__21": {"label": "Headset"}, "options__22": {"label": "Liste"}, "options__23": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__24": {"label": "<PERSON><PERSON><PERSON>"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Kartenstift"}, "options__27": {"label": "Tasse"}, "options__28": {"label": "Pfotenabdruck"}, "options__29": {"label": "Shop"}, "options__30": {"label": "Person"}, "options__31": {"label": "Flugzeug"}, "options__32": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__33": {"label": "<PERSON><PERSON><PERSON>"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "Recycling"}, "options__36": {"label": "Rückgabe"}, "options__37": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__38": {"label": "Stern"}, "options__39": {"label": "Stoppuhr"}, "options__40": {"label": "Etikett"}, "options__41": {"label": "Baum"}, "options__42": {"label": "<PERSON><PERSON><PERSON> hoch"}, "options__43": {"label": "Lastwagen"}, "options__44": {"label": "Fragezeichen"}}}}, "popup": {"name": "Pop-Up", "settings": {"link_label": {"label": "Link-Beschriftung"}, "page": {"label": "Seite"}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-App hinzu. [Weitere Informationen](https://manathemes.com/docs/flux-theme/how-to-guides/complementary-and-related-products)"}}}, "complementary_products": {"name": "Ergänzende Produkte", "settings": {"paragraph": {"content": "Um ergänzende Produkte auszuwählen, fügen Sie die Search & Discovery App hinzu. [Weitere Informationen](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Überschrift"}, "make_collapsible_row": {"label": "Als aufklappbare Zeile anzeigen"}, "icon": {"info": "<PERSON><PERSON><PERSON>, wenn die aufklappbare Zeile angezeigt wird."}, "product_list_limit": {"label": "Maximale Anzahl der anzuzeigenden Produkte"}, "products_per_page": {"label": "Anzahl der Produkte pro Seite"}, "pagination_style": {"label": "Paginierungsstil", "options": {"option_1": "Punkte", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Nummern"}}, "product_card": {"heading": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options": {"option_1": "Hochformat", "option_2": "Quadrat"}}, "enable_quick_add": {"label": "Schaltfläche für schnelles Hinzufügen aktivieren"}, "columns": {"label": "Spalten", "options": {"option_1": "<PERSON><PERSON>", "option_2": "Zwei"}}}}, "ingredient_details": {"name": "Zutaten-Details", "settings": {"header_block_content": {"content": "Inhaltsstoffe"}, "left_column_label": {"label": "Bezeichnung der linken Spalte"}, "right_column_label": {"label": "Bezeichnung der rechten Spalte"}, "content": {"label": "Inhalt", "info": "Trennen Sie Label und Wert mit einem Komma. Verwenden Sie SHIFT + ENTER, um eine neue Zeile hinzuzufügen. Verwenden Sie einen Bindestrich, um Zeilen einzurücken. WICHTIG: Verwenden Sie keine Überschriften und Listenstile."}}}, "icon_with_text": {"name": "Symbol mit Text", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}}, "content": {"label": "Inhalt", "info": "Wählen Sie ein Symbol oder fügen Sie für jede Spalte oder Zeile ein Bild hinzu."}, "heading": {"info": "Lassen Sie das Überschriftenfeld leer, um die Spalte mit dem Symbol auszublenden."}, "icon_1": {"label": "Erstes Symbol"}, "image_1": {"label": "<PERSON><PERSON><PERSON> Bild"}, "heading_1": {"label": "Erste Überschrift"}, "icon_2": {"label": "Zweites Symbol"}, "image_2": {"label": "Zweites Bild"}, "heading_2": {"label": "Zweite Überschrift"}, "icon_3": {"label": "Drittes Sym<PERSON>"}, "image_3": {"label": "<PERSON><PERSON><PERSON>"}, "heading_3": {"label": "Dritte Überschrift"}}}}, "settings": {"info": {"content": "Besuchen Sie die [Thema-Dokumentation](https://manathemes.com/docs/flux-theme/product-page/), um mehr zu erfahren"}, "header_layout": {"content": "Layout"}, "header": {"content": "Medien", "info": "Erfahren Sie mehr über [Medientypen.](https://help.shopify.com/manual/products/product-media)"}, "enable_full_width": {"label": "Layout auf volle Breite einstellen"}, "enable_sticky_info": {"label": "Sticky-Inhalte auf Desktop aktivieren"}, "color_scheme": {"label": "Farbschema der Produktinformationen"}, "enable_info_padding": {"label": "Produktinformationsbox-Padding aktivieren"}, "product_gallery_width": {"label": "Breite der Produktgalerie"}, "product_layout": {"label": "Desktop-Layout", "options__1": {"label": "Layout 1"}, "options__2": {"label": "Layout 2"}, "options__3": {"label": "Layout 3"}, "options__4": {"label": "Layout 4"}, "options__5": {"label": "Layout 5"}}, "product_background_color": {"label": "Hintergrundfarbe"}, "title": {"label": "Überschrift"}, "gallery_layout": {"label": "Desktop-Layout", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Gitter"}, "options__3": {"label": "Miniaturbilder links"}, "options__4": {"label": "Miniaturbilder unten"}, "options__5": {"label": "Miniaturbilder rechts"}, "options__6": {"label": "Slide"}}, "constrain_to_viewport": {"label": "Medien auf Bildschirmhöhe begrenzen"}, "media_size": {"label": "Medienbreite auf Desktop", "info": "Medien werden automatisch für Mobilgeräte optimiert.", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Bildzoom", "info": "Klicken und Schweben öffnet auf Mobilgeräten standardmäßig die Lightbox.", "options__1": {"label": "Lightbox öffnen"}, "options__2": {"label": "Klicken und Schweben"}, "options__3": {"label": "<PERSON><PERSON>"}}, "media_style": {"label": "Medien- und Informationsstil", "options__1": {"label": "<PERSON>il eins"}, "options__2": {"label": "<PERSON><PERSON> zwei"}}, "media_position": {"label": "Medienposition auf Desktop", "info": "Die Position wird automatisch für Mobilgeräte optimiert.", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "media_fit": {"label": "Medienanpassung", "options__1": {"label": "Original"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "thumbnail_size": {"label": "Miniaturansichtsgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "under_gallery": {"label": "Position des Inhalts unter der Galerie", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Zweite"}}, "mobile_thumbnails": {"label": "Galerie-Layout", "options__1": {"label": "2 Spalten"}, "options__2": {"label": "Miniaturbilder anzeigen"}, "options__3": {"label": "Miniaturbilder ausblenden"}}, "hide_variants": {"label": "Medien anderer Varianten nach Auswahl einer Variante ausblenden"}, "enable_video_looping": {"label": "Videowiederholung aktivieren"}, "header_in_stock_colors": {"content": "Vorrätige Farben"}, "in_stock_background_color": {"label": "Hintergrundfarbe für vorrätig"}, "in_stock_color": {"label": "Textfarbe für vorrätig"}, "desktop_enable_gradient": {"label": "Dekorationshintergrund aktivieren"}, "header_quantity_selector": {"content": "Men<PERSON>aus<PERSON><PERSON>"}, "quantity_selector": {"label": "<PERSON><PERSON>aus<PERSON>hl"}, "header_mobile": {"content": "Mobil-Layout"}, "mobile_enable_gradient": {"label": "Dekorationshintergrund aktivieren"}}}, "main-register": {"name": "Registrierung"}, "main-reset-password": {"name": "Passwort zurücksetzen"}, "main-search": {"name": "Suchergebnisse", "settings": {"columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An das Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Zweites Bild bei Hover anzeigen"}, "show_vendor": {"label": "Verkäufer anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-App hinzu. [Mehr erfahren](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "header__1": {"content": "Produktkarte"}, "header__2": {"content": "Blogkarte", "info": "Die Blogkartenstile gelten auch für Seiteneintragskarten in den Suchergebnissen. Um die Kartenstile zu ändern, aktualisieren Sie Ihre Theme-Einstellungen."}, "header__3": {"content": "Seitenkarte"}, "show_article_posts": {"label": "Artikelkarten anzeigen"}, "show_page_posts": {"label": "Seitenkarten anzeigen"}, "article_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "article_show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Mobilgerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "quick-order-list": {"name": "Schnellbestellliste", "settings": {"enable_card_background": {"label": "Kartenhintergrund aktivieren"}, "show_image": {"label": "Bild anzeigen"}, "show_sku": {"label": "SKU anzeigen"}}, "presets": {"name": "Schnellbestellliste"}}, "multicolumn": {"name": "Mehrspaltig", "settings": {"multicolumn_style": {"label": "Layout", "options__1": {"label": "Standard"}, "options__2": {"label": "Elegant"}}, "enable_card_background": {"label": "Kartenhintergrund aktivieren"}, "caption": {"label": "Untertitel"}, "title": {"label": "Überschrift"}, "image_width": {"label": "Bildbreite", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Voll"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An das Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "<PERSON>uadratisch"}, "options__4": {"label": "Kreis"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "desktop_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Mobilgerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "swipe_on_mobile": {"label": "Swipe auf Mobilgeräten aktivieren"}, "mobile_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "hide_image": {"label": "Bild au<PERSON>"}, "title": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}}}}, "presets": {"name": "Mehrspaltig"}}, "events-calendar": {"name": "Veranstaltungskalender", "settings": {"enable_card_background": {"label": "Kartenshintergrund aktivieren"}, "caption": {"label": "Unterüberschrift"}, "title": {"label": "Überschrift"}, "image_width": {"label": "Bildbreite", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Voll"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "Quadrat"}, "options__4": {"label": "Kreis"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "event_layout": {"label": "Layout", "options__1": {"label": "Gitter"}, "options__2": {"label": "Liste"}}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "desktop_enable_gradient": {"label": "Dekorationshintergrund aktivieren"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Handy", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "swipe_on_mobile": {"label": "Wischen auf dem Handy aktivieren"}, "mobile_enable_gradient": {"label": "Dekorationshintergrund aktivieren"}}, "blocks": {"event": {"name": "Veranstaltung", "settings": {"image": {"label": "Bild"}, "hide_image": {"label": "Bild au<PERSON>"}, "event_date": {"label": "Datum"}, "event_month": {"label": "<PERSON><PERSON>", "options__1": {"label": "Jan"}, "options__2": {"label": "Feb"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Apr"}, "options__5": {"label": "<PERSON>"}, "options__6": {"label": "Jun"}, "options__7": {"label": "Jul"}, "options__8": {"label": "Aug"}, "options__9": {"label": "Sep"}, "options__10": {"label": "Okt"}, "options__11": {"label": "Nov"}, "options__12": {"label": "<PERSON>z"}}, "hide_date": {"label": "<PERSON><PERSON> aus<PERSON>nden"}, "event_time": {"label": "Tag und Uhrzeit"}, "event_price": {"label": "Pre<PERSON>"}, "event_heading": {"label": "Überschrift"}, "event_description": {"label": "Text"}, "event_location": {"label": "Ort"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}}}}, "presets": {"name": "Veranstaltungskalender"}}, "multicolumn-cover": {"name": "Mehrspaltige Abdeckung", "settings": {"caption": {"label": "Untertitel"}, "title": {"label": "Überschrift"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An das Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "<PERSON>uadratisch"}}, "min_overlay_height": {"label": "Overlay-<PERSON><PERSON><PERSON> an<PERSON>en"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "background_style": {"label": "Sekundärer Hintergrund", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Als Spaltenhintergrund anzeigen"}}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "show_text_box": {"label": "Textfeld anzeigen"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Mobilgerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "swipe_on_mobile": {"label": "Swipe auf Mobilgeräten aktivieren"}, "full_width": {"label": "Be<PERSON>ich auf volle Breite einstellen"}, "image_overlay_opacity": {"label": "Deckkraft", "info": "Die Deckkraftfarbe können Sie global in den [Theme-Einstellungen](/editor?context=theme&category=colors) ändern."}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "title": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}}}}, "presets": {"name": "Mehrspaltige Abdeckung"}}, "icons-with-text": {"name": "Symbole mit Text", "settings": {"image_width": {"label": "Bildbreite", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Vollständig"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An das Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "<PERSON>uadratisch"}, "options__4": {"label": "Kreisförmig"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "enable_desktop_slider": {"label": "Ka<PERSON>ell auf dem Desktop aktivieren"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Mobilgerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "swipe_on_mobile": {"label": "Swipe auf Mobilgeräten aktivieren"}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "title": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}}}}, "presets": {"name": "Symbole mit Text"}}, "infocards": {"name": "Info-Karten", "settings": {"cards_style": {"label": "Layout", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}, "options__3": {"label": "3 Spalten"}}}, "blocks": {"infocard": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Bild"}, "hide_image": {"label": "Bild au<PERSON>"}, "title": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "text": {"label": "Beschreibung"}, "link_label": {"label": "Link-Be<PERSON>ichnung"}, "link": {"label": "Link"}, "infocards_background_color": {"label": "Kartenhintergrundfarbe"}, "infocards_color": {"label": "Karten-Textfarbe"}}}}, "presets": {"name": "Info-Karten"}}, "testimonials": {"name": "Kundenstimmen", "settings": {"title": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "desktop_title_caption_position": {"label": "Position der Überschrift und Beschriftung auf dem Desktop", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "button_label": {"label": "Button-Beschriftung"}, "button_link": {"label": "Button-Link"}, "desktop_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}, "testimonials_style": {"label": "Stil der Testimonials", "options__1": {"label": "Standard"}, "options__2": {"label": "Modern"}}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Mobilgerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "swipe_on_mobile": {"label": "Swipe auf Mobilgeräten aktivieren"}, "mobile_enable_gradient": {"label": "Hintergrunddekoration aktivieren"}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "title": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "rating": {"label": "Sterne anzeigen"}, "rating_stars": {"label": "Bewertungssterne", "options__1": {"label": "5 Sterne"}, "options__2": {"label": "4 Sterne"}, "options__3": {"label": "3 Sterne"}, "options__4": {"label": "2 Sterne"}, "options__5": {"label": "1 Stern"}}, "product": {"label": "Produkt"}}}}, "presets": {"name": "Kundenstimmen"}}, "promo-popup": {"name": "Werbe-Popup", "settings": {"enable_popup": {"label": "Popup aktivieren"}, "popup_test": {"label": "Test-Popup anzeigen"}, "layout": {"label": "Layout", "options__1": {"label": "Standard"}, "options__2": {"label": "Gitter"}, "options__3": {"label": "Raster-Unten-Rechts"}}, "popup_title": {"label": "Überschrift"}, "header_font_size": {"label": "Schriftgröße der Überschrift"}, "popup_message": {"label": "Nachricht"}, "message_font_size": {"label": "Schriftgröße der Nachricht"}, "submit_button_text": {"label": "Text für Schaltfläche senden"}, "button_color": {"label": "Schaltflächenfarbe"}, "button_text_color": {"label": "Textfarbe der Schaltfläche"}, "button_hover_color": {"label": "Schaltflächen-Hover-Farbe"}, "success_message": {"label": "Erfolgsmeldung"}, "fadein": {"label": "Z<PERSON>, bevor das Popup erscheint"}, "popup_count": {"label": "<PERSON><PERSON><PERSON> der Popups"}, "popup_image": {"label": "Bild für das Popup auswählen"}, "image_width": {"label": "Bildbreite"}, "image_alt": {"label": "Bild-Alternativtext"}}, "presets": {"name": "Werbe-Popup"}}, "promotion-cards": {"name": "Promotionskarten", "settings": {"title": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Mobilgerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "promotion_cards_style": {"label": "Stil der Werbekarten", "options__1": {"label": "Modern"}, "options__2": {"label": "Elegant"}, "options__3": {"label": "Cover"}}, "swipe_on_mobile": {"label": "Swipe auf Mobilgeräten aktivieren"}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "title": {"label": "Überschrift"}, "link_label": {"label": "Link-Beschriftung"}, "link": {"label": "Link"}, "product": {"label": "Produkt"}}}}, "presets": {"name": "Promotionskarten"}}, "newsletter": {"name": "E-Mail-Anmeldung", "settings": {"full_width": {"label": "Bereichshintergrund vollständige Breite", "info": "Nur mit farbigem Hintergrund sichtbar."}, "paragraph": {"content": "Jede E-Mail-Abonnement erstellt ein Kundenkonto. [<PERSON>rf<PERSON><PERSON> mehr](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "paragraph": {"name": "Unterüberschrift", "settings": {"paragraph": {"label": "Beschreibung"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "E-Mail-Anmeldung"}}, "newsletter-banner": {"name": "E-Mail-Anmelde-Banner", "settings": {"paragraph": {"content": "Jede E-Mail-Abonnement erstellt ein Kundenkonto. [<PERSON>rf<PERSON><PERSON> mehr](https://help.shopify.com/manual/customers)"}, "image": {"label": "Bild"}, "image_2": {"label": "Bild", "info": "Dieses Bild wird angeze<PERSON>t, wenn Sie 'Style two' für Ihren Newsletter-Stil auswählen."}, "newsletter_style": {"label": "Newsletter-Stil", "options__1": {"label": "Ein Bild"}, "options__2": {"label": "Zwei Bilder"}, "options__3": {"label": "<PERSON><PERSON>"}}, "layout": {"label": "Desktop-Layout", "options__1": {"label": "Zuerst Bild"}, "options__2": {"label": "Zuerst Text"}}, "full_width": {"label": "Bereich vollständige Breite machen"}}, "blocks": {"caption": {"name": "Untertitel", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Textgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "paragraph": {"name": "Absatz", "settings": {"paragraph": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "Fließtext"}, "options__2": {"label": "Untertitel"}, "label": "Textstil"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "E-Mail-Anmelde-Banner"}}, "recently-viewed-products": {"name": "Zuletzt angesehene Produkte", "settings": {"heading": {"label": "Überschrift"}, "caption": {"label": "Unterüberschrift"}}, "presets": {"name": "Zuletzt angesehene Produkte"}}, "page": {"name": "Seite", "settings": {"page": {"label": "Seite"}}, "presets": {"name": "Seite"}}, "related-products": {"name": "Verwandte Produkte", "settings": {"heading": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "text_style": {"label": "Textstil für die Untertitel", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "text_size": {"label": "Textgröße für die Untertitel", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "products_to_show": {"label": "Maximale anzuzeigende Produkte"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "paragraph__1": {"content": "Dynamische Empfehlungen verwenden Bestell- und Produktinformationen, um sich im Laufe der Zeit zu ändern und zu verbessern. [Erfahren Si<PERSON> mehr](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An das Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Zweites Bild beim Hover anzeigen"}, "show_vendor": {"label": "Hersteller anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, fügen Sie eine Produktbewertungs-App hinzu. [Erfahren <PERSON> mehr](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf dem Handy", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "rich-text": {"name": "Rich Text", "settings": {"desktop_content_position": {"options__1": {"label": "<PERSON><PERSON> au<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Rechts ausrichten"}, "label": "Desktop-Inhaltsposition", "info": "Die Position wird automatisch für mobile Geräte optimiert."}, "content_alignment": {"options__1": {"label": "<PERSON><PERSON> au<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Rechts ausrichten"}, "label": "Inhaltsausrichtung"}, "header_mobile": {"content": "Mobiles Layout"}, "mobile_content_alignment": {"options__1": {"label": "<PERSON><PERSON> au<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Rechts ausrichten"}, "label": "Mobiles Inhaltsausrichtung"}, "full_width": {"label": "Hintergrund auf volle Breite setzen", "info": "Nur bei farbigem Hintergrund sichtbar."}}, "blocks": {"image": {"name": "Bild", "settings": {"image": {"label": "Bild", "info": "<PERSON>ür das optimale Erscheinungsbild stellen Si<PERSON> bitte sicher, dass die Bildabmessungen 120x120 Pixel betragen."}}}, "heading": {"name": "Überschrift", "settings": {"heading": {"label": "Überschrift"}}}, "caption": {"name": "Untertitel", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Textgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Label für erste Schaltfläche", "info": "<PERSON>sen Sie das Label leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "Link für erste Schaltfläche"}, "button_style_secondary_1": {"label": "Stil der Rand-Schaltfläche verwenden"}, "button_label_2": {"label": "Label für zweite Schaltfläche", "info": "<PERSON>sen Sie das Label leer, um die Schaltfläche auszublenden."}, "button_link_2": {"label": "Link für zweite Schaltfläche"}, "button_style_secondary_2": {"label": "Stil der Rand-Schaltfläche verwenden"}}}}, "presets": {"name": "Rich Text"}}, "scrolling-text": {"name": "Scrollender Text/Bild", "settings": {"scroll_direction": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Links"}, "label": "Scrollrichtung"}, "enable_scroll_decoration": {"label": "Dekoration aktivieren"}, "scroll_decoration": {"label": "Scroll-Dekoration", "options__1": {"label": "Kreis"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Hexagon"}, "options__4": {"label": "Stern"}}, "enable_stencil_text": {"label": "Schablonentextstil"}, "scroll_speed": {"label": "Geschwindigkeit"}, "scroll_height": {"label": "Scrollhöhe"}, "scroll_text_size": {"label": "Textgröße"}, "hover_stop": {"label": "<PERSON><PERSON> anhalten"}, "keep_small_mobile": {"label": "<PERSON>f kleinen Geräten beibehalten"}, "border": {"label": "<PERSON><PERSON><PERSON>"}, "enable_announcement_bar_desktop_sticky": {"label": "Klebriges Layout auf dem Desktop aktivieren"}, "enable_announcement_bar_mobile_sticky": {"label": "Klebriges Layout auf dem Handy aktivieren"}}, "blocks": {"text": {"name": "Text/Bild", "settings": {"text": {"label": "Beschreibung"}, "image": {"label": "Bild", "info": "Wenn Si<PERSON> ein Bild hinzufügen, wird der Text verborgen!"}, "image_link": {"label": "Bildlink"}}}}, "presets": {"name": "Scrollender Text/Bild"}}, "video": {"name": "Video", "settings": {"caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "button_label_1": {"label": "Schaltflächenaufschrift"}, "button_link_1": {"label": "Schaltflächenlink"}, "video_layout": {"label": "Layout", "options__1": {"label": "Layout eins"}, "options__2": {"label": "Layout zwei"}}, "cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "placeholder": "Verwenden Sie eine YouTube- oder Vimeo-URL", "info": "Akzeptiert YouTube oder Vimeo-URL"}, "description": {"label": "Alternativer Text für das Video", "info": "Beschreiben Sie das Video für Kunden, die Bildschirmlesegeräte verwenden. [Erfahren Si<PERSON> mehr](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Bildpolster hinzufügen", "info": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Bildpolster aus, wenn <PERSON> nicht möchten, dass Ihr Titelbild beschnitten wird."}, "full_width": {"label": "<PERSON><PERSON>ich bildschirmfüllend machen"}}, "presets": {"name": "Video"}}, "video-background": {"name": "Video-Hi<PERSON>grund", "settings": {"video_url": {"label": "Video auswählen", "info": "Laden Sie Ihr Video in Inhalte - Dateien hoch, kopieren Si<PERSON> dann den Link und fügen Si<PERSON> ihn hier ein."}, "poster": {"label": "Fallback-<PERSON><PERSON>grundbild hinzufügen, falls das Video nicht geladen wird"}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "button_label": {"label": "Schaltflächenbeschriftung", "info": "<PERSON>sen Sie das Label leer, um die Schaltfläche auszublenden."}, "link": {"label": "Schaltflächen-Link"}, "secondary_style": {"label": "Verwenden Sie den Umriss-Schaltflächenstil"}, "full_width_background": {"label": "<PERSON><PERSON>ich bildschirmfüllend machen"}, "background_height": {"label": "Höhe des Desktop-Hintergrunds", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "background_height_mobile": {"label": "Höhe des Hintergrunds auf Mobilgeräten", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "text_align": {"label": "Textausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "box_align": {"label": "Inhaltsposition", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "ignore_box": {"label": "Kästchen aktivieren"}, "blur": {"label": "Fügen Sie bei niedriger Videoqualität einen Weichzeichnungseffekt hinzu"}, "opacity": {"label": "Ändern Sie die Video-Deckkraft"}, "header_video": {"content": "Video"}, "video_style": {"label": "Videostil", "info": "Im nativen Modus wird das Video in seiner Originalgröße ohne Überlagerung von Inhalten angezeigt. Um Inhalte hinzuzufügen, wählen Sie die Hintergrundoption.", "options__1": {"label": "Nativ"}, "options__2": {"label": "Hi<PERSON>grund"}}}, "presets": {"name": "Video-Hi<PERSON>grund"}}, "slideshow": {"name": "<PERSON><PERSON><PERSON>", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Vollbild"}, "options__2": {"label": "Eingerahmt"}}, "slide_height": {"label": "Höhe der Folie", "options__1": {"label": "An erstes Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "slider_visual": {"label": "Anzeigestil der Paginierung", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "auto_rotate": {"label": "Folien automatisch drehen"}, "change_slides_speed": {"label": "Folien alle wechseln"}, "slideshow_controls_background": {"label": "Hintergrund für Schiebereglersteuerung hinzufügen"}, "slider_animations": {"label": "Animationen des Schiebereglers", "options__1": {"label": "Standard"}, "options__2": {"label": "Vertikales Zoomen"}, "options__3": {"label": "Horizontales Zoomen"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__5": {"label": "Horizontale Enthüllung"}}, "mobile": {"content": "Mobiles Layout"}, "show_text_below": {"label": "Inhalt unter den Bildern auf Mobilgeräten anzeigen"}, "accessibility": {"content": "Barrierefreiheit", "label": "Beschreibung der Diashow", "info": "Beschreiben Sie die Diashow für Kunden, die Bildschirmlesegeräte verwenden."}}, "blocks": {"slide": {"name": "Folie", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "subheading": {"label": "Text"}, "button_label": {"label": "Schaltflächenbeschriftung", "info": "<PERSON>sen Sie die Beschriftung leer, um die Schaltfläche auszublenden."}, "link": {"label": "Schaltflächenlink"}, "secondary_style": {"label": "Verwenden Sie den Randstil für die Schaltfläche"}, "box_align": {"label": "Position des Inhalts auf dem Desktop", "info": "Die Position wird automatisch für Mobilgeräte optimiert.", "options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "<PERSON>ten Mi<PERSON>"}, "options__9": {"label": "Unten rechts"}}, "show_text_box": {"label": "Textfeld auf dem Desktop anzeigen"}, "text_alignment": {"label": "Desktop-Inhaltsausrichtung", "option_1": {"label": "Links"}, "option_2": {"label": "<PERSON><PERSON>"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Deckkraft des Bildüberzugs"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn der Container angezeigt wird."}, "text_alignment_mobile": {"label": "Mobile Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "slideshow-two-columns": {"name": "Diashow zwei spalten", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Vollbild"}, "options__2": {"label": "Eingerahmt"}}, "slide_height": {"label": "Höhe der Folie", "options__1": {"label": "An erstes Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "slider_visual": {"label": "Anzeigestil der Paginierung", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "auto_rotate": {"label": "Folien automatisch drehen"}, "change_slides_speed": {"label": "Folien alle wechseln"}, "slideshow_controls_background": {"label": "Hintergrund für Schiebereglersteuerung hinzufügen"}, "slider_animations": {"label": "Animationen des Schiebereglers", "options__1": {"label": "Standard"}, "options__2": {"label": "Vertikales Zoomen"}, "options__3": {"label": "Horizontales Zoomen"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__5": {"label": "Horizontale Enthüllung"}}, "mobile": {"content": "Mobiles Layout"}, "hide_slider_controls": {"label": "Schiebereglersteuerungen ausblenden"}, "show_text_below": {"label": "Inhalt unter den Bildern auf Mobilgeräten anzeigen"}, "accessibility": {"content": "Barrierefreiheit", "label": "Beschreibung der Diashow", "info": "Beschreiben Sie die Diashow für Kunden, die Bildschirmlesegeräte verwenden."}}, "blocks": {"slide": {"name": "Folie", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "subheading": {"label": "Text"}, "button_label": {"label": "Schaltflächenbeschriftung", "info": "<PERSON>sen Sie die Beschriftung leer, um die Schaltfläche auszublenden."}, "link": {"label": "Schaltflächenlink"}, "secondary_style": {"label": "Verwenden Sie den Randstil für die Schaltfläche"}, "text_alignment": {"label": "Desktop-Inhaltsausrichtung", "option_1": {"label": "Links"}, "option_2": {"label": "<PERSON><PERSON>"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn der Container angezeigt wird."}, "text_alignment_mobile": {"label": "Mobile Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Diashow Diashow zwei spalten"}}, "advanced-slideshow": {"name": "Parallax Slider", "settings": {"auto_rotate": {"label": "Automatisches Drehen der Folien"}, "slider_direction": {"label": "Slide<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}}, "slider_loop": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "slider_interval": {"label": "Slider-Intervall"}, "slider_height": {"label": "Slider-Höhe"}, "full_width": {"label": "<PERSON><PERSON><PERSON>n auf volle Breite machen"}, "box": {"content": "Inhaltsbox-Einstellungen"}, "content_height": {"label": "Vertikale Position des Inhalts"}, "content_position": {"label": "Horizontale Position des Inhalts"}, "content_size": {"label": "Größe der Inhaltsbox"}, "content_align": {"label": "Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "content_box": {"label": "Hintergrund der Inhaltsbox aktivieren"}, "content_opacity": {"label": "Deckkraft der Inhaltsbox"}, "text": {"content": "Texteinstellungen"}, "heading_size": {"label": "Überschriftengröße"}, "heading_style": {"label": "Stil des Überschriftentextes", "options__1": {"label": "Standard"}, "options__2": {"label": "Großbuchstaben"}, "options__3": {"label": "Kapitälchen"}}, "caption_size": {"label": "Beschriftungsgröße"}, "link_size": {"label": "Linkgröße"}, "caption_style": {"label": "Stil des Beschriftungstextes", "options__1": {"label": "Standard"}, "options__2": {"label": "Großbuchstaben"}, "options__3": {"label": "Kapitälchen"}}, "mobile": {"content": "Mobile Einstellungen"}, "mobile_heading_size": {"label": "Überschriftgröße"}, "mobile_caption_size": {"label": "Bildunterschriftgröße"}, "mobile_height": {"label": "Slider-Höhe"}, "mobile_content_height": {"label": "Vertikale Position des Inhalts"}, "mobile_content_size": {"label": "Größe der Inhaltsbox"}, "other": {"content": "Weitere Einstellungen"}}, "blocks": {"slide": {"name": "Folie", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Beschriftung"}, "heading": {"label": "Überschrift"}, "button_label": {"label": "Beschriftung des Buttons", "info": "<PERSON>sen Sie die Beschriftung leer, um den Button auszublenden."}, "link": {"label": "Button-Link"}, "show_link_button": {"label": "Schaltfläche aktivieren"}, "secondary_style": {"label": "Umriss-Button-Stil verwenden"}}}}, "presets": {"name": "Parallax Slider"}}, "slick-slider": {"name": "<PERSON><PERSON>lider", "settings": {"header": {"content": "Slider-Einstellungen"}, "image_opacity": {"label": "Bild-Transparenz"}, "opacity_mobile": {"label": "Transparenz nur auf Mobilgeräten"}}, "presets": {"name": "<PERSON><PERSON>lider"}}, "comparison-slider": {"name": "Vergleichsregler", "settings": {"title": {"label": "Überschrift"}, "caption": {"label": "Untertitel"}, "image": {"label": "Bild eins"}, "image_2": {"label": "Bild zwei"}, "comparison_slider_layout": {"label": "Layout", "options__1": {"label": "Vertikal"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}}, "height": {"label": "H<PERSON><PERSON> auf dem Desktop"}, "mouse_percent": {"label": "Standardposition des Reglers"}, "disable_before_after": {"label": "<PERSON><PERSON><PERSON>/Nachher deaktivieren"}, "before_text": {"label": "Text vorher"}, "after_text": {"label": "Text nachher"}, "header_mobile": {"content": "Mobil"}, "mobile_height": {"label": "Höhe auf Mobilgeräten"}, "full_width": {"label": "Layout vollständige Breite machen"}, "accessibility": {"content": "Barrierefreiheit", "label": "Beschreibung der Diashow", "info": "Beschreiben Sie die Diashow für Kunden, die Bildschirmlesegeräte verwenden."}}, "presets": {"name": "Vergleichsregler"}}, "anchor_link": {"name": "Ankerlink", "settings": {"anchor": {"label": "HTML-Anker", "info": "Füge Anker-Text zum Verlinken hinzu"}}, "presets": {"name": "Ankerlink"}}, "separator": {"name": "<PERSON><PERSON><PERSON>", "settings": {"separator_style": {"label": "Stil", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Welle 1"}, "options__3": {"label": "Welle 2"}, "options__4": {"label": "Welle 3"}, "options__5": {"label": "Welle 4"}, "options__6": {"label": "Wolke 1"}, "options__7": {"label": "Wolke 2"}, "options__8": {"label": "Hügel 1"}, "options__9": {"label": "Hügel 2"}, "options__10": {"label": "Gekerbter Rand 1"}, "options__11": {"label": "Gekerbter Rand 2"}}, "spacer_color": {"label": "Farbe des Abstandhalters"}, "margin_top_desktop": {"label": "Oberer Rand auf Desktop"}, "margin_bottom_desktop": {"label": "Unterer Rand auf Desktop"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "collapsible_content": {"name": "Ausklappbarer Inhalt", "settings": {"caption": {"label": "Untertitel"}, "heading": {"label": "Überschrift"}, "heading_alignment": {"label": "Ausrichtung der Überschrift", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "content_style": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "container_color_scheme": {"label": "Container-Farbschema", "info": "<PERSON><PERSON><PERSON>, wenn das Layout auf Zeilen- oder Abschnitts-Container festgelegt ist."}, "open_first_collapsible_row": {"label": "<PERSON><PERSON><PERSON> ausklappbare Zeile öffnen"}, "open_all_collapsible_row": {"label": "Alle öffnen"}, "header_mobile": {"content": "Mobil-Layout"}}, "blocks": {"collapsible_row": {"name": "Ausklappbare Zeile", "settings": {"heading": {"info": "Fügen Sie eine Überschrift hinzu, die den Inhalt erklärt.", "label": "Überschrift"}, "row_content": {"label": "Zeileninh<PERSON>t"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>"}, "icon": {"label": "Symbol", "options__1": {"label": "<PERSON>in <PERSON>"}, "options__2": {"label": "Einkaufskorb"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Box"}, "options__5": {"label": "Box Herz"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Sprechblase"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "Zwischenablage"}, "options__11": {"label": "Tropfen"}, "options__12": {"label": "Tropfen Hälfte"}, "options__13": {"label": "Umschlag"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Pipette"}, "options__16": {"label": "Ausrufezeichen"}, "options__17": {"label": "Geschenk"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "Globus"}, "options__20": {"label": "<PERSON><PERSON>"}, "options__21": {"label": "<PERSON><PERSON>h<PERSON><PERSON>"}, "options__22": {"label": "Liste"}, "options__23": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__24": {"label": "<PERSON><PERSON><PERSON>"}, "options__25": {"label": "Zauberstab"}, "options__26": {"label": "Kartenstift"}, "options__27": {"label": "Tasse"}, "options__28": {"label": "Pfotenabdruck"}, "options__29": {"label": "Shop"}, "options__30": {"label": "Person"}, "options__31": {"label": "Flugzeug"}, "options__32": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__33": {"label": "<PERSON><PERSON><PERSON>"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "Recycling"}, "options__36": {"label": "Zurück"}, "options__37": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__38": {"label": "Stern"}, "options__39": {"label": "Stoppuhr"}, "options__40": {"label": "Etikett"}, "options__41": {"label": "Baum"}, "options__42": {"label": "<PERSON><PERSON><PERSON> hoch"}, "options__43": {"label": "Lkw"}, "options__44": {"label": "Fragezeichen"}}}}}, "presets": {"name": "Ausklappbarer Inhalt"}}, "tabs": {"name": "Tabs", "settings": {"tabs_style": {"label": "Stil", "options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Schaltfläche"}}, "full_width": {"label": "Tabs auf volle Breite"}}, "blocks": {"tab": {"name": "Tab", "settings": {"image": {"label": "Bild"}, "hide_image": {"label": "Bild au<PERSON>"}, "tab_image_width": {"label": "Bildgröße"}, "heading": {"info": "Fügen Sie eine Überschrift hinzu, die den Inhalt erklärt.", "label": "Überschrift"}, "row_content": {"label": "Tab-Inhalt"}, "page": {"label": "Tab-Inhalt von Seite"}, "button_label": {"label": "Beschriftung des ersten Buttons", "info": "<PERSON>sen Sie die Beschriftung leer, um den Button auszublenden."}, "button_link": {"label": "Link des ersten Buttons"}, "button_style_secondary": {"label": "Verwenden Sie den Umriss-Stil für den Button"}}}}, "presets": {"name": "Tabs"}}, "about": {"name": "Über uns", "settings": {"caption": {"label": "Untertitel"}, "title": {"label": "Überschrift"}, "text": {"label": "Text"}, "image": {"label": "Bild"}, "open_first_collapsible_row": {"label": "<PERSON>rste aufklappbare Zeile öffnen"}, "header_mobile": {"content": "Mobil-Layout"}}, "blocks": {"collapsible_row": {"name": "Aufklappbare Zeile", "settings": {"heading": {"info": "Fügen Sie eine Überschrift hinzu, die den Inhalt erklärt.", "label": "Überschrift"}, "row_content": {"label": "Zeileninh<PERSON>t"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>"}, "icon": {"label": "Symbol", "options__1": {"label": "<PERSON>in <PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Box"}, "options__5": {"label": "Herz Box"}, "options__6": {"label": "<PERSON><PERSON>l"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Sprechblase"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "Zwischenablage"}, "options__11": {"label": "Tropfen"}, "options__12": {"label": "Halber Tropfen"}, "options__13": {"label": "Umschlag"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Pipette"}, "options__16": {"label": "Ausrufezeichen"}, "options__17": {"label": "Geschenk"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "Globus"}, "options__20": {"label": "<PERSON><PERSON>"}, "options__21": {"label": "<PERSON><PERSON>h<PERSON><PERSON>"}, "options__22": {"label": "Liste"}, "options__23": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__24": {"label": "<PERSON><PERSON><PERSON>"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Kartenstift"}, "options__27": {"label": "Tasse"}, "options__28": {"label": "Pfotenabdruck"}, "options__29": {"label": "Shop"}, "options__30": {"label": "Person"}, "options__31": {"label": "Flugzeug"}, "options__32": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__33": {"label": "<PERSON><PERSON><PERSON>"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "Recycling"}, "options__36": {"label": "Rückgabe"}, "options__37": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__38": {"label": "Stern"}, "options__39": {"label": "Stoppuhr"}, "options__40": {"label": "Etikett"}, "options__41": {"label": "Baum"}, "options__42": {"label": "<PERSON><PERSON><PERSON> hoch"}, "options__43": {"label": "Lastwagen"}, "options__44": {"label": "Fragezeichen"}}}}}, "presets": {"name": "Über uns"}}}}
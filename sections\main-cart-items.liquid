{{ 'component-cart.css' | asset_url | stylesheet_tag }}
{{ 'component-cart-items.css' | asset_url | stylesheet_tag }}
{{ 'component-totals.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-discounts.css' | asset_url | stylesheet_tag }}
{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'quick-add.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: {{ section.settings.padding_left | times: 0.75 | round: 0 }}px;
    padding-right: {{ section.settings.padding_right | times: 0.75 | round: 0 }}px;
  }

  cart-items {
    border-radius: var(--media-radius);
  }

  cart-items .title-wrapper-with-link {
    margin: 0 0 3rem;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
      padding-left: {{ section.settings.padding_left }}px;
      padding-right: {{ section.settings.padding_right }}px;
    }
  }
{%- endstyle -%}

{%- unless settings.cart_type == 'drawer' -%}
  <script src="{{ 'cart.js' | asset_url }}" defer="defer"></script>
{%- endunless -%}

<cart-items class="{% if cart == empty %} is-empty{% else %} section-{{ section.id }}-padding{% endif %} color-{{ section.settings.color_scheme }} gradient ignore-{{ section.settings.ignore_spacing }}">
  <div class="title-wrapper-with-link">
    <h1 class="title title--primary heading-bold">{{ 'sections.cart.title' | t }}</h1>
    <a href="{{ routes.all_products_collection_url }}" class="link">{{ 'general.continue_shopping' | t }} </a>
  </div>

  <div class="cart__warnings">
    <h1 class="cart-empty-text">{{ 'sections.cart.empty' | t }}</h1>
    {% if settings.enable_empty_cart_message %}
      <div class="empty-cart-message cart-items" data-aos="fade-up" data-aos-delay="150">
        <p>{{ settings.empty_cart_message }}</p>
      </div>
    {% endif %}

    {% if settings.button_link %}
      <a href="{{ settings.button_link }}" class="button" data-aos="fade-up" data-aos-delay="200">
        {{ 'general.continue_shopping' | t }}
      </a>
    {% else %}
      <a href="{{ routes.all_products_collection_url }}" class="button" data-aos="fade-up" data-aos-delay="200">
        {{ 'general.continue_shopping' | t }}
      </a>
    {% endif %}

    {%- if shop.customer_accounts_enabled and customer == null -%}
      <h2 class="cart-login-title">{{ 'sections.cart.login.title' | t }}</h2>
      <p class="cart-login-paragraph">
        {{ 'sections.cart.login.paragraph_html' | t: link: routes.account_login_url }}
      </p>
    {%- endif -%}
  </div>

  <div class="cart-page">
    <form action="{{ routes.cart_url }}" class="cart-contents critical-hidden" method="post" id="cart">
      <div class="cart__items" id="main-cart-items" data-id="{{ section.id }}">
        <div class="js-contents">
          {%- if cart != empty -%}
            <table class="cart-items">
              <caption class="visually-hidden" data-aos="fade-up" data-aos-delay="200">
                {{ 'sections.cart.title' | t }}
              </caption>

              <tbody>
                {%- for item in cart.items -%}
                  <tr class="cart-item" id="CartItem-{{ item.index | plus: 1 }}">
                    <td class="cart-item-media">
                      {% if item.image %}
                        {% comment %} Leave empty space due to a:empty CSS display: none rule {% endcomment %}
                        <a href="{{ item.url }}" class="cart-item-link" aria-hidden="true" tabindex="-1"> </a>
                        <div class="cart-item-image-container global-media-settings">
                          <img
                            src="{{ item.image | image_url: width: 300 }}"
                            class="cart-item-image"
                            alt="{{ item.image.alt | escape }}"
                            loading="lazy"
                            width="150"
                            height="{{ 150 | divided_by: item.image.aspect_ratio | ceil }}"
                          >
                        </div>
                      {% endif %}
                    </td>

                    <td class="cart-item-details">
                      {%- if settings.show_vendor -%}
                        <p class="caption-with-letter-spacing">{{ item.product.vendor }}</p>
                      {%- endif -%}

                      <a href="{{ item.url }}" class="cart-item-name h4 break">{{ item.product.title | escape }}</a>

                      {%- if item.original_price != item.final_price -%}
                        <div class="cart-item-discounted-prices">
                          <span class="visually-hidden">
                            {{ 'products.product.price.regular_price' | t }}
                          </span>
                          <s class="cart-item-old-price product-option">
                            {{- item.original_price | money -}}
                          </s>
                          <span class="visually-hidden">
                            {{ 'products.product.price.sale_price' | t }}
                          </span>
                          <strong class="cart-item-final-price product-option">
                            {{ item.final_price | money }}
                          </strong>
                        </div>
                      {%- else -%}
                        <div class="product-option">
                          {{ item.original_price | money }}
                        </div>
                      {%- endif -%}

                      {%- if item.product.has_only_default_variant == false
                        or item.properties.size != 0
                        or item.selling_plan_allocation != null
                      -%}
                        <dl>
                          {%- if item.product.has_only_default_variant == false -%}
                            {%- for option in item.options_with_values -%}
                              <div class="product-option">
                                <dt>{{ option.name }}:</dt>
                                <dd>{{ option.value }}</dd>
                              </div>
                            {%- endfor -%}
                          {%- endif -%}

                          {%- for property in item.properties -%}
                            {%- assign property_first_char = property.first | slice: 0 -%}
                            {%- if property.last != blank and property_first_char != '_' -%}
                              <div class="product-option">
                                <dt>{{ property.first }}:</dt>
                                <dd>
                                  {%- if property.last contains '/uploads/' -%}
                                    <a href="{{ property.last }}" class="link" target="_blank">
                                      {{ property.last | split: '/' | last }}
                                    </a>
                                  {%- else -%}
                                    {{ property.last }}
                                  {%- endif -%}
                                </dd>
                              </div>
                            {%- endif -%}
                          {%- endfor -%}
                        </dl>

                        <p class="product-option">{{ item.selling_plan_allocation.selling_plan.name }}</p>
                      {%- endif -%}

                      <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
                        {%- for discount in item.discounts -%}
                          <li class="discounts-discount">
                            {%- render 'icon-discount' -%}
                            {{ discount.title }}
                          </li>
                        {%- endfor -%}
                      </ul>
                    </td>

                    <td class="cart-item-totals right medium-hide large-up-hide">
                      <div class="loading-overlay hidden">
                        <div class="loading-overlay-spinner">
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            class="spinner"
                            viewBox="0 0 66 66"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                          </svg>
                        </div>
                      </div>
                      <div class="cart-item-price-wrapper">
                        {%- if item.original_line_price != item.final_line_price -%}
                          <dl class="cart-item-discounted-prices">
                            <dt class="visually-hidden">
                              {{ 'products.product.price.regular_price' | t }}
                            </dt>
                            <dd>
                              <s class="cart-item-old-price price price--end">
                                {{ item.original_line_price | money }}
                              </s>
                            </dd>
                            <dt class="visually-hidden">
                              {{ 'products.product.price.sale_price' | t }}
                            </dt>
                            <dd class="price price--end">
                              {{ item.final_line_price | money }}
                            </dd>
                          </dl>
                        {%- else -%}
                          <span class="price price--end">
                            {{ item.original_line_price | money }}
                          </span>
                        {%- endif -%}

                        {%- if item.variant.available and item.unit_price_measurement -%}
                          <div class="unit-price caption">
                            <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
                            {{ item.variant.unit_price | money }}
                            <span aria-hidden="true">/</span>
                            <span class="visually-hidden"
                              >&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span
                            >
                            {%- if item.variant.unit_price_measurement.reference_value != 1 -%}
                              {{- item.variant.unit_price_measurement.reference_value -}}
                            {%- endif -%}
                            {{ item.variant.unit_price_measurement.reference_unit }}
                          </div>
                        {%- endif -%}
                      </div>
                    </td>

                    <td class="cart-item-quantity">
                      <div class="cart-item-quantity-wrapper">
                        <label class="visually-hidden" for="Quantity-{{ item.index | plus: 1 }}">
                          {{ 'products.product.quantity.label' | t }}
                        </label>
                        <quantity-input class="quantity cart-quantity">
                          <button class="quantity-button no-js-hidden" name="minus" type="button">
                            <span class="visually-hidden">
                              {{- 'products.product.quantity.decrease' | t: product: item.product.title | escape -}}
                            </span>
                            {% render 'icon-minus' %}
                          </button>
                          <input
                            class="quantity-input"
                            data-quantity-variant-id="{{ item.variant.id }}"
                            type="number"
                            name="updates[]"
                            value="{{ item.quantity }}"
                            {% # theme-check-disable %}
                            data-cart-quantity="{{ cart | item_count_for_variant: item.variant.id }}"
                            min="{{ item.variant.quantity_rule.min }}"
                            {% if item.variant.quantity_rule.max != null %}
                              max="{{ item.variant.quantity_rule.max }}"
                            {% endif %}
                            step="{{ item.variant.quantity_rule.increment }}"
                            {% # theme-check-enable %}
                            aria-label="{{ 'products.product.quantity.input_label' | t: product: item.product.title | escape }}"
                            id="Quantity-{{ item.index | plus: 1 }}"
                            data-index="{{ item.index | plus: 1 }}"
                          >
                          <button class="quantity-button no-js-hidden" name="plus" type="button">
                            <span class="visually-hidden">
                              {{- 'products.product.quantity.increase' | t: product: item.product.title | escape -}}
                            </span>
                            {% render 'icon-plus' %}
                          </button>
                        </quantity-input>

                        <cart-remove-button
                          id="Remove-{{ item.index | plus: 1 }}"
                          data-index="{{ item.index | plus: 1 }}"
                        >
                          <a
                            href="{{ item.url_to_remove }}"
                            class="button button--tertiary"
                            aria-label="{{ 'sections.cart.remove_title' | t: title: item.title }}"
                          >
                            {% render 'icon-remove' %}
                          </a>
                        </cart-remove-button>
                      </div>
                      <div class="cart-item-error" id="Line-item-error-{{ item.index | plus: 1 }}" role="alert">
                        <small class="cart-item-error-text"></small>
                        <svg
                          aria-hidden="true"
                          focusable="false"
                          class="icon icon-error"
                          viewBox="0 0 13 13"
                        >
                          <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
                          <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
                          <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
                          <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
                        </svg>
                      </div>
                    </td>

                    <td class="cart-item-totals right small-hide">
                      <div class="loading-overlay hidden">
                        <div class="loading-overlay-spinner">
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            class="spinner"
                            viewBox="0 0 66 66"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                          </svg>
                        </div>
                      </div>

                      <div class="cart-item-price-wrapper">
                        {%- if item.original_line_price != item.final_line_price -%}
                          <dl class="cart-item-discounted-prices">
                            <dt class="visually-hidden">
                              {{ 'products.product.price.regular_price' | t }}
                            </dt>
                            <dd>
                              <s class="cart-item-old-price price price--end">
                                {{ item.original_line_price | money }}
                              </s>
                            </dd>
                            <dt class="visually-hidden">
                              {{ 'products.product.price.sale_price' | t }}
                            </dt>
                            <dd class="price price--end">
                              {{ item.final_line_price | money }}
                            </dd>
                          </dl>
                        {%- else -%}
                          <span class="price price--end">
                            {{ item.original_line_price | money }}
                          </span>
                        {%- endif -%}

                        {%- if item.variant.available and item.unit_price_measurement -%}
                          <div class="unit-price caption">
                            <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
                            {{ item.variant.unit_price | money }}
                            <span aria-hidden="true">/</span>
                            <span class="visually-hidden"
                              >&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span
                            >
                            {%- if item.variant.unit_price_measurement.reference_value != 1 -%}
                              {{- item.variant.unit_price_measurement.reference_value -}}
                            {%- endif -%}
                            {{ item.variant.unit_price_measurement.reference_unit }}
                          </div>
                        {%- endif -%}
                      </div>
                    </td>
                  </tr>
                {%- endfor -%}
              </tbody>
            </table>
          {%- endif -%}
        </div>
      </div>

      <p class="visually-hidden" id="cart-live-region-text" aria-live="polite" role="status"></p>
      <p
        class="visually-hidden"
        id="shopping-cart-line-item-status"
        aria-live="polite"
        aria-hidden="true"
        role="status"
      >
        {{ 'accessibility.loading' | t }}
      </p>
    </form>

    <div class="cross-sell">
      {% if settings.enable_cross_sell %}
        <div class="cart-collection" data-aos="fade-up" data-aos-delay="250">
          <div
            class="featured-collection collection-two collection cart-items"
          >
            <div class="collection-content">
              {% comment %} Start Collection Products {% endcomment %}
              <slider-component class="slider-mobile-gutter slider-no-padding slider-component-desktop">
                <div class="drawer-collection grid">
                  <div class="cross-title cart-items">
                    <span>{{ settings.cross_sell_label }}</span>
                  </div>
                  <div class="disable-slider-arrows-false slider-buttons no-js-hidden">
                    <button
                      type="button"
                      class="slider-button slider-button--prev"
                      name="previous"
                      aria-label="{{ 'general.slider.previous_slide' | t }}"
                      aria-controls="Slider-{{ section.id }}"
                    >
                      {% render 'icon-slider-arrows' %}
                    </button>
                    <button
                      type="button"
                      class="slider-button slider-button--next"
                      name="next"
                      aria-label="{{ 'general.slider.next_slide' | t }}"
                      aria-controls="Slider-{{ section.id }}"
                    >
                      {% render 'icon-slider-arrows' %}
                    </button>
                  </div>
                </div>
                <ul
                  id="Slider-{{ section.id }}"
                  class="grid product-grid contains-card contains-card--product contains-card--standard grid--3-col-desktop grid--2-col-tablet-down grid--1-col-tablet-down slider slider--desktop slider--tablet grid--peek"
                  role="list"
                  aria-label="{{ 'general.slider.name' | t }}"
                >
                  {%- for product in settings.cross_sell_collection.products -%}
                    <li
                      id="Slide-{{ section.id }}-{{ forloop.index }}"
                      class="grid-item slider-slide"
                    >
                      {% render 'card-product-drawer',
                        card_product: product,
                        section_id: section.id,
                        show_secondary_image: true
                      %}
                    </li>
                  {%- else -%}
                    {%- for i in (1..4) -%}
                      <li class="grid-item">
                        {%- assign placeholder_image = 'product-' | append: forloop.rindex -%}
                        {% render 'card-product-drawer', placeholder_image: placeholder_image %}
                      </li>
                    {%- endfor -%}
                  {%- endfor -%}
                </ul>
              </slider-component>
              {% comment %} End Collection Products {% endcomment %}
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</cart-items>

{% schema %}
{
  "name": "t:sections.main-cart-items.name",
  "class": "cart__items-wrapper",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_left",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_left",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_right",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    }
  ]
}
{% endschema %}

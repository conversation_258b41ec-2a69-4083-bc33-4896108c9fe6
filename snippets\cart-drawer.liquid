{% comment %}
  Renders cart drawer

  Usage:
  {% render 'cart-drawer' %}
{% endcomment %}

<script src="{{ 'cart.js' | asset_url }}" defer="defer"></script>
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-rte.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'quick-add.css' | asset_url | stylesheet_tag }}

<style>
  .drawer {
    visibility: hidden;
  }
  .cart-drawer .drawer-inner .card-content-1 {
    display: none;
  }
  .cart-drawer .drawer-inner .card-media .media img:hover {
    transform: none;
  }
  cart-drawer.hide-cart-button .cart-view-button,
  cart-drawer.hide-checkout-button .cart-checkout-button {
    display: none;
  }
</style>


<cart-drawer class="drawer{% if cart == empty %} is-empty{% endif %} {% if settings.disable_cart_button == true %}hide-cart-button{% endif %} {% if settings.disable_checkout_button == true %}hide-checkout-button{% endif %}">
  <div id="CartDrawer" class="cart-drawer">
    <div id="CartDrawer-Overlay" class="cart-drawer-overlay"></div>
    <div
      class="drawer-inner"
      role="dialog"
      aria-modal="true"
      aria-label="{{ 'sections.cart.title' | t }}"
      tabindex="-1"
    >
      {%- if cart == empty -%}
      {% if settings.enable_promo_message %}
        <div class="promo-message cart-items" data-aos="fade-up" data-aos-delay="100">
          <p>{{ settings.promo_message }}</p>
        </div>
      {% endif %}
        <div class="drawer-inner-empty">
          <div class="cart-drawer-warnings center">
            <div class="cart-drawer-empty-content">
              <p class="cart-empty-text h2" data-aos="fade-up" data-aos-delay="150">{{ 'sections.cart.empty' | t }}</p>
              {% if settings.enable_empty_cart_message %}
                <div class="empty-cart-message cart-items" data-aos="fade-up" data-aos-delay="150">
                  <p>{{ settings.empty_cart_message }}</p>
                </div>
              {% endif %}
              <button
                class="drawer-close"
                type="button"
                onclick="this.closest('cart-drawer').close()"
                aria-label="{{ 'accessibility.close' | t }}"
              >
                {% render 'icon-close' %}
              </button>
              
              {% if settings.button_link %}
                <a href="{{ settings.button_link }}" class="button" data-aos="fade-up" data-aos-delay="200">
                  {{ 'general.continue_shopping' | t }}
                </a>
              {% else %}
                <a href="{{ routes.all_products_collection_url }}" class="button" data-aos="fade-up" data-aos-delay="200">
                  {{ 'general.continue_shopping' | t }}
                </a>
              {% endif %}

              {%- if shop.customer_accounts_enabled and customer == null -%}
                <p class="cart-login-title h3" data-aos="fade-up" data-aos-delay="150">{{ 'sections.cart.login.title' | t }}</p>
                <p class="cart-login-paragraph" data-aos="fade-up" data-aos-delay="150">
                  {{ 'sections.cart.login.paragraph_html' | t: link: routes.account_login_url }}
                </p>
              {%- endif -%}
            </div>
          </div>
        </div>
      {%- endif -%}
      {% if settings.enable_promo_message %}
        <div class="promo-message cart-items" data-aos="fade-up" data-aos-delay="100">
          <p>{{ settings.promo_message }}</p>
        </div>
      {% endif %}
      <div class="drawer-header" data-aos="fade-up" data-aos-delay="100">
        <span class="drawer-heading h2">{{ 'sections.cart.title' | t }}</span>
        <button
          class="drawer-close"
          type="button"
          onclick="this.closest('cart-drawer').close()"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          {% render 'icon-close' %}
        </button>
      </div>
      {% if settings.enable_free_shipping_message and cart != empty %}
        {% assign free_shipping_amount = settings.free_shipping_amount %}
        {% assign current_cart_total = cart.total_price | divided_by: 100.0 %}
        {% assign current_cart_total = current_cart_total | round: 2 %}
        {% assign message_part_before = settings.free_shipping_message | split: '*amount*' | first %}
        {% assign message_part_after = settings.free_shipping_message | split: '*amount*' | last %}
        {% assign message_part_success = settings.free_shipping_success %}
        <shipping-message class="free-shipping-message cart-items" data-shipping-message data-aos="fade-up" data-aos-delay="150" 
          data-message-before="{{ message_part_before }}"
          data-message-after="{{ message_part_after }}"
          data-message-success="{{ message_part_success }}"
          data-threshold="{{ free_shipping_amount }}"
          data-cart-total="{{ current_cart_total }}">
        <!-- Placeholder for free shipping amount -->
         <p id="free-shipping-message">
          <!-- JavaScript will dynamically populate this -->
        </p>
        </shipping-message>
      {% endif %}
      <cart-drawer-items
        {% if cart == empty %}
          class=" is-empty"
        {% endif %}
      >
        <form
          action="{{ routes.cart_url }}"
          id="CartDrawer-Form"
          class="cart-contents cart-drawer-form"
          method="post"
        >
          <div id="CartDrawer-CartItems" class="drawer-contents js-contents">
            {%- if cart != empty -%}
              <div class="drawer-cart-items-wrapper">
                <table class="cart-items" role="table">
                  <tbody role="rowgroup">
                    {%- for item in cart.items -%}
                      <tr id="CartDrawer-Item-{{ item.index | plus: 1 }}" class="cart-item" role="row" data-aos="fade-up" data-aos-delay="200">
                        <td class="cart-item-media" role="cell" headers="CartDrawer-ColumnProductImage">
                          {% if item.image %}
                            {% comment %} Leave empty space due to a:empty CSS display: none rule {% endcomment %}
                            <a href="{{ item.url }}" class="cart-item-link" tabindex="-1" aria-hidden="true"> </a>
                            <img
                              class="cart-item-image"
                              src="{{ item.image | image_url: width: 300 }}"
                              alt="{{ item.image.alt | escape }}"
                              loading="lazy"
                              width="150"
                              height="{{ 150 | divided_by: item.image.aspect_ratio | ceil }}"
                            >
                          {% endif %}
                        </td>

                        <td class="cart-item-details" role="cell" headers="CartDrawer-ColumnProduct">
                          {%- if settings.show_vendor -%}
                            <p class="caption light">{{ item.product.vendor }}</p>
                          {%- endif -%}

                          <a href="{{ item.url }}" class="cart-item-name h4 break">
                            {{- item.product.title | escape -}}
                          </a>

                          {%- if item.original_price != item.final_price -%}
                            <div class="cart-item-discounted-prices">
                              <span class="visually-hidden">
                                {{ 'products.product.price.regular_price' | t }}
                              </span>
                              <s class="cart-item-old-price product-option">
                                {{- item.original_price | money -}}
                              </s>
                              <span class="visually-hidden">
                                {{ 'products.product.price.sale_price' | t }}
                              </span>
                              <strong class="cart-item-final-price product-option">
                                {{ item.final_price | money }}
                              </strong>
                            </div>
                          {%- else -%}
                            <div class="product-option">
                              {{ item.original_price | money }}
                            </div>
                          {%- endif -%}

                          {%- if item.product.has_only_default_variant == false
                            or item.properties.size != 0
                            or item.selling_plan_allocation != null
                          -%}
                            <dl>
                              {%- if item.product.has_only_default_variant == false -%}
                                {%- for option in item.options_with_values -%}
                                  <div class="product-option">
                                    <dt>{{ option.name }}:</dt>
                                    <dd>
                                      {{ option.value -}}
                                      {%- unless forloop.last %}, {% endunless %}
                                    </dd>
                                  </div>
                                {%- endfor -%}
                              {%- endif -%}

                              {%- for property in item.properties -%}
                                {%- assign property_first_char = property.first | slice: 0 -%}
                                {%- if property.last != blank and property_first_char != '_' -%}
                                  <div class="product-option">
                                    <dt>{{ property.first }}:</dt>
                                    <dd>
                                      {%- if property.last contains '/uploads/' -%}
                                        <a
                                          href="{{ property.last }}"
                                          class="link"
                                          target="_blank"
                                          aria-describedby="a11y-new-window-message"
                                        >
                                          {{ property.last | split: '/' | last }}
                                        </a>
                                      {%- else -%}
                                        {{ property.last }}
                                      {%- endif -%}
                                    </dd>
                                  </div>
                                {%- endif -%}
                              {%- endfor -%}
                            </dl>

                            <p class="product-option">{{ item.selling_plan_allocation.selling_plan.name }}</p>
                          {%- endif -%}

                          <ul
                            class="discounts list-unstyled"
                            role="list"
                            aria-label="{{ 'customer.order.discount' | t }}"
                          >
                            {%- for discount in item.discounts -%}
                              <li class="discounts-discount">
                                {%- render 'icon-discount' -%}
                                {{ discount.title }}
                              </li>
                            {%- endfor -%}
                          </ul>
                        </td>

                        <td class="cart-item-totals right" role="cell" headers="CartDrawer-ColumnTotal">
                          <div class="loading-overlay hidden">
                            <div class="loading-overlay-spinner">
                              <svg
                                aria-hidden="true"
                                focusable="false"
                                class="spinner"
                                viewBox="0 0 66 66"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                              </svg>
                            </div>
                          </div>

                          <div class="cart-item-price-wrapper">
                            {%- if item.original_line_price != item.final_line_price -%}
                              <div class="cart-item-discounted-prices">
                                <span class="visually-hidden">
                                  {{ 'products.product.price.regular_price' | t }}
                                </span>
                                <s class="cart-item-old-price price price--end">
                                  {{ item.original_line_price | money }}
                                </s>
                                <span class="visually-hidden">
                                  {{ 'products.product.price.sale_price' | t }}
                                </span>
                                <span class="price price--end">
                                  {{ item.final_line_price | money }}
                                </span>
                              </div>
                            {%- else -%}
                              <span class="price price--end">
                                {{ item.original_line_price | money }}
                              </span>
                            {%- endif -%}

                            {%- if item.variant.available and item.unit_price_measurement -%}
                              <div class="unit-price caption">
                                <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
                                {{ item.variant.unit_price | money }}
                                <span aria-hidden="true">/</span>
                                <span class="visually-hidden"
                                  >&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span
                                >
                                {%- if item.variant.unit_price_measurement.reference_value != 1 -%}
                                  {{- item.variant.unit_price_measurement.reference_value -}}
                                {%- endif -%}
                                {{ item.variant.unit_price_measurement.reference_unit }}
                              </div>
                            {%- endif -%}
                          </div>
                        </td>

                        <td class="cart-item-quantity" role="cell" headers="CartDrawer-ColumnQuantity">
                          <div class="cart-item-quantity-wrapper">
                            <quantity-input class="quantity cart-quantity">
                              <button class="quantity-button no-js-hidden" name="minus" type="button">
                                <span class="visually-hidden">
                                  {{- 'products.product.quantity.decrease' | t: product: item.product.title | escape -}}
                                </span>
                                {% render 'icon-minus' %}
                              </button>
                              <input
                                class="quantity-input"
                                type="number"
                                data-quantity-variant-id="{{ item.variant.id }}"
                                name="updates[]"
                                value="{{ item.quantity }}"
                                {% # theme-check-disable %}
                                data-cart-quantity="{{ cart | item_count_for_variant: item.variant.id }}"
                                min="{{ item.variant.quantity_rule.min }}"
                                {% if item.variant.quantity_rule.max != null %}
                                  max="{{ item.variant.quantity_rule.max }}"
                                {% endif %}
                                step="{{ item.variant.quantity_rule.increment }}"
                                {% # theme-check-enable %}
                                aria-label="{{ 'products.product.quantity.input_label' | t: product: item.product.title | escape }}"
                                id="Drawer-quantity-{{ item.index | plus: 1 }}"
                                data-index="{{ item.index | plus: 1 }}"
                              >
                              <button class="quantity-button no-js-hidden" name="plus" type="button">
                                <span class="visually-hidden">
                                  {{- 'products.product.quantity.increase' | t: product: item.product.title | escape -}}
                                </span>
                                {% render 'icon-plus' %}
                              </button>
                            </quantity-input>

                            <cart-remove-button
                              id="CartDrawer-Remove-{{ item.index | plus: 1 }}"
                              data-index="{{ item.index | plus: 1 }}"
                            >
                              <button
                                type="button"
                                class="button button--tertiary"
                                aria-label="{{ 'sections.cart.remove_title' | t: title: item.title }}"
                              >
                                {% render 'icon-remove' %}
                              </button>
                            </cart-remove-button>
                          </div>

                          <div
                            id="CartDrawer-LineItemError-{{ item.index | plus: 1 }}"
                            class="cart-item-error"
                            role="alert"
                          >
                            <small class="cart-item-error-text"></small>
                            <svg
                              aria-hidden="true"
                              focusable="false"
                              class="icon icon-error"
                              viewBox="0 0 13 13"
                            >
                              <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
                              <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
                              <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
                              <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
                            </svg>
                          </div>
                        </td>
                      </tr>
                    {%- endfor -%}
                  </tbody>
                </table>
              </div>
            {%- endif -%}
            <p id="CartDrawer-LiveRegionText" class="visually-hidden" role="status"></p>
            <p id="CartDrawer-LineItemStatus" class="visually-hidden" aria-hidden="true" role="status">
              {{ 'accessibility.loading' | t }}
            </p>
          </div>
          <div id="CartDrawer-CartErrors" role="alert"></div>
        </form>

        {%- if cart != empty -%}
        {% if settings.enable_cross_sell %}
        <div class="cart-collection" data-aos="fade-up" data-aos-delay="250">
          <div
            class="featured-collection collection-two collection cart-items"
          >
            <div class="collection-content">
              {% comment %} Start Collection Products {% endcomment %}
              <slider-component class="slider-mobile-gutter slider-no-padding slider-component-desktop">
                <div class="drawer-collection grid">
                <div class="cross-title cart-items">
                  <span>{{ settings.cross_sell_label }}</span>
                </div>
                <div class="disable-slider-arrows-false slider-buttons no-js-hidden">
                  <button
                    type="button"
                    class="slider-button slider-button--prev"
                    name="previous"
                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                    aria-controls="Slider-{{ section.id }}"
                  >
                    {% render 'icon-slider-arrows' %}
                  </button>
                  <button
                    type="button"
                    class="slider-button slider-button--next"
                    name="next"
                    aria-label="{{ 'general.slider.next_slide' | t }}"
                    aria-controls="Slider-{{ section.id }}"
                  >
                    {% render 'icon-slider-arrows' %}
                  </button>
                </div>
                </div>
                <ul
                  id="Slider-{{ section.id }}"
                  class="grid product-grid contains-card contains-card--product contains-card--standard grid--3-col-desktop grid--2-col-tablet-down grid--1-col-tablet-down slider slider--desktop slider--tablet grid--peek"
                  role="list"
                  aria-label="{{ 'general.slider.name' | t }}"
                >
                  {%- for product in settings.cross_sell_collection.products -%}
                    <li
                      id="Slide-{{ section.id }}-{{ forloop.index }}"
                      class="grid-item slider-slide"
                    >
                      {% render 'card-product-drawer', card_product: product, section_id: section.id, show_secondary_image: true %}
                    </li>
                  {%- else -%}
                    {%- for i in (1..4) -%}
                      <li class="grid-item">
                        {%- assign placeholder_image = 'product-' | append: forloop.rindex -%}
                        {% render 'card-product-drawer', placeholder_image: placeholder_image %}
                      </li>
                    {%- endfor -%}
                  {%- endfor -%}
                </ul>
              </slider-component>
              {% comment %} End Collection Products {% endcomment %}
            </div>
          </div>
        </div>
        {% endif %}
        {% endif %}
      </cart-drawer-items>

      <div class="drawer-footer">
        {%- if settings.show_cart_note -%}
        <collapsible-row class="product__accordion accordion">
          <details id="Details-CartDrawer" {% if settings.cart_note_open %}open{% endif %}>
            <summary>
              <span class="summary-title">
                {{ 'sections.cart.note' | t }}
                {% render 'icon-caret' %}
              </span>
            </summary>
            <cart-note class="collapsible__content cart-note field">
              <label class="visually-hidden" for="CartDrawer-Note">{{ 'sections.cart.note' | t }}</label>
              <textarea
                id="CartDrawer-Note"
                class="text-area text-area--resize-vertical field-input"
                name="note"
                placeholder="{{ 'sections.cart.note' | t }}"
              >{{ cart.note }}</textarea>
            </cart-note>
          </details>
        </collapsible-row>
        {%- endif -%}

        <!-- Subtotals -->
        <div class="cart-drawer-footer" {{ block.shopify_attributes }}>
          <div class="totals" role="status">
            <span class="totals-subtotal">{{ 'sections.cart.subtotal' | t }}</span>
            <p class="totals-subtotal-value heading-bold">{{ cart.total_price | money_with_currency }}</p>
          </div>

          <div>
            {%- if cart.cart_level_discount_applications.size > 0 -%}
              <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
                {%- for discount in cart.cart_level_discount_applications -%}
                  <li class="discounts-discount discounts-discount--end">
                    {%- render 'icon-discount' -%}
                    {{ discount.title }}
                    (-{{ discount.total_allocated_amount | money }})
                  </li>
                {%- endfor -%}
              </ul>
            {%- endif -%}
          </div>

          <small class="tax-note caption-large rte">
            {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
              {{ 'sections.cart.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}
            {%- elsif cart.taxes_included -%}
              {{ 'sections.cart.taxes_included_but_shipping_at_checkout' | t }}
            {%- elsif shop.shipping_policy.body != blank -%}
              {{ 'sections.cart.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}
            {%- else -%}
              {{ 'sections.cart.taxes_and_shipping_at_checkout' | t }}
            {%- endif -%}
          </small>

        <!-- Start blocks -->
        {% if settings.enable_terms %}
          <terms-checkbox class="terms-cart">
            <input type="checkbox" id="termsCheckbox" name="termsCheckbox" value="1">
            <label for="termsCheckbox">{{ settings.terms_label }}</label>
          </terms-checkbox>
        {% endif %}
        
        </div>

        <!-- CTAs -->

        <div class="cart-ctas" {{ block.shopify_attributes }}>
          <noscript>
            <button type="submit" class="cart-update-button button button--secondary" form="CartDrawer-Form">
              {{ 'sections.cart.update' | t }}
            </button>
          </noscript>

          <button
            type="submit"
            id="CartDrawer-ViewCart"
            class="cart-view-button button button--secondary"
            form="CartDrawer-Form"
          >
            {{ 'sections.cart.view_cart' | t }}
          </button>

          <button
            type="submit"
            id="CartDrawer-Checkout"
            class="cart-checkout-button button"
            name="checkout"
            form="CartDrawer-Form"
            {% if cart == empty or settings.enable_terms %}
              disabled
            {% endif %}
          >
            {{ 'sections.cart.checkout' | t }}
          </button>
        </div>
      </div>
    </div>
  </div>
</cart-drawer>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    function isIE() {
      const ua = window.navigator.userAgent;
      const msie = ua.indexOf('MSIE ');
      const trident = ua.indexOf('Trident/');

      return msie > 0 || trident > 0;
    }

    if (!isIE()) return;
    const cartSubmitInput = document.createElement('input');
    cartSubmitInput.setAttribute('name', 'checkout');
    cartSubmitInput.setAttribute('type', 'hidden');
    document.querySelector('#cart').appendChild(cartSubmitInput);
    document.querySelector('#checkout').addEventListener('click', function (event) {
      document.querySelector('#cart').submit();
    });
  });
</script>

{{ 'tabs.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
<div class="color-{{ section.settings.color_scheme }} gradient {% if section.settings.full_width == false %}tabs-boxed{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
  <div class="tabs page-width section-{{ section.id }}-padding tabs-{{ section.settings.tabs_style }}">
    <custom-tabs>
      <ul class="color-{{ section.settings.color_scheme_1 }} gradient tab-nav" data-aos="fade-up">
        {%- for block in section.blocks -%}
          <li class="tab-item">
            <a href="#Tab-{{ block.id }}-{{ section.id }}" class="link link--text" {{ block.shopify_attributes }}>
              {{ block.settings.heading | default: block.settings.page.title }}
            </a>
          </li>
        {%- endfor -%}
      </ul>


      <div class="tab-content-wrapper">
        {% for block in section.blocks %}
          <div id="Tab-{{ block.id }}-{{ section.id }}" class="tab-content">
            <div class="{% if block.settings.hide_image == true %}hide-image{% endif %} grid">
              
              {% if block.settings.hide_image == false %}<div class="grid-item global-media-settings" data-aos="fade-up">{% endif %}
                {% if block.settings.hide_image != true and block.settings.image != blank %}
                  {%- assign tab_image_height = block.settings.tab_image_width
                    | divided_by: block.settings.tab_image.aspect_ratio
                  -%}

                  <div
                    class="footer-block__image-wrapper"
                    style="max-width: min(100%, {{ block.settings.tab_image_width }}px);"
                  >
                    {{
                      block.settings.image
                      | image_url: width: 1100
                      | image_tag:
                        loading: 'lazy',
                        widths: '50, 100, 150, 200, 300, 400, 550, 800, 1100',
                        height: tab_image_height,
                        width: block.settings.tab_image_width,
                        class: 'tab-image'
                    }}
                  </div>
                {%- else -%}
                  {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                {% endif %}
                  
              {% if block.settings.hide_image == false %}</div>{% endif %}
            
            <div class="grid-item" data-aos="fade-up">
            {{ block.settings.row_content }}
            <div
              class="button-block"
            >
              {%- if block.settings.button_label != blank -%}
                <a
                  {% if block.settings.button_link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.button_link }}"
                  {% endif %}
                  class="button-arrow button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                >
                  {{- block.settings.button_label | escape -}}
                  {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
                </a>
              {%- endif -%}
            </div>
            </div>
          </div>
          </div>
        {% endfor %}
      </div>
    </custom-tabs>
  </div>
</div>
</div>

{% schema %}
{
  "name": "t:sections.tabs.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "tabs_style",
      "options": [
        {
          "value": "style-1",
          "label": "t:sections.tabs.settings.tabs_style.options__1.label"
        },
        {
          "value": "style-2",
          "label": "t:sections.tabs.settings.tabs_style.options__2.label"
        },
        {
          "value": "style-3",
          "label": "t:sections.tabs.settings.tabs_style.options__3.label"
        }
      ],
      "default": "style-2",
      "label": "t:sections.tabs.settings.tabs_style.label"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.tabs.settings.full_width.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 96
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 96
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "tab",
      "name": "t:sections.tabs.blocks.tab.name",
      "limit": 4,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Tab",
          "label": "t:sections.tabs.blocks.tab.settings.heading.label",
          "info": "t:sections.tabs.blocks.tab.settings.heading.info"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.tabs.blocks.tab.settings.image.label"
        },
        {
          "type": "range",
          "id": "tab_image_width",
          "min": 100,
          "max": 1000,
          "step": 20,
          "default": 1000,
          "unit": "px",
          "label": "t:sections.tabs.blocks.tab.settings.tab_image_width.label"
        },
        {
          "type": "checkbox",
          "id": "hide_image",
          "default": false,
          "label": "t:sections.tabs.blocks.tab.settings.hide_image.label"
        },
        {
          "type": "richtext",
          "id": "row_content",
          "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>",
          "label": "t:sections.tabs.blocks.tab.settings.row_content.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.tabs.blocks.tab.settings.button_label.label",
          "info": "t:sections.tabs.blocks.tab.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.tabs.blocks.tab.settings.button_link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "default": false,
          "label": "t:sections.tabs.blocks.tab.settings.button_style_secondary.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.tabs.presets.name",
      "blocks": [
        {
          "type": "tab"
        },
        {
          "type": "tab"
        },
        {
          "type": "tab"
        },
        {
          "type": "tab"
        }
      ]
    }
  ]
}
{% endschema %}

{{ 'section-collection-tabs.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-rte.css' | asset_url | stylesheet_tag }}
{{ 'section-banner-two-columns.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'quick-add.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'template-collection.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
        padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
      }

      @media screen and (min-width: 750px) {
        .section-{{ section.id }}-padding {
          padding-top: {{ section.settings.padding_top }}px;
          padding-bottom: {{ section.settings.padding_bottom }}px;
        }
      }

      @media screen and (max-width: 990px) {
      .margin-spacing-negative.section-{{ section.id }}-margin {
        margin-top: -{{ section.settings.margin_top }}px;
      }
      .margin-spacing-positive.section-{{ section.id }}-margin {
        margin-top: {{ section.settings.margin_top }}px;
      }
    }

    .css-slider-dot-navigation .css-slider-dot {
      width: 9px;
      height: 9px;
      display: inline-block;
      margin: 0 5px;
      border-radius: 7px;
      transition: all .1s linear;
      overflow: hidden;
      cursor: pointer;
      background-color: #000;
      opacity: .25;
      position: relative;
    }

    .css-slider-dot-navigation .css-slider-dot.active {
      opacity: 1;
      width: 30px;
    }

    @media screen and (min-width: 750px) {
      .featured-collection.banner-two-columns .banner--height {
        min-height: {{ section.settings.banner_height_desktop }}rem;
      }
    }
    @media screen and (max-width: 750px) {
      .featured-collection.banner-two-columns .banner--height {
        min-height: {{ section.settings.banner_height_mobile }}rem;
      }
    }
{%- endstyle -%}

{%- liquid
  assign products_to_display = section.settings.products_to_show

  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and products_to_display > columns_mobile_int
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider and products_to_display > section.settings.columns_desktop
    assign show_desktop_slider = true
  endif
-%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
  <div
    id="animation"
    class="collection-tabs-section color-{{ section.settings.color_scheme }} gradient extract {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin {% if section.settings.show_secondary_image == true %}show-secondary-image{% endif %}"
  >
    <div
      class="featured-collection banner-two-columns {% if section.settings.quick_add_position == 'overlay' %} quick-add-overlay{% endif %} {% if section.settings.collection_style == 'collection-one' %} collection-one{% endif %} {% if section.settings.collection_style == 'collection-two' %} collection-two{% endif %} {% if section.settings.collection_style == 'collection-three' %} collection-three{% endif %} {% if section.settings.collection_style == 'collection-four' %} collection-four{% endif %} collection section-{{ section.id }}-padding{% if section.settings.full_width %} collection--full-width{% endif %} page-width quick-view-{{ section.settings.enable_quick_add }} enable-quick-buy-{{ section.settings.enable_quick_buy }}"
      data-aos="fade-up"
    >
      <div class="collection-content">
        {% comment %} Start Collection Products {% endcomment %}

        <div class="collection-title title-wrapper title-wrapper--no-top-margin {% if show_mobile_slider %} title-wrapper--self-padded-tablet-down{% endif %}{% if show_desktop_slider %} collection-title--desktop-slider{% endif %}">
          <div class="grid">
            <div class="grid-item {% if section.settings.content_alignment == 'left' %}left {% else %} center {% endif %}">
              {%- if section.settings.caption != blank -%}
                <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
                  {{ section.settings.caption | escape }}
                </p>
              {%- endif -%}
              {%- if section.settings.title != blank -%}
                <{{ section.settings.heading_tag }} class="title {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }} heading-bold">
                  {{- section.settings.title | escape -}}
                </{{ section.settings.heading_tag }}>
              {%- endif -%}
              {%- if section.settings.description != blank
                or section.settings.show_description
                and section.settings.collection.description != empty
              -%}
                <div class="collection-description {{ section.settings.description_style }} rte">
                  {%- if section.settings.show_description -%}
                    {{ section.settings.collection.description }}
                  {%- else -%}
                    {{ section.settings.description -}}
                  {%- endif %}
                </div>
              {%- endif -%}
            </div>
          </div>
        </div>
        {% comment %} End Title,Description & View All Link {% endcomment %}
        <tab-switcher data-section-id="{{ section.id }}">
          <div class="container">
            <div class="tabs {% if section.settings.content_alignment == 'center' %}center {% endif %}">
              {% for block in section.blocks %}
                <input
                  type="radio"
                  id="radio-{{ section.id }}-{{ forloop.index }}"
                  name="tab-{{ section.id }}"
                  {% if forloop.first %}
                    checked
                  {% endif %}
                >
                <label class="tab" for="radio-{{ section.id }}-{{ forloop.index }}">
                  {{ block.settings.tab_heading }}
                  <span class="notification">{{ block.settings.collection.products_count }}</span>
                </label>
              {% endfor %}
              <div class="glider">&nbsp;</div>
            </div>
          </div>

          {% for block in section.blocks %}
            <slider-component
              data-section-id="{{ section.id }}"
              class="slider-mobile-gutter{% if section.settings.full_width %} slider-component-full-width{% endif %} slider-no-padding page-width{% if show_desktop_slider == false and section.settings.full_width == false %} page-width-desktop{% endif %}{% if show_desktop_slider %} slider-component-desktop{% endif %} mobile-disable-quick-add--{{ section.settings.disable_quick_add }} {% if show_mobile_slider == true and show_desktop_slider == false  %} slider-buttons-desktop-hide{% endif %} {% if show_mobile_slider == false and show_desktop_slider == true %} slider-buttons-mobile-hide{% endif %}"
            >
              <div class="tab-content">
                <div
                  class="collection-{{ forloop.index }}"
                  style="{% if forloop.first %}display: block;{% else %}display: none;{% endif %}"
                >
                  {%- if show_desktop_slider -%}
                    <div class="grid-item right">
                      <div class="disable-slider-arrows-true slider-buttons no-js-hidden">
                        <button
                          type="button"
                          class="slider-button slider-button--prev"
                          name="previous"
                          aria-label="{{ 'general.slider.previous_slide' | t }}"
                          aria-controls="Slider-{{ section.id }}-{{ forloop.index }}"
                        >
                          {% render 'icon-slider-arrows' %}
                        </button>
                        <button
                          type="button"
                          class="slider-button slider-button--next"
                          name="next"
                          aria-label="{{ 'general.slider.next_slide' | t }}"
                          aria-controls="Slider-{{ section.id }}-{{ forloop.index }}"
                        >
                          {% render 'icon-slider-arrows' %}
                        </button>
                      </div>
                    </div>
                  {%- endif -%}
                  <ul
                    id="Slider-{{ section.id }}-{{ forloop.index }}"
                    class="grid product-grid contains-card contains-card--product{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--1-col-tablet-down{% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
                    role="list"
                    data-aos="fade-up"
                    aria-label="{{ 'general.slider.name' | t }}"
                  >
                    {%- for product in block.settings.collection.products limit: section.settings.products_to_show -%}
                      <li
                        id="Slide-{{ section.id }}-{{ forloop.index }}"
                        class="grid-item{% if show_mobile_slider or show_desktop_slider %} slider-slide{% endif %} {% if section.settings.enable_quick_buy == false and section.settings.enable_quick_add == false  %}enable-quick-buy-false{% endif %}"
                      >
                        {% render 'card-product',
                          card_product: product,
                          media_aspect_ratio: section.settings.image_ratio,
                          show_secondary_image: section.settings.show_secondary_image,
                          show_vendor: section.settings.show_vendor,
                          show_quick_add: section.settings.enable_quick_add,
                          show_quick_buy: section.settings.enable_quick_buy,
                          section_id: section.id
                        %}
                      </li>

                    {%- else -%}
                      {%- for i in (1..4) -%}
                        <li class="grid-item">
                          {%- assign placeholder_image = 'product-' | append: forloop.rindex -%}
                          {% render 'card-product',
                            show_vendor: section.settings.show_vendor,
                            placeholder_image: placeholder_image
                          %}
                        </li>
                      {%- endfor -%}
                    {%- endfor -%}
                  </ul>
                </div>
              </div>
            </slider-component>
          {% endfor %}
        </tab-switcher>
        {% comment %} End Collection Products {% endcomment %}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.collection-tabs.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "text",
      "id": "caption",
      "default": "Image Caption",
      "label": "t:sections.featured-collection.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "small",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Featured collection",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
       "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.featured-collection.settings.description.label"
    },
    {
      "type": "select",
      "id": "description_style",
      "label": "t:sections.featured-collection.settings.description_style.label",
      "options": [
        {
          "value": "body",
          "label": "t:sections.featured-collection.settings.description_style.options__1.label"
        },
        {
          "value": "subtitle",
          "label": "t:sections.featured-collection.settings.description_style.options__2.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.featured-collection.settings.description_style.options__3.label"
        }
      ],
      "default": "body"
    },
    {
      "type": "select",
      "id": "collection_style",
      "options": [
        {
          "value": "collection-one",
          "label": "t:sections.collection-tabs.settings.collection_style.options__1.label"
        },
        {
          "value": "collection-three",
          "label": "t:sections.collection-tabs.settings.collection_style.options__2.label"
        }
      ],
      "default": "collection-one",
      "label": "t:sections.collection-tabs.settings.collection_style.label"
    },
    {
      "type": "select",
      "id": "content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collection-tabs.settings.content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.collection-tabs.settings.content_alignment.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.collection-tabs.settings.content_alignment.label"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 25,
      "step": 1,
      "default": 4,
      "label": "t:sections.featured-collection.settings.products_to_show.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "t:sections.featured-collection.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.featured-collection.settings.full_width.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_desktop_slider",
      "label": "t:sections.featured-collection.settings.enable_desktop_slider.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.featured-collection.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.featured-collection.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.featured-collection.settings.image_ratio.options__3.label"
        }
      ],
      "default": "square",
      "label": "t:sections.featured-collection.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_add",
      "default": false,
      "label": "t:sections.featured-collection.settings.enable_quick_buy.label"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_buy",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_quick_add.label"
    },
    {
      "type": "select",
      "id": "quick_add_position",
      "options": [
        {
          "value": "overlay",
          "label": "t:sections.featured-collection.settings.quick_add_position.options__1.label"
        },
        {
          "value": "default",
          "label": "t:sections.featured-collection.settings.quick_add_position.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.featured-collection.settings.quick_add_position.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 12
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "1",
      "label": "t:sections.related-products.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.related-products.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.related-products.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "disable_quick_add",
      "default": false,
      "label": "t:sections.featured-collection.settings.disable_quick_add.label"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.featured-collection.settings.swipe_on_mobile.label"
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "t:sections.collection-tabs.blocks.collection.name",
      "limit": 3,
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collection-tabs.blocks.collection.settings.collection.label"
        },
        {
          "type": "text",
          "id": "tab_heading",
          "default": "Tab heading",
          "label": "t:sections.collection-tabs.blocks.collection.settings.tab_heading.label"
        },
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.collection-tabs.presets.name",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}

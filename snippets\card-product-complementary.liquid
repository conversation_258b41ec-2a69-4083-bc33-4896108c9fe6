{% comment %}
  Renders a product card

  Accepts:
  - card_product: {Object} Product Liquid object (optional)
  - media_aspect_ratio: {String} Size of the product image card. Values are "square" and "portrait". Default is "square" (optional)
  - show_secondary_image: {<PERSON><PERSON><PERSON>} Show the secondary image on hover. Default: false (optional)
  - show_vendor: {<PERSON><PERSON><PERSON>} Show the product vendor. Default: false
  - show_rating: {<PERSON><PERSON><PERSON>} Show the product rating. Default: false
  - extend_height: {<PERSON>olean} Card height extends to available container space. Default: true (optional)
  - lazy_load: {Boolean} Image should be lazy loaded. Default: true (optional)
  - show_quick_add: {<PERSON><PERSON><PERSON>} Show the quick add button.
  - section_id: {String} The ID of the section that contains this card.
  - horizontal_class: {<PERSON><PERSON><PERSON>} Add a card--horizontal class if set to true. Default: false (optional)
  - horizontal_quick_add: {<PERSON>ole<PERSON>} Changes the quick add button styles when set to true. Default: false (optional)

  Usage:
  {% render 'card-product', show_vendor: section.settings.show_vendor %}
{% endcomment %}

{{ 'component-rating.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .icon-container {
    display: inline-block;
  }
{%- endstyle -%}

{%- if card_product and card_product != empty -%}
  {%- liquid
    assign ratio = 1
    if card_product.featured_media and media_aspect_ratio == 'portrait'
      assign ratio = 0.8
    elsif card_product.featured_media and media_aspect_ratio == 'adapt'
      assign ratio = card_product.featured_media.aspect_ratio
    endif
    if ratio == 0 or ratio == null
      assign ratio = 1
    endif
  -%}
  <div class="card-wrapper product-card-wrapper underline-links-hover">
    <div
      class="color-{{ color_scheme }} gradient  
        card
        card--{{ settings.card_style }}
        {% if card_product.featured_media %} card--media{% else %} card--text{% endif %}
        {% if extend_height %} card--extend-height{% endif %}
        {% if card_product.featured_media == nil %} no-featured-image{% endif %}
        {% if horizontal_class %} card--horizontal{% endif %}
      "
      style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
    >
      <div
        class="card-inner image-opacity-{{ section.settings.enable_quick_add }} ratio global-media-settings"
        style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
      >
        {%- if card_product.featured_media -%}
          <div class="card-media">
            <div class="media media--transparent media--hover-effect">
              {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
              <img
                srcset="
                  {%- if card_product.featured_media.width >= 165 -%}{{ card_product.featured_media | image_url: width: 165 }} 165w,{%- endif -%}
                  {%- if card_product.featured_media.width >= 360 -%}{{ card_product.featured_media | image_url: width: 360 }} 360w,{%- endif -%}
                  {%- if card_product.featured_media.width >= 533 -%}{{ card_product.featured_media | image_url: width: 533 }} 533w,{%- endif -%}
                  {%- if card_product.featured_media.width >= 720 -%}{{ card_product.featured_media | image_url: width: 720 }} 720w,{%- endif -%}
                  {%- if card_product.featured_media.width >= 940 -%}{{ card_product.featured_media | image_url: width: 940 }} 940w,{%- endif -%}
                  {%- if card_product.featured_media.width >= 1066 -%}{{ card_product.featured_media | image_url: width: 1066 }} 1066w,{%- endif -%}
                  {{ card_product.featured_media | image_url }} {{ card_product.featured_media.width }}w
                "
                src="{{ card_product.featured_media | image_url: width: 533 }}"
                sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 130 | divided_by: 4 }}px, (min-width: 990px) calc((100vw - 130px) / 4), (min-width: 750px) calc((100vw - 120px) / 3), calc((100vw - 35px) / 2)"
                alt="{{ card_product.featured_media.alt | escape }}"
                class="motion-reduce"
                {% unless lazy_load == false %}
                  loading="lazy"
                {% endunless %}
                width="{{ card_product.featured_media.width }}"
                height="{{ card_product.featured_media.height }}"
              >
              {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}

              {%- if card_product.media[1] != null and show_secondary_image -%}
                <img
                  srcset="
                    {%- if card_product.media[1].width >= 165 -%}{{ card_product.media[1] | image_url: width: 165 }} 165w,{%- endif -%}
                    {%- if card_product.media[1].width >= 360 -%}{{ card_product.media[1] | image_url: width: 360 }} 360w,{%- endif -%}
                    {%- if card_product.media[1].width >= 533 -%}{{ card_product.media[1] | image_url: width: 533 }} 533w,{%- endif -%}
                    {%- if card_product.media[1].width >= 720 -%}{{ card_product.media[1] | image_url: width: 720 }} 720w,{%- endif -%}
                    {%- if card_product.media[1].width >= 940 -%}{{ card_product.media[1] | image_url: width: 940 }} 940w,{%- endif -%}
                    {%- if card_product.media[1].width >= 1066 -%}{{ card_product.media[1] | image_url: width: 1066 }} 1066w,{%- endif -%}
                    {{ card_product.media[1] | image_url }} {{ card_product.media[1].width }}w
                  "
                  src="{{ card_product.media[1] | image_url: width: 533 }}"
                  sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 130 | divided_by: 4 }}px, (min-width: 990px) calc((100vw - 130px) / 4), (min-width: 750px) calc((100vw - 120px) / 3), calc((100vw - 35px) / 2)"
                  alt=""
                  class="motion-reduce"
                  loading="lazy"
                  width="{{ card_product.media[1].width }}"
                  height="{{ card_product.media[1].height }}"
                >
              {%- endif -%}
            </div>
          </div>
        {%- endif -%}
      </div>

      <div class="card-information">
        {%- if show_vendor -%}
          <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
          <div class="card-vendor light">{{ card_product.vendor }}</div>
        {%- endif -%}
        <h3
          class="card-heading{% if card_product.featured_media or settings.card_style == 'standard' %} heading-bold{% endif %}"
          {% if card_product.featured_media or settings.card_style == 'card' %}
            id="title-{{ section_id }}-{{ card_product.id }}"
          {% endif %}
        >
          <a
            href="{{ card_product.url }}"
            id="CardLink-{{ section_id }}-{{ card_product.id }}"
            class="full-unstyled-link"
            aria-labelledby="CardLink-{{ section_id }}-{{ card_product.id }} Badge-{{ section_id }}-{{ card_product.id }}"
          >
            {{ card_product.title | escape }}
          </a>
        </h3>
        <div class="card-information">
          {% render 'price', product: card_product, price_class: '' %}
          <span class="caption-large light">{{ block.settings.description | escape }}</span>

          {%- if show_rating and card_product.metafields.reviews.rating.value != blank -%}
            {% liquid
              assign rating_decimal = 0
              assign decimal = card_product.metafields.reviews.rating.value.rating | modulo: 1
              if decimal >= 0.3 and decimal <= 0.7
                assign rating_decimal = 0.5
              elsif decimal > 0.7
                assign rating_decimal = 1
              endif
            %}
            <div
              class="rating"
              role="img"
              aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: card_product.metafields.reviews.rating.value, rating_max: card_product.metafields.reviews.rating.value.scale_max }}"
            >
              <span
                aria-hidden="true"
                class="rating-star color-icon-{{ settings.accent_icons }}"
                style="--rating: {{ card_product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ card_product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
              ></span>
              <p class="rating-text caption">
                <span aria-hidden="true">
                  {{- card_product.metafields.reviews.rating.value }} /
                  {{ card_product.metafields.reviews.rating.value.scale_max -}}
                </span>
              </p>
              <p class="rating-count caption">
                <span aria-hidden="true">({{ card_product.metafields.reviews.rating_count }})</span>
                <span class="visually-hidden">
                  {{- card_product.metafields.reviews.rating_count }}
                  {{ 'accessibility.total_reviews' | t -}}
                </span>
              </p>
            </div>
          {%- endif -%}

          {% assign dynamic_key = settings.card_metafield_key %}
          {% assign icons = card_product.metafields.custom[dynamic_key].value %}
          {% if card_product.metafields.custom[dynamic_key].value != blank %}
            <div class="card-icons">
              {% assign counter = 0 %}
              {% for icon in icons %}
                {% if counter < 5 %}
                  <div class="icon-container">
                    <span class="text-with-icon text-with-icon--large text-with-icon--tooltip {{ settings.card_icons_size }}">
                      <span class="text-with-icon__icon" aria-hidden="true">
                        {{ icon.image | image_url: width: 100 | image_tag }}
                      </span>
                      <span class="text-with-icon__label">
                        {{ icon.title }}
                      </span>
                    </span>
                  </div>
                {% endif %}
                {% assign counter = counter | plus: 1 %}
              {% endfor %}
            </div>
          {% endif %}
        </div>
      
        <div class="quick-add quick-add-position-{{ section.settings.quick_add_position }} no-js-hidden">
          {%- liquid
            assign product_form_id = 'quick-add-' | append: section_id | append: card_product.id
            assign qty_rules = false
            if card_product.selected_or_first_available_variant.quantity_rule.min > 1 or card_product.selected_or_first_available_variant.quantity_rule.max != null or card_product.selected_or_first_available_variant.quantity_rule.increment > 1
              assign qty_rules = true
            endif
          -%}

          {%- if show_quick_add -%}
          <modal-opener data-modal="#QuickAdd-{{ card_product.id }}">
            <button
              id="{{ product_form_id }}-submit"
              type="submit"
              name="add"
              class="quick-add__submit button {% if horizontal_quick_add %} card--horizontal__quick-add animate-arrow{% endif %}"
              aria-haspopup="dialog"
              aria-labelledby="{{ product_form_id }}-submit title-{{ section_id }}-{{ card_product.id }}"
              data-product-url="{{ card_product.url }}"
            >
              {% render 'icon-eye' %}
              {%- if horizontal_quick_add -%}
                <span class="icon-wrap">{% render 'icon-arrow' %}</span>
              {%- endif -%}
              <div class="loading-overlay-spinner hidden">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </button>
          </modal-opener>
        {%- endif -%}
          <quick-add-modal id="QuickAdd-{{ card_product.id }}" class="quick-add-modal">
            <div
              role="dialog"
              aria-label="{{ 'products.product.choose_product_options' | t: product_name: card_product.title | escape }}"
              aria-modal="true"
              class="quick-add-modal__content global-settings-popup"
              tabindex="-1"
            >
              <button
                id="ModalClose-{{ card_product.id }}"
                type="button"
                class="quick-add-modal__toggle"
                aria-label="{{ 'accessibility.close' | t }}"
              >
                {% render 'icon-close' %}
              </button>
              <div id="QuickAddInfo-{{ card_product.id }}" class="quick-add-modal__content-info"></div>
            </div>
          </quick-add-modal>

          <product-form>
            {%- form 'product',
              card_product,
              id: product_form_id,
              class: 'form',
              novalidate: 'novalidate',
              data-type: 'add-to-cart-form'
            -%}
              <input
                type="hidden"
                name="id"
                value="{{ card_product.selected_or_first_available_variant.id }}"
                disabled
              >
              <button
                id="{{ product_form_id }}-submit"
                type="submit"
                name="add"
                class="product-form__submit button button--secondary"
                aria-haspopup="dialog"
                aria-labelledby="{{ product_form_id }}-submit title-{{ section_id }}-{{ card_product.id }}"
                aria-live="polite"
                data-sold-out-message="true"
                {% if card_product.selected_or_first_available_variant.available == false %}
                  disabled
                {% endif %}
              >
                <span>
                  {%- if card_product.selected_or_first_available_variant.available -%}
                    {% render 'icon-plus' %}
                  {%- else -%}
                    {% render 'icon-sold-out' %}
                  {%- endif -%}
                </span>
                <span class="sold-out-message hidden">
                  {{ 'products.product.sold_out' | t }}
                </span>

                <div class="loading-overlay-spinner hidden">
                  <svg
                    aria-hidden="true"
                    focusable="false"
                    class="spinner"
                    viewBox="0 0 66 66"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                  </svg>
                </div>
              </button>
            {%- endform -%}
          </product-form>
        </div>
      
      </div>


    </div>
  </div>
{%- else -%}
  <div class="product-card-wrapper card-wrapper underline-links-hover onboarding">
    <div class="{% if section.settings.collection_style == 'collection-three' %}color-{{ section.settings.color_scheme_1 }} gradient{% endif %}
        color-{{ section.settings.color_scheme_1 }} gradient card">
      {%- if placeholder_image -%}
        {{ placeholder_image | placeholder_svg_tag: 'placeholder-svg' }}
      {%- else -%}
        {{ 'product-4' | placeholder_svg_tag: 'placeholder-svg' }}
      {% endif %}
      <div class="card-information">
        <h3 class="card-heading{% if settings.card_style == 'standard' %} heading-bold{% endif %}">
          <a role="link" aria-disabled="true" class="full-unstyled-link">
            {{ 'onboarding.product_title' | t }}
          </a>
        </h3>
        <div class="card-information-one">
          {%- if show_vendor -%}
            <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
            <div class="caption-with-letter-spacing light">{{ 'products.product.vendor' | t }}</div>
          {%- endif -%}
          {% render 'price' %}
        </div>
      </div>
    </div>
  </div>
{%- endif -%}

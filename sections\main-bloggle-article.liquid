
{% if localization.language.iso_code == "fr" or localization.language.iso_code contains "fr-" %}
    {% capture goback %} Retournez en arrière {% endcapture %}
    {% capture lastArticles %} Dernières nouvelles {% endcapture %}
    {% capture author %} Écrit par : {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Temps de lecture {% endcapture %}
    {% capture productSideBar %} Produit associé {% endcapture %}
    {% capture ctaText %} Voir le produit {% endcapture %}
    {% capture commentsListTitle %} Commentaires {% endcapture %}
    {% capture commentsTitle %} Laisser un commentaire {% endcapture %}
    {% capture commentNamePlaceholder %} Jean <PERSON> {% endcapture %}
    {% capture commentNameLabel %} Votre nom {% endcapture %}
    {% capture commentNameError %} Veuillez saisir un nom {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Votre e-mail {% endcapture %}
    {% capture commentEmailError %} Veuillez saisir un e-mail valide {% endcapture %}
    {% capture commentMessagePlaceholder %} Votre commentaire {% endcapture %}
    {% capture commentMessageLabel %} Votre commentaire {% endcapture %}
    {% capture commentMessageError %} Veuillez saisir un commentaire {% endcapture %}
    {% capture commentButton %} Envoyer {% endcapture %}
    {% capture commentSuccessMessage %} Votre commentaire a été ajouté avec succès {% endcapture %}
    {% capture commentErrorMessage %} Une erreur s'est produite lors de l'envoi de votre commentaire. {% endcapture %}
{% elsif localization.language.iso_code == "en" or localization.language.iso_code contains "en-" %}
    {% capture goback %} Go back {% endcapture %}
    {% capture lastArticles %} Latest News {% endcapture %}
    {% capture author %} Written by: {% endcapture %}
    {% capture datePublished %} Published on {% endcapture %}
    {% capture dateModified %} Last updated on {% endcapture %}
    {% capture timeToRead %} Time to read {% endcapture %}
    {% capture productSideBar %} Related product {% endcapture %}
    {% capture ctaText %} See Product {% endcapture %}
    {% capture commentsListTitle %} Comments {% endcapture %}
    {% capture commentsTitle %} Leave a comment {% endcapture %}
    {% capture commentNamePlaceholder %} John Doe {% endcapture %}
    {% capture commentNameLabel %} Your name {% endcapture %}
    {% capture commentNameError %} Please enter a name {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Your email {% endcapture %}
    {% capture commentEmailError %} Please enter a valid email {% endcapture %}
    {% capture commentMessagePlaceholder %} Your comment {% endcapture %}
    {% capture commentMessageLabel %} Your comment {% endcapture %}
    {% capture commentMessageError %} Please enter a comment {% endcapture %}
    {% capture commentButton %} Submit {% endcapture %}
    {% capture commentSuccessMessage %} Your comment has been successfully added {% endcapture %}
    {% capture commentErrorMessage %} An error occurred while submitting your comment. {% endcapture %}
{% elsif localization.language.iso_code == "es" or localization.language.iso_code contains "es-" %}
    {% capture goback %} Vuelve atrás {% endcapture %}
    {% capture lastArticles %} Últimas noticias {% endcapture %}
    {% capture author %} Escrito por: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Tiempo de lectura {% endcapture %}
    {% capture productSideBar %} Producto relacionado {% endcapture %}
    {% capture ctaText %} Ver producto {% endcapture %}
    {% capture commentsListTitle %} Comentarios {% endcapture %}
    {% capture commentsTitle %} Deja un comentario {% endcapture %}
    {% capture commentNamePlaceholder %} Juan Pérez {% endcapture %}
    {% capture commentNameLabel %} Tu nombre {% endcapture %}
    {% capture commentNameError %} Por favor, introduce un nombre {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Tu correo electrónico {% endcapture %}
    {% capture commentEmailError %} Por favor, introduce un correo electrónico válido {% endcapture %}
    {% capture commentMessagePlaceholder %} Tu comentario {% endcapture %}
    {% capture commentMessageLabel %} Tu comentario {% endcapture %}
    {% capture commentMessageError %} Por favor, introduce un comentario {% endcapture %}
    {% capture commentButton %} Enviar {% endcapture %}
    {% capture commentSuccessMessage %} Tu comentario ha sido añadido con éxito {% endcapture %}
    {% capture commentErrorMessage %} Se produjo un error al enviar tu comentario. {% endcapture %}
{% elsif localization.language.iso_code == "pt"or localization.language.iso_code contains "pt-" %}
    {% capture goback %} Volte para trás {% endcapture %}
    {% capture lastArticles %} Últimas notícias {% endcapture %}
    {% capture author %} Escrito por: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Tempo de leitura {% endcapture %}
    {% capture productSideBar %} Produto relacionado {% endcapture %}
    {% capture ctaText %} Ver produto {% endcapture %}
    {% capture commentsListTitle %} Comentários {% endcapture %}
    {% capture commentsTitle %} Deixe um comentário {% endcapture %}
    {% capture commentNamePlaceholder %} João Silva {% endcapture %}
    {% capture commentNameLabel %} Seu nome {% endcapture %}
    {% capture commentNameError %} Por favor, insira um nome {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Seu e-mail {% endcapture %}
    {% capture commentEmailError %} Por favor, insira um e-mail válido {% endcapture %}
    {% capture commentMessagePlaceholder %} Seu comentário {% endcapture %}
    {% capture commentMessageLabel %} Seu comentário {% endcapture %}
    {% capture commentMessageError %} Por favor, insira um comentário {% endcapture %}
    {% capture commentButton %} Enviar {% endcapture %}
    {% capture commentSuccessMessage %} Seu comentário foi adicionado com sucesso {% endcapture %}
    {% capture commentErrorMessage %} Ocorreu um erro ao enviar seu comentário. {% endcapture %}
{% elsif localization.language.iso_code == "it" or localization.language.iso_code contains "it-" %}
    {% capture goback %} Torna indietro {% endcapture %}
    {% capture lastArticles %} Ultime notizie {% endcapture %}
    {% capture author %} Scritto da: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Tempo di lettura {% endcapture %}
    {% capture productSideBar %} Prodotto correlato {% endcapture %}
    {% capture ctaText %} Vedi prodotto {% endcapture %}
    {% capture commentsListTitle %} Commenti {% endcapture %}
    {% capture commentsTitle %} Lascia un commento {% endcapture %}
    {% capture commentNamePlaceholder %} Mario Rossi {% endcapture %}
    {% capture commentNameLabel %} Il tuo nome {% endcapture %}
    {% capture commentNameError %} Per favore, inserisci un nome {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} La tua email {% endcapture %}
    {% capture commentEmailError %} Per favore, inserisci un'email valida {% endcapture %}
    {% capture commentMessagePlaceholder %} Il tuo commento {% endcapture %}
    {% capture commentMessageLabel %} Il tuo commento {% endcapture %}
    {% capture commentMessageError %} Per favore, inserisci un commento {% endcapture %}
    {% capture commentButton %} Invia {% endcapture %}
    {% capture commentSuccessMessage %} Il tuo commento è stato aggiunto con successo {% endcapture %}
    {% capture commentErrorMessage %} Si è verificato un errore durante l'invio del tuo commento. {% endcapture %}
{% elsif localization.language.iso_code == "de" or localization.language.iso_code contains "de-" %}
    {% capture goback %} Geh zurück {% endcapture %}
    {% capture lastArticles %} Aktuelle Nachrichten {% endcapture %}
    {% capture author %} Geschrieben von: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Lesezeit {% endcapture %}
    {% capture productSideBar %} Verwandtes Produkt {% endcapture %}
    {% capture ctaText %} Produkt ansehen {% endcapture %}
    {% capture commentsListTitle %} Kommentare {% endcapture %}
    {% capture commentsTitle %} Hinterlasse einen Kommentar {% endcapture %}
    {% capture commentNamePlaceholder %} Max Mustermann {% endcapture %}
    {% capture commentNameLabel %} Ihr Name {% endcapture %}
    {% capture commentNameError %} Bitte geben Sie einen Namen ein {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Ihre E-Mail {% endcapture %}
    {% capture commentEmailError %} Bitte geben Sie eine gültige E-Mail ein {% endcapture %}
    {% capture commentMessagePlaceholder %} Ihr Kommentar {% endcapture %}
    {% capture commentMessageLabel %} Ihr Kommentar {% endcapture %}
    {% capture commentMessageError %} Bitte geben Sie einen Kommentar ein {% endcapture %}
    {% capture commentButton %} Absenden {% endcapture %}
    {% capture commentSuccessMessage %} Ihr Kommentar wurde erfolgreich hinzugefügt {% endcapture %}
    {% capture commentErrorMessage %} Beim Senden Ihres Kommentars ist ein Fehler aufgetreten. {% endcapture %}
{% elsif localization.language.iso_code == "sv" or localization.language.iso_code contains "sv-" %}
    {% capture goback %} Gå tillbaka {% endcapture %}
    {% capture lastArticles %} Senaste nyheter {% endcapture %}
    {% capture author %} Skriven av: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Tid att läsa {% endcapture %}
    {% capture productSideBar %} Relaterad produkt {% endcapture %}
    {% capture ctaText %} Se produkt {% endcapture %}
    {% capture commentsListTitle %} Kommentarer {% endcapture %}
    {% capture commentsTitle %} Lämna en kommentar {% endcapture %}
    {% capture commentNamePlaceholder %} John Andersson {% endcapture %}
    {% capture commentNameLabel %} Ditt namn {% endcapture %}
    {% capture commentNameError %} Var vänlig ange ett namn {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Din e-post {% endcapture %}
    {% capture commentEmailError %} Var vänlig ange en giltig e-post {% endcapture %}
    {% capture commentMessagePlaceholder %} Din kommentar {% endcapture %}
    {% capture commentMessageLabel %} Din kommentar {% endcapture %}
    {% capture commentMessageError %} Var vänlig ange en kommentar {% endcapture %}
    {% capture commentButton %} Skicka {% endcapture %}
    {% capture commentSuccessMessage %} Din kommentar har lagts till framgångsrikt {% endcapture %}
    {% capture commentErrorMessage %} Ett fel uppstod när din kommentar skickades. {% endcapture %}
{% elsif localization.language.iso_code == "nl" or localization.language.iso_code contains "nl-" %}
    {% capture goback %} Ga terug {% endcapture %}
    {% capture lastArticles %} Laatste nieuws {% endcapture %}
    {% capture author %} Geschreven door: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Tijd om te lezen {% endcapture %}
    {% capture productSideBar %} Gerelateerd product {% endcapture %}
    {% capture ctaText %} Bekijk product {% endcapture %}
    {% capture commentsListTitle %} Reacties {% endcapture %}
    {% capture commentsTitle %} Laat een reactie achter {% endcapture %}
    {% capture commentNamePlaceholder %} Jan Jansen {% endcapture %}
    {% capture commentNameLabel %} Uw naam {% endcapture %}
    {% capture commentNameError %} Vul alstublieft een naam in {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Uw e-mail {% endcapture %}
    {% capture commentEmailError %} Vul alstublieft een geldig e-mailadres in {% endcapture %}
    {% capture commentMessagePlaceholder %} Uw reactie {% endcapture %}
    {% capture commentMessageLabel %} Uw reactie {% endcapture %}
    {% capture commentMessageError %} Vul alstublieft een reactie in {% endcapture %}
    {% capture commentButton %} Versturen {% endcapture %}
    {% capture commentSuccessMessage %} Uw reactie is succesvol toegevoegd {% endcapture %}
    {% capture commentErrorMessage %} Er is een fout opgetreden bij het versturen van uw reactie. {% endcapture %}
{% elsif localization.language.iso_code == "no" or localization.language.iso_code contains "no-" %}
    {% capture goback %} Gå tilbake {% endcapture %}
    {% capture lastArticles %} Siste nyheter {% endcapture %}
    {% capture author %} Skrevet av: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Tid å lese {% endcapture %}
    {% capture productSideBar %} Relatert produkt {% endcapture %}
    {% capture ctaText %} Se produkt {% endcapture %}
    {% capture commentsListTitle %} Kommentarer {% endcapture %}
    {% capture commentsTitle %} Legg igjen en kommentar {% endcapture %}
    {% capture commentNamePlaceholder %} Ola Nordmann {% endcapture %}
    {% capture commentNameLabel %} Ditt navn {% endcapture %}
    {% capture commentNameError %} Vennligst skriv inn et navn {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Din e-post {% endcapture %}
    {% capture commentEmailError %} Vennligst skriv inn en gyldig e-post {% endcapture %}
    {% capture commentMessagePlaceholder %} Din kommentar {% endcapture %}
    {% capture commentMessageLabel %} Din kommentar {% endcapture %}
    {% capture commentMessageError %} Vennligst skriv inn en kommentar {% endcapture %}
    {% capture commentButton %} Send {% endcapture %}
    {% capture commentSuccessMessage %} Din kommentar ble lagt til {% endcapture %}
    {% capture commentErrorMessage %} En feil oppstod under innsending av din kommentar. {% endcapture %}
{% elsif localization.language.iso_code == "zh" or localization.language.iso_code contains "zh-" %}
    {% capture goback %} 回去 {% endcapture %}
    {% capture lastArticles %} 最新资讯 {% endcapture %}
    {% capture author %} 作者： {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} 阅读时间 {% endcapture %}
    {% capture productSideBar %} 相关产品 {% endcapture %}
    {% capture ctaText %} 查看产品 {% endcapture %}
    {% capture commentsListTitle %} 评论 {% endcapture %}
    {% capture commentsTitle %} 发表评论 {% endcapture %}
    {% capture commentNamePlaceholder %} 张三 {% endcapture %}
    {% capture commentNameLabel %} 您的名字 {% endcapture %}
    {% capture commentNameError %} 请输入姓名 {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} 您的电子邮件 {% endcapture %}
    {% capture commentEmailError %} 请输入有效的电子邮件 {% endcapture %}
    {% capture commentMessagePlaceholder %} 您的评论 {% endcapture %}
    {% capture commentMessageLabel %} 您的评论 {% endcapture %}
    {% capture commentMessageError %} 请输入评论 {% endcapture %}
    {% capture commentButton %} 提交 {% endcapture %}
    {% capture commentSuccessMessage %} 您的评论已成功添加 {% endcapture %}
    {% capture commentErrorMessage %} 提交评论时发生错误。 {% endcapture %}
{% elsif localization.language.iso_code == "ja" or localization.language.iso_code contains "ja-" %}
    {% capture goback %} 戻る {% endcapture %}
    {% capture lastArticles %} 最新ニュース {% endcapture %}
    {% capture author %} 執筆者: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} 読む時間 {% endcapture %}
    {% capture productSideBar %} 関連製品 {% endcapture %}
    {% capture ctaText %} 商品を見る {% endcapture %}
    {% capture commentsListTitle %} コメント {% endcapture %}
    {% capture commentsTitle %} コメントを残す {% endcapture %}
    {% capture commentNamePlaceholder %} 田中太郎 {% endcapture %}
    {% capture commentNameLabel %} お名前 {% endcapture %}
    {% capture commentNameError %} 名前を入力してください {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} メールアドレス {% endcapture %}
    {% capture commentEmailError %} 有効なメールアドレスを入力してください {% endcapture %}
    {% capture commentMessagePlaceholder %} コメント {% endcapture %}
    {% capture commentMessageLabel %} コメント {% endcapture %}
    {% capture commentMessageError %} コメントを入力してください {% endcapture %}
    {% capture commentButton %} 送信 {% endcapture %}
    {% capture commentSuccessMessage %} コメントが正常に追加されました {% endcapture %}
    {% capture commentErrorMessage %} コメントの送信中にエラーが発生しました。 {% endcapture %}
{% elsif localization.language.iso_code == "ar" or localization.language.iso_code contains "ar-" %}
    {% capture goback %} ارجع {% endcapture %}
    {% capture lastArticles %} آخر الأخبار {% endcapture %}
    {% capture author %} كتب بواسطة: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} وقت القراءة {% endcapture %}
    {% capture productSideBar %} المنتج ذو الصلة {% endcapture %}
    {% capture ctaText %} عرض المنتج {% endcapture %}
    {% capture commentsListTitle %} التعليقات {% endcapture %}
    {% capture commentsTitle %} اترك تعليقا {% endcapture %}
    {% capture commentNamePlaceholder %} محمد علي {% endcapture %}
    {% capture commentNameLabel %} اسمك {% endcapture %}
    {% capture commentNameError %} الرجاء إدخال الاسم {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} بريدك الإلكتروني {% endcapture %}
    {% capture commentEmailError %} الرجاء إدخال بريد إلكتروني صالح {% endcapture %}
    {% capture commentMessagePlaceholder %} تعليقك {% endcapture %}
    {% capture commentMessageLabel %} تعليقك {% endcapture %}
    {% capture commentMessageError %} الرجاء إدخال تعليق {% endcapture %}
    {% capture commentButton %} إرسال {% endcapture %}
    {% capture commentSuccessMessage %} تمت إضافة تعليقك بنجاح {% endcapture %}
    {% capture commentErrorMessage %} حدث خطأ أثناء إرسال تعليقك. {% endcapture %}
 {% elsif localization.language.iso_code == "pl" or localization.language.iso_code contains "pl-" %}
    {% capture goback %} Wróć {% endcapture %}
    {% capture lastArticles %} Najnowsze wiadomości {% endcapture %}
    {% capture author %} Napisane przez: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Czas czytania {% endcapture %}
    {% capture productSideBar %} Powiązany produkt {% endcapture %}
    {% capture ctaText %} Zobacz produkt {% endcapture %}
    {% capture commentsListTitle %} Komentarze {% endcapture %}
    {% capture commentsTitle %} Dodaj komentarz {% endcapture %}
    {% capture commentNamePlaceholder %} Jan Kowalski {% endcapture %}
    {% capture commentNameLabel %} Twoje imię {% endcapture %}
    {% capture commentNameError %} Wprowadź imię {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Twój e-mail {% endcapture %}
    {% capture commentEmailError %} Wprowadź prawidłowy adres e-mail {% endcapture %}
    {% capture commentMessagePlaceholder %} Twój komentarz {% endcapture %}
    {% capture commentMessageLabel %} Twój komentarz {% endcapture %}
    {% capture commentMessageError %} Wprowadź komentarz {% endcapture %}
    {% capture commentButton %} Wyślij {% endcapture %}
    {% capture commentSuccessMessage %} Twój komentarz został pomyślnie dodany {% endcapture %}
    {% capture commentErrorMessage %} Wystąpił błąd podczas przesyłania komentarza. {% endcapture %}
     {% elsif localization.language.iso_code == "da" or localization.language.iso_code contains "da-" %}
    {% capture goback %} Gå tilbage {% endcapture %}
    {% capture lastArticles %} Seneste nyheder {% endcapture %}
    {% capture author %} Skrevet af: {% endcapture %}
    {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %} Tid til at læse {% endcapture %}
    {% capture productSideBar %} Relateret produkt {% endcapture %}
    {% capture ctaText %} Se produkt {% endcapture %}
    {% capture commentsListTitle %} Kommentarer {% endcapture %}
    {% capture commentsTitle %} Efterlad en kommentar {% endcapture %}
    {% capture commentNamePlaceholder %} John Doe {% endcapture %}
    {% capture commentNameLabel %} Dit navn {% endcapture %}
    {% capture commentNameError %} Indtast venligst et navn {% endcapture %}
    {% capture commentEmailPlaceholder %} <EMAIL> {% endcapture %}
    {% capture commentEmailLabel %} Din e-mail {% endcapture %}
    {% capture commentEmailError %} Indtast venligst en gyldig e-mail {% endcapture %}
    {% capture commentMessagePlaceholder %} Din kommentar {% endcapture %}
    {% capture commentMessageLabel %} Din kommentar {% endcapture %}
    {% capture commentMessageError %} Indtast venligst en kommentar {% endcapture %}
    {% capture commentButton %} Indsend {% endcapture %}
    {% capture commentSuccessMessage %} Din kommentar er blevet tilføjet med succes {% endcapture %}
    {% capture commentErrorMessage %} Der opstod en fejl ved indsendelse af din kommentar. {% endcapture %}
     {% else %}
    {% capture goback %}  {% endcapture %}
    {% capture lastArticles %}  {% endcapture %}
    {% capture author %}  {% endcapture %}
     {% capture datePublished %}  {% endcapture %}
    {% capture dateModified %}  {% endcapture %}
    {% capture timeToRead %}  {% endcapture %}
    {% capture productSideBar %}  {% endcapture %}
    {% capture ctaText %}  {% endcapture %}
    {% capture commentsListTitle %}  {% endcapture %}
    {% capture commentsTitle %}  {% endcapture %}
    {% capture commentNamePlaceholder %}  {% endcapture %}
    {% capture commentNameLabel %}  {% endcapture %}
    {% capture commentNameError %}  {% endcapture %}
    {% capture commentEmailPlaceholder %}  {% endcapture %}
    {% capture commentEmailLabel %}  {% endcapture %}
    {% capture commentEmailError %}  {% endcapture %}
    {% capture commentMessagePlaceholder %}  {% endcapture %}
    {% capture commentMessageLabel %}  {% endcapture %}
    {% capture commentMessageError %}  {% endcapture %}
    {% capture commentButton %}  {% endcapture %}
    {% capture commentSuccessMessage %}  {% endcapture %}
    {% capture commentErrorMessage %}  {% endcapture %}
{% endif %}
    {% capture titleSideBarClass %} undefined {% endcapture %}
  <style>
    .bloggle_rte p:not(.bggle-font) {
      font-size: 14px;
      line-height: 1.6em;
    }
    .bloggle_rte a,.bloggle_rte li,.bloggle_rte ul,.bloggle_rte strong, .bloggle_rte span, .bloggle_rte ol {
      font-size: inherit;
      line-height: inherit;
    }
  </style>
  <script async src="https://d2xvgzwm836rzd.cloudfront.net/lazysizes-bloggle.min.js"></script>
  <link href="https://d2xvgzwm836rzd.cloudfront.net/article-layout.min.css" type="text/css" rel="stylesheet">
  <div id="bloggle_article-layout" class="sideBar__right{%if localization.language.iso_code == "ar" or localization.language.iso_code == "he" %} blggle-rtl{%endif%}">
  <div id="bloggle_article" >
      <div id="article" class="flex-column-reverse">
      {% if article.image %}
      <img class="bloggle--blog-item-img lazybloggle" data-src="{{ article.image | img_url:'1200x' }}" alt="{{ article.image.alt }}">
      {% endif %}
    <div id="article-header" class="text__center">
      
       
      <h1 id="bloggle--article-title" class="">{{article.title }}</h1>
      <div class="info_header">
        <p id="bloggle--blog-item-author" >{{author}}<span id="author"> {{ article.author }}</span></p>
        <p id="separator">|</p>
         <p id="bloggle--blog-item-author" >{{datePublished}} <span>{{ article.published_at | time_tag: format: 'date'}}</span></p>
          
           
        <p id="separator">|</p>
        {% assign wordCount = article.content | strip_html | split: ' ' | size %}
               {% if wordCount %}
                 {% assign timeToReadMin = wordCount | divided_by: 225 %}
                 <p id="bloggle--blog-item-ttread" >{{ timeToRead }} <span id='time'>{{ timeToReadMin }}</span> min</p>
               {% endif %}
      </div>
    </div>
  </div>
    <div class="bloggle_rte rte" id="bloggle_rte">
      {{ article.content }}
    <div id="bloggle--blog-item-goback" class="bloggle--goback-bottom">
         <a href="{{blog.url}}">&#8592 {{goback}}</a>
      </div>
    </div>

          <div id="share-buttons" class="share-buttons" >
        <div class="twitter-share">
          <svg style="margin-right: 2px;color: #000000;width: 28px;" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="svg5" x="0px" y="0px" viewBox="0 0 1668.56 1221.19" xml:space="preserve"> <g id="layer1" transform="translate(52.390088,-25.058597)"><path fill="#000000" id="path1009" d="M283.94,167.31l386.39,516.64L281.5,1104h87.51l340.42-367.76L984.48,1104h297.8L874.15,558.3l361.92-390.99   h-87.51l-313.51,338.7l-253.31-338.7H283.94z M412.63,231.77h136.81l604.13,807.76h-136.81L412.63,231.77z"/></g> </svg>
          <a target='_blank' href="https://twitter.com/share?url={{shop.url}}{{article.url}}&text=Check%20out%20this%20post%21%20" class="twitter-share-button-bloggle" data-text="Look what I just read" data-show-count="false">Tweet</a>
        </div>
        <div class="facebook-share">
           <svg class="custom--icon" xmlns="http://www.w3.org/2000/svg" width="12.135" height="22.446" viewBox="0 0 12.135 22.446"><path style="fill:#000000;" d="M33.755,0,30.845,0c-3.27,0-5.383,2.168-5.383,5.524V8.071H22.535a.458.458,0,0,0-.458.458v3.69a.458.458,0,0,0,.458.458h2.927v9.312a.458.458,0,0,0,.458.458h3.818a.458.458,0,0,0,.458-.458V12.677h3.422a.458.458,0,0,0,.458-.458V8.529a.458.458,0,0,0-.458-.458H30.2V5.912c0-1.038.247-1.565,1.6-1.565h1.961a.458.458,0,0,0,.457-.458V.462A.458.458,0,0,0,33.755,0Z" transform="translate(-22.077)"/></svg>
          <a target='_blank' href="https://www.facebook.com/share.php?u={{shop.url}}{{article.url}}">Share</a>
        </div>
        <div class="pinterest-share">
          <svg class="custom--icon" xmlns="http://www.w3.org/2000/svg" width="15.463" height="19.032" viewBox="0 0 15.463 19.032"><path style="fill:#000000;" d="M10.24,0C5.023,0,2.25,3.343,2.25,6.988c0,1.69.944,3.8,2.457,4.467.431.194.374-.043.745-1.462a.337.337,0,0,0-.081-.331c-2.162-2.5-.422-7.641,4.561-7.641C17.143,2.021,15.8,12,11.186,12A1.7,1.7,0,0,1,9.393,9.913a22.6,22.6,0,0,0,1-3.842c0-2.5-3.719-2.126-3.719,1.182A4,4,0,0,0,7.04,8.964S5.843,13.8,5.621,14.7a12.829,12.829,0,0,0,.088,4.217.125.125,0,0,0,.228.058,14.955,14.955,0,0,0,1.97-3.71c.147-.543.753-2.748.753-2.748a3.288,3.288,0,0,0,2.774,1.323c3.645,0,6.279-3.2,6.279-7.179C17.7,2.852,14.439,0,10.24,0Z" transform="translate(-2.25)"/></svg>
            <a target='_blank' href="https://www.pinterest.com/pin/create/button/?url={{shop.url}}{{article.url}}">Pin it</a>
        </div>
      </div>
  </div>

  
  </div>


  <style>
      .bggle--icon {
        width: 20px;
        height: 20px;
        fill: black;
      }
      #bloggle_sideBar{
      padding: 0px;
      top: 0px;
      background: #ffffff;
      }
      @media screen and (min-width: 768px) {
        #bloggle_sideBar .bggle--product-info {
          margin-top: 15px;
        }
      }
      #bloggle_sideBar .bggle--product-info p, #bloggle_sideBar p, #bloggle_sideBar li{
        color: #000000;
     }
      .bloggle--blog-item-img {
        width: 100%;
         height: 350px;
        object-fit: cover;
        transition: all 0.3s ease;
      }
      .bggle--product-item-img{
        object-fit: contain!important;
      }
      .bggle-latest-article{
        margin-bottom:20px
        }
      #bloggle_article-layout #bloggle_article {
        width: 100%;}
  
      #bloggle_article-layout {
        width: 1100px !important;
        max-width: 100% !important;
        margin-top: 20px;
      }
      

      #bloggle_comment-layout{
        width: 1100px;
        max-width: 100%;
      }
      .bggle--success-message {
        background: #CBEBC8;
        padding: 5px 10px;
      }
      .bggle--form-error {
        background: #FFB6B6;
        padding: 5px 10px;
      }
      .bggle--product-vendor{
        font-weight: bold;
        font-size:12;
      }

      .bggle--product-price{ font-size:14;}
      .bggle--product-title{ font-size:18;}

      @media (max-width: 768px) {
      .bloggle--blog-item-img {
          height: 300px;
      }
      #bloggle_sideBar{
        height: 120px;
        transform: translateY(120px);
        transition: all 0.3s ease;
      }
      }
       

      @media screen and (min-width: 769px) {
        .bloggle_sideBar-V2 .bggle--info-siderBar-product img {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
            max-height:150px;
        }
       .bloggle_sideBar-V2 .bggle--product-info {
          margin-top: 10px;
          width: 100%;
        }

        .bloggle_sideBar-V2 .bggle--info-siderBar-product {
          flex-direction: column !important;
        }
        .bloggle_sideBar-V2 .bggle-latest-article-img{
          width: 100% !important;
        }
      }

  </style>
<script>
  function handleScroll() {
    const sidebar = document.querySelector('#bloggle_sideBar');
    const product = document.querySelector('#bggle--product-related');
    const isSmallScreen = window.innerWidth < 768;
    if(isSmallScreen && product){
      if (window.scrollY > 120) {
      sidebar.style.transform = 'translateY(0px)';
    } else {
      sidebar.style.transform = 'translateY(120px)';
    }
    }
  }
  window.addEventListener('scroll', handleScroll);
</script>


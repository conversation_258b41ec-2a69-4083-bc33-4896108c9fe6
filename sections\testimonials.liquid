{{ 'section-testimonials.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider
    assign show_desktop_slider = true
  endif
-%}

<div class="color-{{ section.settings.color_scheme }} gradient ignore-{{ section.settings.ignore_spacing }}">
  <div class="{% if section.settings.title == blank %} no-heading{% endif %} testimonials-{{ section.settings.testimonials_style }} section-{{ section.id }}-padding {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
    <div
      class="testimonial-section page-width extract"
      data-aos="fade-up"
    >
      <slider-component class="slider-mobile-gutter {% if show_desktop_slider %} slider-component-desktop{% endif %}  {% if show_mobile_slider == true and show_desktop_slider == false  %} slider-buttons-desktop-hide{% endif %} {% if show_mobile_slider == false and show_desktop_slider == true  %} slider-buttons-mobile-hide{% endif %}">
        <div class="collection-info grid">
          <div class="grid_item">
            {%- unless section.settings.title == blank -%}
              <div class="testimonials-title">
                <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
                  {{ section.settings.caption | escape }}
                </p>
                <{{ section.settings.heading_tag }} class="title {{ section.settings.heading_size }} heading-bold">
                  {{ section.settings.title | escape }}
                </{{ section.settings.heading_tag }}>
                {%- if section.settings.button_label != blank and show_mobile_slider -%}
                  <a href="{{ section.settings.button_link }}" class="link underlined-link large-up-hide">
                    {{- section.settings.button_label | escape -}}
                  </a>
                {%- endif -%}
              </div>
            {%- endunless -%}
          </div>
          <div class="grid_item">
            {%- if show_mobile_slider or show_desktop_slider -%}
              <div class="disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden">
                <button
                  type="button"
                  class="slider-button slider-button--prev"
                  name="previous"
                  aria-label="{{ 'general.slider.previous_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
                <button
                  type="button"
                  class="slider-button slider-button--next"
                  name="next"
                  aria-label="{{ 'general.slider.next_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
              </div>
            {%- endif -%}
          </div>
        </div>
        <ul
          class="testimonials-list contains-content-container grid grid--1-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop {% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
          id="Slider-{{ section.id }}"
          data-aos="fade-up"
          role="list"
        >
          {%- liquid
            assign highest_ratio = 0
            for block in section.blocks
              if block.settings.image.aspect_ratio > highest_ratio
                assign highest_ratio = block.settings.image.aspect_ratio
              endif
            endfor
          -%}
          {%- for block in section.blocks -%}
            {%- assign empty_column = '' -%}
            {%- if block.settings.image == blank
              and block.settings.title == blank
              and block.settings.text == blank
              and block.settings.link_label == blank
            -%}
              {%- assign empty_column = ' testimonials-list__item--empty' -%}
            {%- endif -%}

            <li
              id="Slide-{{ section.id }}-{{ forloop.index }}"
              class="testimonials-list__item grid-item{% if section.settings.swipe_on_mobile %} slider-slide{% endif %}{% if section.settings.column_alignment == 'center' %} center{% endif %}{{ empty_column }}"
              {{ block.shopify_attributes }}
            >
              <div class="color-{{ section.settings.color_scheme_1 }} gradient global-media-settings testimonials content-container grid">
                <div class="color-{{ section.settings.color_scheme_1 }} gradient {% if block.settings.product == blank %} no-product-info{% endif %} grid-item left">
                  <div class="testimonial-text">
                    {%- if section.settings.testimonials_style == 'style-2' -%}
                      {%- if block.settings.product != blank -%}
                        <div class="testimonials-info">
                          <div class="product-featured-image">
                            <a href="{{ block.settings.product.url }}">
                              {{
                                block.settings.product.featured_image
                                | image_url: width: 400, height: 400
                                | image_tag: loading: 'lazy'
                              }}
                            </a>
                          </div>

                          <h4 class="product-title">
                            <a
                              class="link"
                              {% if block.settings.product.url == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block.settings.product.url }}"
                              {% endif %}
                            >
                              {%- if block.settings.product.title != blank -%}
                                {{ block.settings.product.title | escape }}
                              {%- else -%}
                                {{ 'onboarding.product_title' | t }}
                              {%- endif -%}
                            </a>
                          </h4>
                        </div>
                      {%- endif -%}
                    {%- endif -%}

                    {%- if block.settings.rating == true -%}
                      <div class="rating">
                        {%- if block.settings.rating_stars == '5-star' -%}
                          <svg class="five-stars" viewBox="0 0 401 63" xmlns="http://www.w3.org/2000/svg" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M98.378,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M181.711,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.459,19.708c0.325,1.846 -1.5,3.292 -3.109,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M265.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M348.378,62.265c-1.609,0.825 -3.434,-0.621 -3.109,-2.467l3.459,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                          </svg>
                        {%- endif -%}
                              
                        {%- if block.settings.rating_stars == '4-star' -%}
                          <svg class="five-stars" viewBox="0 0 401 63" xmlns="http://www.w3.org/2000/svg" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M98.378,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M181.711,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.459,19.708c0.325,1.846 -1.5,3.292 -3.109,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M265.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M348.378,62.265c-1.609,0.825 -3.434,-0.621 -3.109,-2.467l3.459,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                          </svg>
                        {%- endif -%}

                        {%- if block.settings.rating_stars == '3-star' -%}
                          <svg class="five-stars" viewBox="0 0 401 63" xmlns="http://www.w3.org/2000/svg" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M98.378,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M181.711,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.459,19.708c0.325,1.846 -1.5,3.292 -3.109,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M265.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M348.378,62.265c-1.609,0.825 -3.434,-0.621 -3.109,-2.467l3.459,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                          </svg>
                        {%- endif -%}

                        {%- if block.settings.rating_stars == '2-star' -%}
                          <svg class="five-stars" viewBox="0 0 401 63" xmlns="http://www.w3.org/2000/svg" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M98.378,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M181.711,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.459,19.708c0.325,1.846 -1.5,3.292 -3.109,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M265.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M348.378,62.265c-1.609,0.825 -3.434,-0.621 -3.109,-2.467l3.459,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                          </svg>
                        {%- endif -%}

                        {%- if block.settings.rating_stars == '1-star' -%}
                          <svg class="five-stars" viewBox="0 0 401 63" xmlns="http://www.w3.org/2000/svg" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="currentColor"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M98.378,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M181.711,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.838,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.459,19.708c0.325,1.846 -1.5,3.292 -3.109,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M265.044,62.265c-1.608,0.825 -3.433,-0.621 -3.108,-2.467l3.458,-19.708l-14.679,-13.984c-1.371,-1.308 -0.658,-3.7 1.179,-3.958l20.409,-2.9l9.1,-18.029c0.821,-1.625 3.041,-1.625 3.862,-0l9.1,18.029l20.409,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.288,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M348.378,62.265c-1.609,0.825 -3.434,-0.621 -3.109,-2.467l3.459,-19.708l-14.679,-13.984c-1.371,-1.308 -0.659,-3.7 1.179,-3.958l20.408,-2.9l9.1,-18.029c0.821,-1.625 3.042,-1.625 3.863,-0l9.1,18.029l20.408,2.9c1.837,0.258 2.55,2.65 1.175,3.958l-14.675,13.984l3.458,19.708c0.325,1.846 -1.5,3.292 -3.108,2.467l-18.296,-9.4l-18.287,9.4l0.004,-0Z" fill="none" stroke="currentColor" stroke-width="3"/>
                          </svg>
                        {%- endif -%}
                      </div>
                    {%- endif -%}

                    {%- if block.settings.text != blank -%}
                      <div class="rte">{{ block.settings.text }}</div>
                    {%- endif -%}
                    {%- if block.settings.title != blank -%}
                      <h3 class="heading-bold">{{ block.settings.title | escape }}</h3>
                    {%- endif -%}
                  </div>

                  {%- if section.settings.testimonials_style == 'style-1' -%}
                    {% if block.settings.hide_image != true %}
                      {%- if block.settings.image != blank -%}
                        <div class="testimonials-image">
                          <div class="testimonials-media">
                            {{ block.settings.image | image_url: width: 200 | image_tag: loading: 'lazy' }}
                          </div>
                        </div>
                      {%- else -%}
                        <div class="testimonials-image">
                          <div class="testimonials-media">
                            {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                          </div>
                        </div>
                      {%- endif -%}
                    {%- endif -%}
                  {%- endif -%}
                </div>

                {%- if section.settings.testimonials_style == 'style-2' -%}
                  {% if block.settings.hide_image != true %}
                    {%- if block.settings.image != blank -%}
                      <div class="testimonials-image">
                        <div class="testimonials-media">
                          {{ block.settings.image | image_url: width: 200 | image_tag: loading: 'lazy' }}
                        </div>
                      </div>
                    {%- else -%}
                      <div class="testimonials-image">
                        <div class="testimonials-media">
                          {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                        </div>
                      </div>
                    {%- endif -%}
                  {%- endif -%}
                {%- endif -%}

                {%- if section.settings.testimonials_style != 'style-2' -%}
                  <div class="grid-item right">
                    {%- if block.settings.product != blank -%}
                      <div class="testimonials-info">
                        <div class="product-featured-image">
                          <a href="{{ block.settings.product.url }}">
                            {{
                              block.settings.product.featured_image
                              | image_url: width: 400, height: 400
                              | image_tag: loading: 'lazy'
                            }}
                          </a>
                        </div>

                        <h4 class="product-title">
                          <a
                            class="link"
                            {% if block.settings.product.url == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block.settings.product.url }}"
                            {% endif %}
                          >
                            {%- if block.settings.product.title != blank -%}
                              {{ block.settings.product.title | escape }}
                            {%- else -%}
                              {{ 'onboarding.product_title' | t }}
                            {%- endif -%}
                          </a>
                        </h4>
                      </div>
                    {%- endif -%}
                  </div>
                {%- endif -%}
              </div>
            </li>
          {%- endfor -%}
        </ul>
      </slider-component>
    </div>
    <div class="center{% if show_mobile_slider %} small-hide medium-hide{% endif %}" data-aos="fade-up">
      {%- if section.settings.button_label != blank -%}
        <a
          class="button-arrow button button--primary"
          {% if section.settings.button_link == blank %}
            role="link" aria-disabled="true"
          {% else %}
            href="{{ section.settings.button_link }}"
          {% endif %}
        >
          {{ section.settings.button_label | escape }}
          {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
        </a>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.testimonials.name",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "text",
      "id": "title",
      "default": "What Clients Are Saying!",
      "label": "t:sections.testimonials.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "text",
      "id": "caption",
      "default": "Testimonials",
      "label": "t:sections.testimonials.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.testimonials.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.testimonials.settings.button_link.label"
    },
    {
      "type": "select",
      "id": "testimonials_style",
      "options": [
        {
          "value": "style-1",
          "label": "t:sections.testimonials.settings.testimonials_style.options__1.label"
        },
        {
          "value": "style-2",
          "label": "t:sections.testimonials.settings.testimonials_style.options__2.label"
        }
      ],
      "default": "style-1",
      "label": "t:sections.testimonials.settings.testimonials_style.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 3,
      "label": "t:sections.testimonials.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "enable_desktop_slider",
      "label": "t:sections.collection-list.settings.enable_desktop_slider.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 96
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 96
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.testimonials.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.testimonials.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.all.disable_arrow_mobile.label"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.testimonials.blocks.column.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "rating",
          "default": true,
          "label": "t:sections.testimonials.blocks.column.settings.rating.label"
        },
        {
          "type": "select",
          "id": "rating_stars",
          "options": [
            {
              "value": "5-star",
              "label": "t:sections.testimonials.blocks.column.settings.rating_stars.options__1.label"
            },
            {
              "value": "4-star",
              "label": "t:sections.testimonials.blocks.column.settings.rating_stars.options__2.label"
            },
            {
              "value": "3-star",
              "label": "t:sections.testimonials.blocks.column.settings.rating_stars.options__3.label"
            },
            {
              "value": "2-star",
              "label": "t:sections.testimonials.blocks.column.settings.rating_stars.options__4.label"
            },
            {
              "value": "1-star",
              "label": "t:sections.testimonials.blocks.column.settings.rating_stars.options__5.label"
            }
          ],
          "default": "5-star",
          "label": "t:sections.testimonials.blocks.column.settings.rating_stars.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.testimonials.blocks.column.settings.text.label"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Column",
          "label": "t:sections.testimonials.blocks.column.settings.title.label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.testimonials.blocks.column.settings.image.label"
        },
        {
          "type": "checkbox",
          "id": "hide_image",
          "default": false,
          "label": "t:sections.multicolumn.blocks.column.settings.hide_image.label"
        },
        {
          "type": "product",
          "id": "product",
          "label": "t:sections.testimonials.blocks.column.settings.product.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.testimonials.presets.name",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}

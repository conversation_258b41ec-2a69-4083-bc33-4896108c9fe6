{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-search.css' | asset_url | stylesheet_tag }}
{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'quick-add.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-search.css' | asset_url | stylesheet_tag }}</noscript>

{%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
  {{ 'component-facets.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

<script src="{{ 'main-search.js' | asset_url }}" defer="defer"></script>

<style>
  .template-search__header {
    margin-bottom: 3rem;
  }

  .template-search__search {
    margin: 0 auto 3.5rem;
    max-width: 74.1rem;
  }

  .template-search__search .search {
    margin-top: 3rem;
  }

  .template-search--empty {
    padding-bottom: 18rem;
  }

  .template-search .card-information{
    text-align: center;
  }

  .template-search .quick-add .quick-add__submit.button {
    border-radius: var(--media-radius);
  }

  .template-search .article-date svg,
  .template-search .article-author svg {
    display: none;
  }

  .collection-two .template-search__results .quick-add,
  .collection-two .template-search__results .quick-add .quick-add__submit:before{
    border-bottom-left-radius: var(--media-radius);
    border-bottom-right-radius: var(--media-radius);
  }

  @media screen and (min-width: 990px) {
    .collection-two .template-search__results .quick-add {
      margin-top: -25px;
    }
    
    .collection-two .template-search__results .grid--2-col-desktop .quick-add {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      position: absolute;
      top: auto;
      bottom: -10px;
      left: 0;
      transform: none;
      width: auto;
    }
  }

  @media screen and (min-width: 750px) {
    .template-search__header {
      margin-bottom: 5rem;
    }
  }
  @media screen and (max-width: 990px) {
    .template-search .card-information {
      padding: 1.5rem;
    }
  }
  .search__button .icon {
    height: 1.8rem;
  }
</style>

{%- liquid
  assign sort_by = search.sort_by | default: search.default_sort_by
  assign terms = search.terms | escape
  assign search_url = '?q=' | append: terms | append: '&options%5Bprefix%5D=last&sort_by=' | append: sort_by
-%}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient collection-product-style-two collection-two ignore-{{ section.settings.ignore_spacing }} template-search{% unless search.performed and search.results_count > 0 %} template-search--empty{% endunless %} section-{{ section.id }}-padding">
  <div class="template-search__header page-width" data-aos="fade-up">
    <h1 class="h2 center">
      {%- if search.performed -%}
        {{- 'templates.search.title' | t -}}
      {%- else -%}
        {{- 'general.search.search' | t -}}
      {%- endif -%}
    </h1>
    <div class="template-search__search">
      {%- if settings.predictive_search_enabled -%}
        <predictive-search data-loading-text="{{ 'accessibility.loading' | t }}">
      {%- endif -%}
          <main-search>
            <form action="{{ routes.search_url }}" method="get" role="search" class="search">
              <div class="field">
                <input
                  class="search__input field-input"
                  id="Search-In-Template"
                  type="search"
                  name="q"
                  value="{{ search.terms | escape }}"
                  placeholder="{{ 'general.search.search' | t }}"
                  {%- if settings.predictive_search_enabled -%}
                    role="combobox"
                    aria-expanded="false"
                    aria-owns="predictive-search-results"
                    aria-controls="predictive-search-results"
                    aria-haspopup="listbox"
                    aria-autocomplete="list"
                    autocorrect="off"
                    autocomplete="off"
                    autocapitalize="off"
                    spellcheck="false"
                  {%- endif -%}
                >
                <label class="field-label" for="Search-In-Template">{{ 'general.search.search' | t }}</label>
                <input name="options[prefix]" type="hidden" value="last">

                {%- if settings.predictive_search_enabled -%}
                  <div class="predictive-search predictive-search--search-template" tabindex="-1" data-predictive-search>
                    <div class="predictive-search__loading-state">
                      <svg aria-hidden="true" focusable="false" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                      </svg>
                    </div>
                  </div>

                  <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
                {%- endif -%}

                <button type="reset" class="reset__button field__button{% if search.terms == blank %} hidden{% endif %}" aria-label="{{ 'general.search.reset' | t }}">
                  <svg class="icon icon-close" aria-hidden="true" focusable="false">
                    <use xlink:href="#icon-reset">
                  </svg>
                </button>
                <button type="submit" class="search__button field__button" aria-label="{{ 'general.search.search' | t }}">
                  <svg class="icon icon-search" aria-hidden="true" focusable="false">
                    <use xlink:href="#icon-search">
                  </svg>
                </button>
              </div>
            </form>
          </main-search>
      {%- if settings.predictive_search_enabled -%}
        </predictive-search>
      {%- endif -%}

    </div>
    {%- if search.performed -%}
      {%- unless section.settings.enable_filtering or section.settings.enable_sorting -%}
        {%- if search.results_count > 0 -%}
          <p role="status">{{ 'templates.search.results_with_count_and_term' | t: terms: search.terms, count: search.results_count }}</p>
        {%- endif -%}
      {%- endunless -%}
      {%- if search.results_count == 0 and search.filters == empty -%}
        <p role="status">{{ 'templates.search.no_results' | t: terms: search.terms }}</p>
      {%- endif -%}
    {%- endif -%}
  </div>
  {%- if search.performed -%}

    <div class="{% if section.settings.filter_type == 'vertical' %} facets-vertical page-width{% endif %}">
      {%- if search.filters != empty -%}
        {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
          <aside aria-labelledby="verticalTitle" class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}" id="main-search-filters" data-id="{{ section.id }}">
            {% render 'facets', results: search, enable_filtering: section.settings.enable_filtering, enable_sorting: section.settings.enable_sorting, filter_type: section.settings.filter_type %}
          </aside>
        {%- endif -%}
      {%- endif -%}
      
      <div class="product-grid-container" id="ProductGridContainer">
        {%- if search.results.size == 0 and search.filters != empty -%}
          <div class="template-search__results collection collection--empty{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}" id="product-grid" data-id="{{ section.id }}">
            <div class="loading-overlay"></div>
            <div class="title-wrapper center">
              <h2 class="title title--primary">
                {{ 'sections.collection_template.empty' | t }}<br>
                {{ 'sections.collection_template.use_fewer_filters_html' | t: link: search_url, class: "underlined-link link" }}
              </h2>
            </div>
          </div>
        {%- else -%}
          
        {% paginate search.results by 24 %}
          <div class="template-search__results collection{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}" id="product-grid" data-id="{{ section.id }}">
            <div class="loading-overlay"></div>
            <ul class="grid product-grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down" role="list">
              {%- for item in search.results -%}
                {% assign lazy_load = false %}
                {%- if forloop.index > 2 -%}
                  {%- assign lazy_load = true -%}
                {%- endif -%}
        
                {%- case item.object_type -%}
                  {%- when 'product' -%}
                    <li class="grid-item">
                      {%- capture product_settings -%}{%- if section.settings.product_show_vendor -%}vendor,{%- endif -%}title,price{%- endcapture -%}
                      {% render 'card-product',
                        card_product: item,
                        media_aspect_ratio: section.settings.image_ratio,
                        show_secondary_image: section.settings.show_secondary_image,
                        show_vendor: section.settings.show_vendor,
                        lazy_load: lazy_load
                      %}
                    </li>
                  
                  {%- when 'article' -%}
                    {%- if section.settings.show_article_posts -%}
                      <li class="grid-item">
                        {% render 'article-card',
                          article: item,
                          show_image: true,
                          show_date: section.settings.article_show_date,
                          show_author: section.settings.article_show_author,
                          show_badge: true,
                          media_aspect_ratio: 1,
                          lazy_load: lazy_load
                        %}
                      </li>
                    {%- endif -%}
                  
                  {%- when 'page' -%}
                    {%- if section.settings.show_page_posts -%}
                      <li class="grid-item">
                        <div class="article-card-wrapper card-wrapper underline-links-hover">
                          <div class="card card--card card--text ratio" style="--ratio-percent: 100%;">
                            <div class="card-content page-card-content">
                              <div class="card-information">
                                <h3 class="card-heading">
                                  <a href="{{ item.url }}" class="full-unstyled-link">
                                    {{ item.title | truncate: 50 | escape }}
                                  </a>
                                </h3>
                              </div>
                              <div class="card-badge {{ settings.badge_position }}">
                                <span class="badge color-background-1">{{ 'templates.search.page' | t }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    {%- endif -%}
                {%- endcase -%}
              {%- endfor -%}
            </ul>
        
            {%- if paginate.pages > 1 -%}
              {% render 'pagination', paginate: paginate %}
            {%- endif -%}
          </div>
        {% endpaginate %}

        {%- endif -%}
      </div>
    </div>
  {%- endif -%}
</div>

{% schema %}
{
  "name": "t:sections.main-search.name",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 5,
      "label": "t:sections.main-search.settings.columns_desktop.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__1.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "portrait",
          "label": "t:sections.main-search.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-search.settings.image_ratio.options__3.label"
        }
      ],
      "default": "square",
      "label": "t:sections.main-search.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.main-search.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.main-search.settings.show_vendor.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "vertical",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__3.label"
        }
      ],
      "default": "drawer",
      "label": "t:sections.main-collection-product-grid.settings.filter_type.label",
      "info": "t:sections.main-collection-product-grid.settings.filter_type.info"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__2.content",
      "info": "t:sections.main-search.settings.header__2.info"
    },
    {
      "type": "checkbox",
      "id": "article_show_date",
      "default": true,
      "label": "t:sections.main-search.settings.article_show_date.label"
    },
    {
      "type": "checkbox",
      "id": "article_show_author",
      "default": false,
      "label": "t:sections.main-search.settings.article_show_author.label"
    },
    {
      "type": "checkbox",
      "id": "show_article_posts",
      "default": true,
      "label": "t:sections.main-search.settings.show_article_posts.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__3.content"
    },
      {
      "type": "checkbox",
      "id": "show_page_posts",
      "default": true,
      "label": "t:sections.main-search.settings.show_page_posts.label"
    },
    {
      "type": "header",
      "content": "t:sections.collection-list.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.related-products.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.related-products.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.related-products.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-2"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    }
  ]
}
{% endschema %}
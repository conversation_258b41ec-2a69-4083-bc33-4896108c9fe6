<style type="text/css">
  .template-404 .title + * {
    margin-top: 1rem;
  }

  .template-404 .title {
    margin: 0;
  }

  .template-404 .number {
    font-size: 6rem;
    margin: 0;
  }

  @media screen and (min-width: 750px) {
    .template-404 .title + * {
      margin-top: 2rem;
    }
    .template-404 .number {
      font-size: 16rem;
      line-height: 1.3;
      margin: 0;
    }
  }
</style>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="template-404 page-width center section-{{ section.id }}-padding ">
    <p class="number heading-bold">
      {{ section.settings.heading }}
    </p>
    {%- if section.settings.image != blank -%}
      {%- capture sizes -%}
            (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
            (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
          {%- endcapture -%}
      {{
        section.settings.image
        | image_url: width: 1500
        | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
      }}
    {%- endif -%}
    <h1 class="title">
      {{ section.settings.text }}
    </h1>
    <a href="{{ routes.all_products_collection_url }}" class="button">
      {{ 'general.continue_shopping' | t }}
    </a>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-404.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
          "type": "text",
          "id": "heading",
          "default": "404",
          "label": "t:sections.main-404.settings.heading.label"
        },
     {
          "type": "richtext",
          "id": "text",
          "default": "<p>The Page you're looking for could not be found</p>",
          "label": "t:sections.main-404.settings.text.label"
        },
    {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.main-404.settings.image.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 40
    }
  ]
  }
{% endschema %}

{{ 'component-card.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .recently-viewed .price {
    font-size: 1.2rem;
  }
  @media screen and (min-width: 990px) {
    .recent-products .grid--4-col-desktop .grid-item {
      width: calc(16.6% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
      max-width: calc(16.6% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
    }
  }
  @media screen and (max-width: 990px) {
    .recent-products .grid--4-col-desktop .grid-item {
      width: calc(33.3% - var(--grid-desktop-horizontal-spacing));
      max-width: 33.3%;
    }
  }
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

<div
  id="recently-viewed"
  data-color-scheme="{{ section.settings.color_scheme_1 }}"
  class="ignore-{{ section.settings.ignore_spacing }}"
>
  <div class="color-{{ section.settings.color_scheme }} gradient margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
    <div class="page-width recent-products section-{{ section.id }}-padding" data-aos="fade-up">
      {%- unless section.settings.caption == blank -%}
        <p
          class="video-caption image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}"
          {{ section.shopify_attributes }}
        >
          {{ section.settings.caption | escape }}
        </p>
      {%- endunless -%}
      {%- unless section.settings.heading == blank -%}
        <div class="title-wrapper title-wrapper--no-top-margin">
          <{{ section.settings.heading_tag }} class="title heading-bold {{ section.settings.heading_size }}">
            {{- section.settings.heading -}}
          </{{ section.settings.heading_tag }}>
        </div>
      {%- endunless -%}
      <ul class="recently-viewed grid product-grid grid--4-col-desktop grid--2-col-tablet-down" role="list">
        <!-- Recently viewed products will appear here -->
      </ul>
    </div>
  </div>
</div>

<script>
    // Function to get the current color scheme
function getCurrentColorScheme() {
    var colorSchemeContainer = document.getElementById("recently-viewed");
    return colorSchemeContainer.getAttribute("data-color-scheme");
}

const localViewed = localStorage.recentlyViewedProduct;

function getRecentlyViewedProducts() {
  var selectedColor = getCurrentColorScheme();
  const productData = JSON.parse(localStorage.getItem("recentlyViewedProduct"));
  const recentlyViewedHtml = [];
  
  if (productData) {
    productData.forEach(function (item) {
      if (item.productTitle) {
        recentlyViewedHtml.push(`
         <li class="grid-item">
           <div class="card-wrapper underline-links-hover">
            <div class="color-${selectedColor} gradient card card--standard card--media " style="--ratio-percent: 100%;">
              <div class="card-inner ratio" style="--ratio-percent: 100%;">
               <div style="position:static" class="card__media">
               <div class="media media--transparent media--hover-effect global-media-settings">
          		  <img class="motion-reduce" src="${item.productImg}" width="${item.imgWidth}" height="${item.imgHeight}"  loading="lazy" alt="${item.productImageAltText}"/>
               </div>
               </div>
             </div>
             <div class="card-content">
             <div class="card-information">
             <h3 class="card-heading h5">
             <a class="full-unstyled-link" href="${item.productUrl}">${item.productTitle}</a></h3>
               <div class="card-information">
                 <div class="price ">
                   <div class="price-container">
                     <div class="price-item price-item--regular">
                         ${item.productPrice}
                     </div>
                   </div>
                 </div>
               </div>
             </div>
             </div>
             </div>
            </div>
          </li>
        `);
      }
    });
  }

  const recentlyViewedContainer = document.querySelector(".recently-viewed");
  const displayElement = document.querySelector(".recent-products");

  if (recentlyViewedHtml.length > 0) {
    const newProductData = recentlyViewedHtml.join("");

    // Validation: Ensure `<a>` elements contain non-empty text
    const tempElement = document.createElement("div");
    tempElement.innerHTML = newProductData;

    const aElement = tempElement.querySelector("a");
    if (aElement && aElement.textContent.trim() !== "") {
      recentlyViewedContainer.innerHTML = newProductData;
      displayElement.style.display = "block";
    } else {
      displayElement.style.display = "none";
    }
  } else {
    displayElement.style.display = "none";
  }
}

document.addEventListener("DOMContentLoaded", function () {
  getRecentlyViewedProducts();
});

document.addEventListener("shopify:section:load", function () {
  getRecentlyViewedProducts();
});
</script>

{% schema %}
{
  "name": "t:sections.recently-viewed-products.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
  {
    "type": "text",
    "id": "caption",
    "default": "Pick up where you left off",
    "label": "t:sections.recently-viewed-products.settings.caption.label"
  },
  {
    "type": "select",
    "id": "text_style",
    "options": [
      {
        "value": "subtitle",
        "label": "t:sections.all.text_style.options__1.label"
      },
      {
        "value": "caption-with-letter-spacing",
        "label": "t:sections.all.text_style.options__2.label"
      }
    ],
    "default": "caption-with-letter-spacing",
    "label": "t:sections.all.text_style.label"
  },
  {
    "type": "select",
    "id": "text_size",
    "options": [
      {
        "value": "small",
        "label": "t:sections.all.text_size.options__1.label"
      },
      {
        "value": "medium",
        "label": "t:sections.all.text_size.options__2.label"
      },
      {
        "value": "large",
        "label": "t:sections.all.text_size.options__3.label"
      }
    ],
    "default": "medium",
    "label": "t:sections.all.text_size.label"
  },
  {
    "type": "text",
    "id": "heading",
    "default": "Recently Viewed",
    "label": "t:sections.recently-viewed-products.settings.heading.label"
  },
  {
    "type": "select",
    "id": "heading_size",
    "options": [
      {
        "value": "extra-large",
        "label": "t:sections.all.heading_size.options__1.label"
      },
      {
        "value": "large",
        "label": "t:sections.all.heading_size.options__2.label"
      },
      {
        "value": "medium",
        "label": "t:sections.all.heading_size.options__3.label"
      }
    ],
    "default": "medium",
    "label": "t:sections.all.heading_size.label"
  },
  {
    "type": "select",
    "id": "heading_tag",
    "options": [
      {
        "value": "h1",
        "label": "t:sections.all.heading_tag.options__1.label"
      },
      {
        "value": "h2",
        "label": "t:sections.all.heading_tag.options__2.label"
      },
      {
        "value": "h3",
        "label": "t:sections.all.heading_tag.options__3.label"
      },
      {
        "value": "h4",
        "label": "t:sections.all.heading_tag.options__4.label"
      },
      {
        "value": "h5",
        "label": "t:sections.all.heading_tag.options__5.label"
      },
      {
        "value": "h6",
        "label": "t:sections.all.heading_tag.options__6.label"
      }
    ],
    "default": "h2",
    "label": "t:sections.all.heading_tag.label",
    "info": "t:sections.all.heading_tag.info"
  },
  {
    "type": "header",
    "content": "t:sections.all.header_color_box.content"
  },
  {
    "type": "color_scheme",
    "id": "color_scheme",
    "label": "t:sections.all.colors.label",
    "default": "option-2"
  },
  {
    "type": "color_scheme",
    "id": "color_scheme_1",
    "label": "t:sections.all.colors_box.label",
    "default": "option-1"
  },
  {
    "type": "header",
    "content": "t:sections.all.padding.section_padding_heading"
  },
  {
    "type": "range",
    "id": "padding_top",
    "min": 0,
    "max": 100,
    "step": 4,
    "unit": "px",
    "label": "t:sections.all.padding.padding_top",
    "default": 72
  },
  {
    "type": "range",
    "id": "padding_bottom",
    "min": 0,
    "max": 100,
    "step": 4,
    "unit": "px",
    "label": "t:sections.all.padding.padding_bottom",
    "default": 72
  },
  {
    "type": "checkbox",
    "id": "ignore_spacing",
    "default": false,
    "label": "t:sections.all.ignore_spacing.label"
  },
  {
    "type": "header",
    "content": "t:sections.featured-collection.settings.header_mobile.content"
  },
  {
    "type": "select",
    "id": "margin_spacing",
    "options": [
      {
        "value": "negative",
        "label": "t:sections.all.margin_spacing.options__1.label"
      },
      {
        "value": "positive",
        "label": "t:sections.all.margin_spacing.options__2.label"
      }
    ],
    "default": "negative",
    "label": "t:sections.all.margin_spacing.label"
  },
  {
    "type": "range",
    "id": "margin_top",
    "min": 0,
    "max": 200,
    "step": 4,
    "unit": "px",
    "label": "t:sections.all.margin_top",
    "default": 0
  }
  ],
  "presets": [
  {
    "name": "t:sections.recently-viewed-products.presets.name"
  }
]
}
{% endschema %}
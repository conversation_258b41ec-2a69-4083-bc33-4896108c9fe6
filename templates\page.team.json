{"sections": {"main": {"type": "main-page", "disabled": true, "settings": {"padding_top": 36, "padding_bottom": 36}}, "video_background": {"type": "video-background", "settings": {"caption": "Subheading", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Heading", "heading_size": "large", "heading_tag": "h2", "text": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>", "button_label": "Button Label", "link": "", "full_width_background": false, "background_height": "small", "box_align": "middle-left", "text_align": "left", "ignore_box": false, "blur": 0, "opacity": 0.5, "color_scheme": "option-3", "padding_top": 0, "padding_bottom": 36, "ignore_spacing": false}}, "multicolumn": {"type": "multicolumn", "blocks": {"column_1": {"type": "column", "settings": {"image": "", "hide_image": false, "title": "Author's Name", "text": "<p>Add customer reviews and testimonials to showcase your store's happy customers.</p>", "link_label": "", "link": ""}}, "column_2": {"type": "column", "settings": {"image": "", "hide_image": false, "title": "Author's Name", "text": "<p>Add customer reviews and testimonials to showcase your store's happy customers.</p>", "link_label": "", "link": ""}}, "column_3": {"type": "column", "settings": {"image": "", "hide_image": false, "title": "Author's Name", "text": "<p>Add customer reviews and testimonials to showcase your store's happy customers.</p>", "link_label": "", "link": ""}}}, "block_order": ["column_1", "column_2", "column_3"], "settings": {"enable_card_background": false, "caption": "Subheading", "text_style": "caption-with-letter-spacing", "text_size": "medium", "title": "Heading", "heading_size": "large", "heading_style": "default", "heading_tag": "h2", "image_width": "full", "image_ratio": "square", "columns_desktop": 3, "column_alignment": "left", "button_label": "", "button_link": "", "color_scheme": "option-1", "color_scheme_1": "option-2", "padding_top": 0, "padding_bottom": 36, "ignore_spacing": true, "columns_mobile": "1", "swipe_on_mobile": true, "disable_arrow_mobile": true}}, "banner_two_columns": {"type": "banner-two-columns", "blocks": {"slide_1": {"type": "slide", "settings": {"caption": "Subheading", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Heading", "heading_size": "medium", "heading_tag": "h2", "subheading": "Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.", "link_label": "", "link": "", "show_text_box": false, "image_overlay_opacity": 90, "color_scheme_1": ""}}, "slide_2": {"type": "slide", "settings": {"caption": "Subheading", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Heading", "heading_size": "medium", "heading_tag": "h2", "subheading": "Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.", "link_label": "", "link": "", "show_text_box": false, "image_overlay_opacity": 90, "color_scheme_1": ""}}, "slide_3": {"type": "slide", "settings": {"caption": "Image Caption", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Banner", "heading_size": "medium", "heading_tag": "h2", "subheading": "Tell your brand's story through images", "link_label": "", "link": "", "show_text_box": false, "image_overlay_opacity": 60, "color_scheme_1": "option-3"}}, "slide_4": {"type": "slide", "settings": {"caption": "Image Caption", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Banner", "heading_size": "medium", "heading_tag": "h2", "subheading": "Tell your brand's story through images", "link_label": "", "link": "", "show_text_box": false, "image_overlay_opacity": 60, "color_scheme_1": "option-3"}}, "slide_5": {"type": "slide", "settings": {"caption": "Image Caption", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Banner", "heading_size": "medium", "heading_tag": "h2", "subheading": "Tell your brand's story through images", "link_label": "", "link": "", "show_text_box": false, "image_overlay_opacity": 60, "color_scheme_1": "option-3"}}, "slide_6": {"type": "slide", "settings": {"caption": "Image Caption", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Banner", "heading_size": "medium", "heading_tag": "h2", "subheading": "Tell your brand's story through images", "link_label": "", "link": "", "show_text_box": false, "image_overlay_opacity": 60, "color_scheme_1": "option-3"}}, "slide_7": {"type": "slide", "settings": {"caption": "Image Caption", "text_style": "caption-with-letter-spacing", "text_size": "medium", "heading": "Banner", "heading_size": "medium", "heading_tag": "h2", "subheading": "Tell your brand's story through images", "link_label": "", "link": "", "show_text_box": false, "image_overlay_opacity": 60, "color_scheme_1": "option-3"}}}, "block_order": ["slide_1", "slide_2", "slide_3", "slide_4", "slide_5", "slide_6", "slide_7"], "settings": {"banner_layout": "grid_one", "animate_slider": false, "full_width": false, "color_scheme": "", "padding_top": 0, "padding_bottom": 0, "ignore_spacing": true, "margin_spacing": "negative", "margin_top": 0, "swipe_on_mobile": false, "disable_arrow_mobile": true}}, "rich_text": {"type": "rich-text", "blocks": {"image": {"type": "image", "settings": {"image": ""}}, "heading": {"type": "heading", "settings": {"heading": "<p>Heading</p>", "heading_size": "large", "heading_style": "default", "heading_tag": "h2"}}, "text": {"type": "text", "settings": {"text": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store. </p>"}}, "button": {"type": "button", "settings": {"button_label": "", "button_link": "", "button_style_secondary": false, "button_label_2": "", "button_link_2": "", "button_style_secondary_2": false}}}, "block_order": ["image", "heading", "text", "button"], "settings": {"desktop_content_position": "center", "content_alignment": "center", "full_width": true, "color_scheme": "option-2", "padding_top": 76, "padding_bottom": 0, "ignore_spacing": false, "mobile_content_alignment": "center"}}, "tabs": {"type": "tabs", "blocks": {"tab_1": {"type": "tab", "settings": {"heading": "Year", "image": "", "hide_image": false, "row_content": "<h2>Heading</h2><p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>", "button_label": "", "button_link": "", "button_style_secondary": false}}, "tab_2": {"type": "tab", "settings": {"heading": "Year", "image": "", "hide_image": false, "row_content": "<h2>Heading</h2><p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>", "button_label": "", "button_link": "", "button_style_secondary": false}}, "tab_3": {"type": "tab", "settings": {"heading": "Year", "image": "", "hide_image": false, "row_content": "<h2>Heading</h2><p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>", "button_label": "", "button_link": "", "button_style_secondary": false}}, "tab_4": {"type": "tab", "settings": {"heading": "Year", "image": "", "hide_image": false, "row_content": "<h2>Heading</h2><p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>", "button_label": "", "button_link": "", "button_style_secondary": false}}}, "block_order": ["tab_1", "tab_2", "tab_3", "tab_4"], "settings": {"tabs_style": "style-2", "full_width": false, "color_scheme": "option-2", "padding_top": 16, "padding_bottom": 76, "ignore_spacing": true}}, "image_gallery": {"type": "image-gallery", "blocks": {"text_1": {"type": "text", "settings": {"image": ""}}, "text_2": {"type": "text", "settings": {"image": ""}}}, "block_order": ["text_1", "text_2"], "settings": {"caption": "Follow us on socials", "text_style": "caption-with-letter-spacing", "text_size": "medium", "title": "@fuel", "heading_size": "medium", "heading_style": "default", "heading_tag": "h2", "scroll_direction": "ltc", "scroll_speed": 70, "scroll_height": 600, "hover_stop": true, "color_scheme": "", "padding_top": 0, "padding_bottom": 0, "ignore_spacing": false, "margin_spacing": "negative", "margin_top": 0}}}, "order": ["main", "video_background", "multicolumn", "banner_two_columns", "rich_text", "tabs", "image_gallery"]}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-rte.css' | asset_url | stylesheet_tag }}
{{ 'section-banner-two-columns.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'quick-add.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'template-collection.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }

    @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
    .featured-collection.banner-two-columns .banner--height {
      padding: 10px;
    }
  }

  .css-slider-dot-navigation .css-slider-dot {
    width: 9px;
    height: 9px;
    display: inline-block;
    margin: 0 5px;
    border-radius: 7px;
    transition: all .1s linear;
    overflow: hidden;
    cursor: pointer;
    background-color: #000;
    opacity: .25;
    position: relative;
  }

  .css-slider-dot-navigation .css-slider-dot.active {
    opacity: 1;
    width: 30px;
  }

  .featured-collection .grid {
    align-items: stretch;
  }

  .featured-collection.banner-two-columns .banner--height {
    min-height: 100%;
  }
  
{%- endstyle -%}


{%- liquid
  assign products_to_display = section.settings.collection.all_products_count

  if section.settings.collection.all_products_count > section.settings.products_to_show
    assign products_to_display = section.settings.products_to_show
    assign more_in_collection = true
  endif

  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and products_to_display > columns_mobile_int
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider and products_to_display > section.settings.columns_desktop
    assign show_desktop_slider = true
  endif
-%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
  <div
    id="animation"
    class="color-{{ section.settings.color_scheme }} gradient extract {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin {% if section.settings.show_secondary_image == true %}show-secondary-image{% endif %}"
  >
    <div
      class="featured-collection banner-two-columns {% if section.settings.quick_add_position == 'overlay' %} quick-add-overlay{% endif %} {% if section.settings.collection_style == 'collection-one' %} collection-one{% endif %} {% if section.settings.collection_style == 'collection-two' %} collection-two{% endif %} {% if section.settings.collection_style == 'collection-three' %} collection-three{% endif %} {% if section.settings.collection_style == 'collection-four' %} collection-four{% endif %} collection section-{{ section.id }}-padding{% if section.settings.full_width %} collection--full-width{% endif %} page-width quick-view-{{ section.settings.enable_quick_add }} enable-quick-buy-{{ section.settings.enable_quick_buy }}"
      data-aos="fade-up"
    >
      <div class="collection-content">
        {% comment %} Start Collection Products {% endcomment %}
        <slider-component class="slider-mobile-gutter{% if section.settings.full_width %} slider-component-full-width{% endif %} slider-no-padding page-width{% if show_desktop_slider == false and section.settings.full_width == false %} page-width-desktop{% endif %}{% if show_desktop_slider %} slider-component-desktop{% endif %} mobile-disable-quick-add--{{ section.settings.disable_quick_add }} {% if show_mobile_slider == true and show_desktop_slider == false  %} slider-buttons-desktop-hide{% endif %} {% if show_mobile_slider == false and show_desktop_slider == true %} slider-buttons-mobile-hide{% endif %}">
          <div class="collection-title title-wrapper title-wrapper--no-top-margin {% if show_mobile_slider %} title-wrapper--self-padded-tablet-down{% endif %}{% if show_desktop_slider %} collection-title--desktop-slider{% endif %}">
            <div class="grid">
              <div class="grid-item left">
                {%- if section.settings.caption != blank -%}
                  <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
                    {{ section.settings.caption | escape }}
                  </p>
                {%- endif -%}
                {%- if section.settings.title != blank -%}
                  <{{ section.settings.heading_tag }} class="title {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }} heading-bold">
                    {{- section.settings.title | escape -}}
                  </{{ section.settings.heading_tag }}>
                {%- endif -%}
                {%- if section.settings.description != blank
                  or section.settings.show_description
                  and section.settings.collection.description != empty
                -%}
                  <div class="collection-description {{ section.settings.description_style }} rte">
                    {%- if section.settings.show_description -%}
                      {{ section.settings.collection.description }}
                    {%- else -%}
                      {{ section.settings.description -}}
                    {%- endif %}
                  </div>
                {%- endif -%}
              </div>

              <div class="grid-item right">
                {%- if show_mobile_slider or show_desktop_slider -%}
                  <div class="disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden">
                    <button
                      type="button"
                      class="slider-button slider-button--prev"
                      name="previous"
                      aria-label="{{ 'general.slider.previous_slide' | t }}"
                      aria-controls="Slider-{{ section.id }}"
                    >
                      {% render 'icon-slider-arrows' %}
                    </button>
                    <button
                      type="button"
                      class="slider-button slider-button--next"
                      name="next"
                      aria-label="{{ 'general.slider.next_slide' | t }}"
                      aria-controls="Slider-{{ section.id }}"
                    >
                      {% render 'icon-slider-arrows' %}
                    </button>
                  </div>
                {%- endif -%}

                {%- if section.settings.show_view_all and more_in_collection -%}
                  <div class="collection-view-all">
                    <a
                      href="{{ section.settings.collection.url }}"
                      class="{% if section.settings.view_all_style == 'link' %}link {% elsif section.settings.view_all_style == 'solid' %}button{% else %}button button--secondary{% endif %}"
                      aria-label="{{ 'sections.featured_collection.view_all_label' | t: collection_name: section.settings.collection.title }}"
                    >
                      {{ 'sections.featured_collection.view_all' | t }}
                      {%- if section.settings.view_all_style == 'link' -%}{% render 'icon-arrow' %}{%- endif -%}
                    </a>
                  </div>
                {%- endif -%}
              </div>
            </div>
          </div>
          {% comment %} End Title,Description & View All Link {% endcomment %}

          <ul
            id="Slider-{{ section.id }}"
            class="grid product-grid contains-card contains-card--product{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--1-col-tablet-down{% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
            role="list"
            aria-label="{{ 'general.slider.name' | t }}"
          >
            <!-- Promo Banner Start -->
            {%- for block in section.blocks -%}
              <li class="grid-item{% if show_mobile_slider or show_desktop_slider %} slider-slide{% endif %}">
                <div
                  id="Banner-{{ section.id }}"
                  class="banner banner--height {% if section.blocks.first.settings.image == blank %} slideshow--placeholder{% endif %}"
                  {{ block.shopify_attributes }}
                >
                  <style>
                    #Cover-{{ section.id }}-{{ forloop.index }} .banner-media::after {
                      opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};
                    }
                  </style>
                  <div id="Cover-{{ section.id }}-{{ forloop.index }}">
                    <div class="banner-media media{% if block.settings.image == blank %} placeholder{% endif %} {% if section.settings.animate_slider == true %} animate--slider{% endif %}">
                      {% if block.settings.hide_image != true %}
                      {%- if block.settings.image -%}
                        {%- assign height = block.settings.image.width
                          | divided_by: block.settings.image.aspect_ratio
                          | round
                        -%}
                        {{
                          block.settings.image
                          | image_url: width: 3840
                          | image_tag:
                            loading: 'lazy',
                            height: height,
                            sizes: '100vw',
                            widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
                        }}
                      {%- else -%}
                        {%- assign placeholder_slide = forloop.index | modulo: 2 -%}
                        {%- if placeholder_slide == 1 -%}
                          {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
                        {%- else -%}
                          {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
                        {%- endif -%}
                      {%- endif -%}
                      {%- endif -%}
                    </div>
                  </div>

                  <div class="banner-content {% if block.settings.show_text_box == false %} banner--desktop-transparent{% endif %}">
                    <div class="global-media-settings banner-two-columns-box content-container content-container--full-width-mobile color-{{ block.settings.color_scheme_1 }} gradient">
                      {%- if block.settings.caption != blank -%}
                        <p
                          class="image-with-text-text image-with-text-text--caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ block.settings.caption | escape }}
                        </p>
                      {%- endif -%}
                      {%- if block.settings.heading != blank -%}
                        <{{ block.settings.heading_tag }} class="banner-heading heading-bold {{ block.settings.heading_size }}">
                          {{- block.settings.heading | escape -}}
                        </{{ block.settings.heading_tag }}>
                      {%- endif -%}
                      {%- if block.settings.subheading != blank -%}
                        <div class="banner-text" {{ block.shopify_attributes }}>
                          <span>{{ block.settings.subheading | escape }}</span>
                        </div>
                      {%- endif -%}
                      {%- if block.settings.link_label != blank -%}
                        <div class="banner-buttons">
                          <a
                            class="button-arrow button button--primary"
                            {% if block.settings.link == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block.settings.link }}"
                            {% endif %}
                          >
                            {{- block.settings.link_label | escape -}}
                            {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
                          </a>
                        </div>
                      {%- endif -%}
                    </div>
                  </div>
                  {% comment %} End Banner Content {% endcomment %}
                </div>
              </li>
              <!-- Promo Banner End -->
            {%- endfor -%}

            {%- for product in section.settings.collection.products limit: section.settings.products_to_show -%}
              <li
                id="Slide-{{ section.id }}-{{ forloop.index }}"
                class="grid-item{% if show_mobile_slider or show_desktop_slider %} slider-slide{% endif %} {% if section.settings.enable_quick_buy == false and section.settings.enable_quick_add == false  %}enable-quick-buy-false{% endif %}"
              >
                {% render 'card-product',
                  card_product: product,
                  media_aspect_ratio: section.settings.image_ratio,
                  show_secondary_image: section.settings.show_secondary_image,
                  show_vendor: section.settings.show_vendor,
                  show_quick_add: section.settings.enable_quick_add,
                  show_quick_buy: section.settings.enable_quick_buy,
                  section_id: section.id
                %}
              </li>
            {%- else -%}
              {%- for i in (1..3) -%}
                 <li
                  id="Slide-{{ section.id }}-{{ forloop.index }}"
                  class="grid-item{% if show_mobile_slider or show_desktop_slider %} slider-slide{% endif %}"
                >
                  {%- assign placeholder_image = 'collection-' | append: forloop.rindex -%}
                  {% render 'card-product',
                    show_vendor: section.settings.show_vendor,
                    placeholder_image: placeholder_image
                  %}
                </li>
              {%- endfor -%}
            {%- endfor -%}
          </ul>
        </slider-component>
        {% comment %} End Collection Products {% endcomment %}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.featured-collection.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "collection",
      "id": "collection",
      "label": "t:sections.featured-collection.settings.collection.label"
    },
    {
      "type": "text",
      "id": "caption",
      "default": "Image Caption",
      "label": "t:sections.featured-collection.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Featured collection",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
       "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.featured-collection.settings.description.label"
    },
    {
      "type": "checkbox",
      "id": "show_description",
      "label": "t:sections.featured-collection.settings.show_description.label",
      "default": false
    },
    {
      "type": "select",
      "id": "description_style",
      "label": "t:sections.featured-collection.settings.description_style.label",
      "options": [
        {
          "value": "body",
          "label": "t:sections.featured-collection.settings.description_style.options__1.label"
        },
        {
          "value": "subtitle",
          "label": "t:sections.featured-collection.settings.description_style.options__2.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.featured-collection.settings.description_style.options__3.label"
        }
      ],
      "default": "body"
    },
    {
      "type": "select",
      "id": "collection_style",
      "options": [
        {
          "value": "collection-one",
          "label": "t:sections.featured-collection.settings.collection_style.options__1.label"
        },
        {
          "value": "collection-three",
          "label": "t:sections.featured-collection.settings.collection_style.options__3.label"
        }
      ],
      "default": "collection-three",
      "label": "t:sections.featured-collection.settings.collection_style.label"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 25,
      "step": 1,
      "default": 4,
      "label": "t:sections.featured-collection.settings.products_to_show.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "t:sections.featured-collection.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.featured-collection.settings.full_width.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_view_all.label"
    },
    {
      "type": "select",
      "id": "view_all_style",
      "label": "t:sections.featured-collection.settings.view_all_style.label",
      "options": [
        {
          "value": "link",
          "label": "t:sections.featured-collection.settings.view_all_style.options__1.label"
        },
        {
          "value": "button",
          "label": "t:sections.featured-collection.settings.view_all_style.options__2.label"
        }
      ],
      "default": "link"
    },
    {
      "type": "checkbox",
      "id": "enable_desktop_slider",
      "label": "t:sections.featured-collection.settings.enable_desktop_slider.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.featured-collection.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.featured-collection.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.featured-collection.settings.image_ratio.options__3.label"
        }
      ],
      "default": "square",
      "label": "t:sections.featured-collection.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_add",
      "default": false,
      "label": "t:sections.featured-collection.settings.enable_quick_buy.label"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_buy",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_quick_add.label"
    },
    {
      "type": "select",
      "id": "quick_add_position",
      "options": [
        {
          "value": "overlay",
          "label": "t:sections.featured-collection.settings.quick_add_position.options__1.label"
        },
        {
          "value": "default",
          "label": "t:sections.featured-collection.settings.quick_add_position.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.featured-collection.settings.quick_add_position.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-2"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 12
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "1",
      "label": "t:sections.related-products.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.related-products.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.related-products.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "disable_quick_add",
      "default": false,
      "label": "t:sections.featured-collection.settings.disable_quick_add.label"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.featured-collection.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.all.disable_arrow_mobile.label"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "t:sections.banner-two-columns.blocks.slide.name",
      "limit": 2,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.image.label"
        },
        {
          "type": "checkbox",
          "id": "hide_image",
          "default": false,
          "label": "t:sections.events-calendar.blocks.event.settings.hide_image.label"
        },
        {
          "type": "text",
          "id": "caption",
          "default": "Image Caption",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.caption.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.all.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.all.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.all.text_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.all.text_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.all.text_size.options__3.label"
            }
          ],
          "default": "small",
          "label": "t:sections.all.text_size.label"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Banner",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "extra-large",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "large",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "medium",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "large",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "options": [
            {
              "value": "h1",
              "label": "t:sections.all.heading_tag.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_tag.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_tag.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_tag.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_tag.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_tag.options__6.label"
            }
          ],
          "default": "h2",
          "label": "t:sections.all.heading_tag.label",
          "info": "t:sections.all.heading_tag.info"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Tell your brand's story through images",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.subheading.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "show_text_box",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.show_text_box.label",
          "default": false
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "t:sections.banner-two-columns.blocks.slide.settings.image_overlay_opacity.label",
          "default": 60
        },
        {
          "type": "color_scheme",
          "id": "color_scheme_1",
          "label": "t:sections.all.colors.label",
          "default": "option-3"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-collection.presets.name",
      "blocks": [
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}

{{ 'component-newsletter.css' | asset_url | stylesheet_tag }}
{{ 'newsletter-section.css' | asset_url | stylesheet_tag }}
{{ 'component-rte.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

<div class="ignore-{{ section.settings.ignore_spacing }}">
<div class="newsletter center {% if section.settings.full_width == false %}newsletter--narrow page-width{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
  <div class="newsletter-wrapper color-{{ section.settings.color_scheme }} gradient content-container extract{% if section.settings.full_width %} content-container--full-width{% endif %} section-{{ section.id }}-padding" data-aos="fade-up">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when '@app' -%}
          {% render block %}
        {%- when 'heading' -%}
          <{{ block.settings.heading_tag }}
            class="heading-bold {{ block.settings.heading_size }} heading-{{ block.settings.heading_style }}"
            {{ block.shopify_attributes }}
          >
            {{ block.settings.heading | escape }}
          </{{ block.settings.heading_tag }}>
        {%- when 'paragraph' -%}
          <div class="newsletter-subheading rte" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>
        {%- when 'email_form' -%}
          <div {{ block.shopify_attributes }}>
            {% form 'customer', class: 'newsletter-form' %}
              <input type="hidden" name="contact[tags]" value="newsletter">
              <div class="newsletter-form__field-wrapper animate-arrow">
                <div class="field">
                  <input
                    id="NewsletterForm--{{ section.id }}"
                    type="email"
                    name="contact[email]"
                    class="field-input email-input"
                    value="{{ form.email }}"
                    aria-required="true"
                    autocorrect="off"
                    autocapitalize="off"
                    autocomplete="email"
                    {% if form.errors %}
                      autofocus
                      aria-invalid="true"
                      aria-describedby="Newsletter-error--{{ section.id }}"
                    {% elsif form.posted_successfully? %}
                      aria-describedby="Newsletter-success--{{ section.id }}"
                    {% endif %}
                    placeholder="{{ 'newsletter.label' | t }}"
                    required
                  >
                  <label class="field-label" for="NewsletterForm--{{ section.id }}">
                    {{ 'newsletter.label' | t }}
                  </label>
                  <button
                    type="submit"
                    class="newsletter-form__button field__button"
                    name="commit"
                    id="NewsletterSubscribe"
                    aria-label="{{ 'newsletter.button_label' | t }}"
                  >
                    {% render 'icon-arrow' %}
                  </button>
                </div>
                {%- if form.errors -%}
                  <small class="newsletter-form-message form-message" id="Newsletter-error--{{ section.id }}">
                    {%- render 'icon-error' -%}
                    {{- form.errors.translated_fields.email | capitalize }}
                    {{ form.errors.messages.email -}}
                  </small>
                {%- endif -%}
              </div>
              {%- if form.posted_successfully? -%}
                <h3
                  class="newsletter-form-message newsletter-form-message--success form-message"
                  id="Newsletter-success--{{ section.id }}"
                  tabindex="-1"
                  autofocus
                >
                  {% render 'icon-success' -%}
                  {{- 'newsletter.success' | t }}
                </h3>
              {%- endif -%}
            {% endform %}
          </div>
      {%- endcase -%}
    {%- endfor -%}
  </div>
</div>
</div>

{% schema %}
{
  "name": "t:sections.newsletter.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "t:sections.newsletter.settings.full_width.label",
      "info": "t:sections.newsletter.settings.full_width.info"
    },
    {
      "type": "paragraph",
      "content": "t:sections.newsletter.settings.paragraph.content"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 52
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.newsletter.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Subscribe to our emails",
          "label": "t:sections.newsletter.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "extra-large",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "large",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "medium",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "large",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "heading_style",
          "options": [
            {
              "value": "default",
              "label": "t:sections.all.heading_style.options__1.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.all.heading_style.options__2.label"
            }
          ],
          "default": "default",
          "label": "t:sections.all.heading_style.label"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "options": [
            {
              "value": "h1",
              "label": "t:sections.all.heading_tag.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_tag.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_tag.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_tag.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_tag.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_tag.options__6.label"
            }
          ],
          "default": "h2",
          "label": "t:sections.all.heading_tag.label",
          "info": "t:sections.all.heading_tag.info"
        }
      ]
    },
    {
      "type": "paragraph",
      "name": "t:sections.newsletter.blocks.paragraph.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Be the first to know about new collections and exclusive offers.</p>",
          "label": "t:sections.newsletter.blocks.paragraph.settings.paragraph.label"
        }
      ]
    },
    {
      "type": "email_form",
      "name": "t:sections.newsletter.blocks.email_form.name",
      "limit": 1
    },
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.newsletter.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "paragraph"
        },
        {
          "type": "email_form"
        }
      ]
    }
  ]
}
{% endschema %}

{{ 'component-article-card.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'section-featured-blog.css' | asset_url | stylesheet_tag }}

<noscript>{{ 'component-article-card.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-card.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'section-featured-blog.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign posts_displayed = section.settings.blog.articles_count
  if section.settings.post_limit <= section.settings.blog.articles_count
    assign posts_exceed_limit = true
    assign posts_displayed = section.settings.post_limit
  endif
-%}
{% comment %} Start Featured Blog {% endcomment %}
<div class="ignore-{{ section.settings.ignore_spacing }}" data-aos="fade-up">
  <div class="blog color-{{ section.settings.color_scheme }} gradient {% if section.settings.heading == blank %} no-heading{% endif %} {{ section.settings.blog_style }} {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
    <div class="page-width extract{% if posts_displayed < 3 %} page-width-tablet{% endif %} section-{{ section.id }}-padding">
      {% comment %} Start Featured Blog Heading {% endcomment %}
      <div class="grid featured-post">
        <div class="grid-item left">
          {%- if section.settings.caption != blank -%}
            <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
              {{ section.settings.caption | escape }}
            </p>
          {%- endif -%}
          {%- unless section.settings.heading == blank -%}
            <div class="title-wrapper--no-top-margin title-wrapper--no-bottom-margin">
              <{{ section.settings.heading_tag }}
                id="SectionHeading-{{ section.id }}"
                class="blog-title {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }} heading-bold"
              >
                {{ section.settings.heading | escape }}
              </{{ section.settings.heading_tag }}>

              {%- if section.settings.blog != blank
                and section.settings.show_view_all
                and section.settings.post_limit < section.settings.blog.articles_count
              -%}
                <a
                  href="{{ section.settings.blog.url }}"
                  class="link underlined-link large-up-hide"
                >
                  {{ 'sections.featured_blog.view_all' | t }}
                </a>
              {%- endif -%}
            </div>
          {%- endunless -%}
        </div>
        {% comment %} End Featured Blog Heading {% endcomment %}

        {% comment %} Start Featured Blog Post View All Button (Slides) {% endcomment %}
        {%- if section.settings.show_view_all and section.settings.post_limit < section.settings.blog.articles_count -%}
          <div class="grid-item right" data-aos="fade-up">
            <div class="blog-view-all small-hide medium-hide">
              <a
                href="{{ section.settings.blog.url }}"
                id="ViewAll-{{ section.id }}"
                class="link"
                aria-labelledby="ViewAll-{{ section.id }} SectionHeading-{{ section.id }}"
              >
                {{ 'sections.featured_blog.view_all' | t }}
                {% render 'icon-arrow' %}
              </a>
            </div>
          </div>
        {%- endif -%}
        {% comment %} End Featured Blog Post View All Button (Slides) {% endcomment %}
      </div>

      {% comment %} Start Featured Blog Post Content (Slides) {% endcomment %}
      {%- if section.settings.blog != blank and section.settings.blog.articles_count > 0 -%}
        <slider-component class="slider-mobile-gutter">
          {%- if section.settings.swipe_on_mobile == true -%}
            {%- if posts_exceed_limit -%}
              <div class="disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden{% if section.settings.post_limit < 3 %} medium-hide{% endif %}{% if section.settings.post_limit < 2 %} small-hide{% endif %}">
                <button
                  type="button"
                  class="slider-button slider-button--prev"
                  name="previous"
                  aria-label="{{ 'general.slider.previous_slide' | t }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
                <div class="slider-counter caption">
                  <span class="slider-counter--current">1</span>
                  <span aria-hidden="true"> / </span>
                  <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                  <span class="slider-counter--total">{{ section.settings.post_limit }}</span>
                </div>
                <button
                  type="button"
                  class="slider-button slider-button--next"
                  name="next"
                  aria-label="{{ 'general.slider.next_slide' | t }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
              </div>
            {%- endif -%}
          {%- endif -%}
          <ul
            id="Slider-{{ section.id }}"
            class="blog-posts articles-wrapper contains-card contains-card--article{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid grid--peek grid--2-col-tablet grid--{{ section.settings.columns_desktop }}-col-desktop {% if section.settings.swipe_on_mobile == true %}slider {% if posts_displayed > 2 %}slider--tablet{% else %}slider--mobile{% endif %}{% endif %}"
            role="list"
            data-aos="fade-up"
          >
            {%- for article in section.settings.blog.articles limit: section.settings.post_limit -%}
              <li
                id="Slide-{{ section.id }}-{{ forloop.index }}"
                class="blog-post grid-item article slider-slide slider-slide--full-width"
              >
                {% if section.settings.blog_style == 'blog-style-four' %}
                  {% render 'article-card',
                    blog: section.settings.blog,
                    article: article,
                    media_aspect_ratio: 1.26,
                    show_image: section.settings.show_image,
                    show_date: section.settings.show_date,
                    show_author: section.settings.show_author,
                    show_excerpt: section.settings.show_excerpt
                  %}
                {% else %}
                  {% render 'article-card',
                    blog: section.settings.blog,
                    article: article,
                    media_aspect_ratio: 1.66,
                    show_image: section.settings.show_image,
                    show_date: section.settings.show_date,
                    show_author: section.settings.show_author,
                    show_excerpt: section.settings.show_excerpt
                  %}
                {% endif %}
              </li>
            {%- endfor -%}
          </ul>
        </slider-component>
        {% comment %} End Featured Blog Post Content (Slides) {% endcomment %}

      {%- else -%}
        {% comment %} Start Featured Blog Post Placeholder {% endcomment %}
        <div class="grid" data-aos="fade-up">
          <div class="blog-placeholder grid-item">
            <div class="placeholder media media--landscape">
              {{ 'collection-5' | placeholder_svg_tag: 'placeholder-svg' }}
            </div>

            <div class="color-{{ section.settings.color_scheme_1 }} gradient blog-content">
            <h2 class="heading-bold">
              {{ 'sections.featured_blog.onboarding_title' | t }}
            </h2>
            <p>
              {{ 'sections.featured_blog.onboarding_content' | t }}
            </p>
            </div>
          </div>

          <div class="blog-placeholder grid-item">
            <div class="placeholder media media--landscape">
              {{ 'collection-6' | placeholder_svg_tag: 'placeholder-svg' }}
            </div>

            <div class="color-{{ section.settings.color_scheme_1 }} gradient blog-content">
            <h2 class="heading-bold">
              {{ 'sections.featured_blog.onboarding_title' | t }}
            </h2>
            <p>
              {{ 'sections.featured_blog.onboarding_content' | t }}
            </p>
            </div>
          </div>
        </div>
        {% comment %} End Featured Blog Post Placeholder {% endcomment %}
      {%- endif -%}
    </div>
  </div>
</div>
{% comment %} End Featured Blog {% endcomment %}

{% schema %}
{
  "name": "t:sections.featured-blog.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "text",
      "id": "caption",
      "default": "Image Caption",
      "label": "t:sections.featured-blog.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Featured Blog Posts",
      "label": "t:sections.featured-blog.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.featured-blog.settings.blog.label"
    },
    {
      "type": "select",
      "id": "blog_style",
      "options": [
        {
          "value": "blog-style-one",
          "label": "t:sections.featured-blog.settings.blog_style.options__1.label"
        },
        {
          "value": "blog-style-two",
          "label": "t:sections.featured-blog.settings.blog_style.options__2.label"
        },
        {
          "value": "blog-style-three",
          "label": "t:sections.featured-blog.settings.blog_style.options__3.label"
        },
        {
          "value": "blog-style-four",
          "label": "t:sections.featured-blog.settings.blog_style.options__4.label"
        }
      ],
      "default": "blog-style-four",
      "label": "t:sections.featured-blog.settings.blog_style.label"
    },
    {
      "type": "range",
      "id": "post_limit",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.post_limit.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_author.label"
    },
    {
      "type": "checkbox",
      "id": "show_excerpt",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_excerpt.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_view_all.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-3"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-blog.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.featured-blog.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.all.disable_arrow_mobile.label"
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-blog.presets.name",
      "settings": {
        "blog": "News"
      }
    }
  ]
}
{% endschema %}

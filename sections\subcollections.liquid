{{ 'section-featured-collections.css' | asset_url | stylesheet_tag }}
{{ 'collection-list-styles.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider
    assign show_desktop_slider = true
  endif
-%}

{% assign subcollections = collection.metafields.custom.subcollections.value %}
{% if subcollections != blank %}
  <div class="ignore-{{ section.settings.ignore_spacing }}">
  <div class="subcollections color-{{ section.settings.color_scheme }} gradient {{ section.settings.subcollection_list_style }} {% if section.settings.swipe_on_mobile == false %}swipe-mobile-false{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin" data-aos="fade-up">
    <div class="collection page-width section-{{ section.id }}-padding">

      <slider-component class="slider-mobile-gutter {% if show_desktop_slider %} slider-component-desktop{% endif %}  {% if show_mobile_slider == true and show_desktop_slider == false  %} slider-buttons-desktop-hide{% endif %} {% if show_mobile_slider == false and show_desktop_slider == true  %} slider-buttons-mobile-hide{% endif %}">
        <div class="collection-info grid">
          <div class="grid_item">
            {%- unless section.settings.title == blank and section.settings.caption == blank -%}
              <div class="subcollections-title">
                <p class="image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}">
                  {{ section.settings.caption | escape }}
                </p>
                <{{ section.settings.heading_tag }} class="title {{ section.settings.heading_size }} heading-bold">
                  {{ section.settings.title | escape }}
                </{{ section.settings.heading_tag }}>
              </div>
            {%- endunless -%}
          </div>
          <div class="grid_item">
            {%- if show_mobile_slider or show_desktop_slider -%}
              <div class="disable-slider-arrows-{{ section.settings.disable_arrow_mobile }} slider-buttons no-js-hidden">
                <button
                  type="button"
                  class="slider-button slider-button--prev"
                  name="previous"
                  aria-label="{{ 'general.slider.previous_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
                <button
                  type="button"
                  class="slider-button slider-button--next"
                  name="next"
                  aria-label="{{ 'general.slider.next_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  {% render 'icon-slider-arrows' %}
                </button>
              </div>
            {%- endif -%}
          </div>
        </div>

        {% comment %} Start Subcollection {% endcomment %}
        <ul
          id="Slider-{{ section.id }}"
          class="featured-collections-wrapper grid product-grid contains-card contains-card--product{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop{% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
          role="list"
          aria-label="{{ 'general.slider.name' | t }}"
        >
          {% for subcollection in subcollections limit: section.settings.collections_to_show %}
            <li
              id="Slide-{{ section.id }}-{{ forloop.index }}"
              class="grid-item{% if show_mobile_slider or show_desktop_slider %} slider-slide{% endif %}"
            >
              {% render 'card-collection-one',
                card_collection: subcollection,
                media_aspect_ratio: section.settings.image_ratio
              %}
            </li>
          {%- endfor -%}
        </ul>
        {% comment %} End Subcollection {% endcomment %}
      </slider-component>
    </div>
  </div>
  </div>
{% endif %}

{% schema %}
{
  "name": "t:sections.subcollections.name",
  "class": "section section-subcollections",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.subcollections.settings.info.content"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Title",
      "label": "t:sections.subcollections.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "text",
      "id": "caption",
      "default": "Caption",
      "label": "t:sections.subcollections.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "select",
      "id": "subcollection_list_style",
      "options": [
        {
          "value": "collection-list-one",
          "label": "t:sections.subcollections.settings.subcollection_list_style.options__1.label"
        },
        {
          "value": "collection-list-two",
          "label": "t:sections.subcollections.settings.subcollection_list_style.options__2.label"
        },
        {
          "value": "collection-list-three",
          "label": "t:sections.subcollections.settings.subcollection_list_style.options__3.label"
        }
      ],
      "default": "collection-list-one",
      "label": "t:sections.subcollections.settings.subcollection_list_style.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.subcollections.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.subcollections.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.subcollections.settings.image_ratio.options__3.label"
        }
      ],
      "default": "square",
      "label": "t:sections.subcollections.settings.image_ratio.label",
      "info": "t:sections.subcollections.settings.image_ratio.info"
    },
    {
      "type": "range",
      "id": "collections_to_show",
      "min": 1,
      "max": 25,
      "step": 1,
      "default": 4,
      "label": "t:sections.subcollections.settings.collections_to_show.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 5,
      "label": "t:sections.subcollections.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "enable_desktop_slider",
      "label": "t:sections.subcollections.settings.enable_desktop_slider.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.subcollections.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "1",
      "label": "t:sections.related-products.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.related-products.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.related-products.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.subcollections.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "disable_arrow_mobile",
      "default": true,
      "label": "t:sections.all.disable_arrow_mobile.label"
    }
    ]
}
{% endschema %}

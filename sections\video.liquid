{{ 'video-section.css' | asset_url | stylesheet_tag }}
{{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{% comment %} Start Video Content {% endcomment %}
<div class="ignore-{{ section.settings.ignore_spacing }} video-{{ section.settings.video_layout }}">
<div class="color-{{ section.settings.color_scheme }} gradient {% unless section.settings.full_width %} page-width{% endunless %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
  <div
    class="video-section {% if section.settings.video_layout == 'layout-two' %}grid{% endif %} extract section-{{ section.id }}-padding"
    data-aos="fade-up"
  >
    <noscript>
      <div
        class="video-section-media {% if section.settings.video_layout == 'layout-two' %}grid-item right-side{% endif %}"
        {% if section.settings.cover_image != blank and section.settings.video_layout == 'layout-two' %}
          style="padding-bottom: {{ 1 | divided_by: section.settings.cover_image.aspect_ratio | times: 60 }}%;"
        {% endif %}
      >
        <a
          href="{{ section.settings.video_url }}"
          class="video-section-poster media media--transparent media--landscape{% if section.settings.cover_image == blank %} video-section-placeholder{% endif %}"
        >
          {%- if section.settings.cover_image != blank -%}
            {%- capture sizes -%}
              {% if section.settings.full_width -%}
                100vw
              {%- else -%}
                (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px)
                calc(100vw - 10rem), 100vw
              {%- endif %}
            {%- endcapture -%}
            {%- assign alt = 'sections.video.load_video' | t: description: section.settings.description | escape -%}
            {{
              section.settings.cover_image
              | image_url: width: 3840
              | image_tag:
                loading: 'lazy',
                sizes: sizes,
                widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840',
                alt: alt
            }}
          {%- else -%}
            {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
          {%- endif -%}
        </a>
      </div>
    </noscript>
    <deferred-media
      class="video-section-media deferred-media {% if section.settings.video_layout == 'layout-two' %}grid-item right-side{% endif %}  no-js-hidden global-media-settings{% if section.settings.full_width %} global-media-settings--full-width{% endif %} {% if section.settings.cover_image == blank %} video-section-placeholder{% endif %}"
      data-media-id="{{ section.settings.video_url.id }}"
      {% if section.settings.cover_image != blank and section.settings.video_layout == 'layout-two' %}
        style="padding-bottom: {{ 1 | divided_by: section.settings.cover_image.aspect_ratio | times: 60 }}%;"
      {% endif %}
    >
      <button
        id="Deferred-Poster-Modal-{{ section.settings.video_url.id }}"
        class="video-section-poster media deferred-media-poster media--landscape"
        type="button"
        aria-label="{{ 'sections.video.load_video' | t: description: section.settings.description | escape }}"
      >
        {%- if section.settings.cover_image != blank -%}
          {%- capture sizes -%}
            {% if section.settings.full_width -%}
              100vw
            {%- else -%}
              (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px)
              calc(100vw - 10rem), 100vw
            {%- endif %}
          {%- endcapture -%}
          {%- assign alt = 'sections.video.load_video' | t: description: section.settings.description | escape -%}
          {{
            section.settings.cover_image
            | image_url: width: 3840
            | image_tag: loading: 'lazy', sizes: sizes, widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840', alt: alt
          }}
        {%- else -%}
          {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
        {%- endif -%}
        <span class="video-icon deferred-media-poster-button motion-reduce">
          {%- render 'icon-play' -%}
          <span class="video-icon-sonar"></span>
        </span>
      </button>
      <template>
        {%- if section.settings.video_url.type == 'youtube' -%}
          <iframe
            src="https://www.youtube.com/embed/{{ section.settings.video_url.id }}?enablejsapi=1&autoplay=1"
            class="js-youtube"
            allow="autoplay; encrypted-media"
            allowfullscreen
            title="{{ section.settings.description | escape }}"
          ></iframe>
        {%- else -%}
          <iframe
            src="https://player.vimeo.com/video/{{ section.settings.video_url_hosted }}?autoplay=1"
            class="js-vimeo"
            allow="autoplay; encrypted-media"
            allowfullscreen
            title="{{ section.settings.description | escape }}"
          ></iframe>
        {%- endif -%}
      </template>
    </deferred-media>

    
    <div class="{% if section.settings.full_width %}page-width{% endif %} {% if section.settings.video_layout == 'layout-two' %}grid-item left-side{% endif %} {% if section.settings.video_layout == 'layout-one' %}video-layout-one{% endif %} color-{{ section.settings.color_scheme_1 }} gradient {% if section.settings.caption == blank and section.settings.heading == blank and section.settings.text == blank and section.settings.button_label_1 == blank %}no-padding{% endif %}">
      {%- unless section.settings.caption == blank -%}
        <p
          class="video-caption image-with-text-text image-with-text-text--caption {{ section.settings.text_style }} {{ section.settings.text_style }}--{{ section.settings.text_size }} {{ section.settings.text_style }}"
          {{ section.shopify_attributes }}
        >
          {{ section.settings.caption | escape }}
        </p>
      {%- endunless -%}
      {%- unless section.settings.heading == blank -%}
        <div class="title-wrapper title-wrapper--no-top-margin">
          <{{ section.settings.heading_tag }} class="title heading-bold {{ section.settings.heading_size }} heading-{{ section.settings.heading_style }}">
            {{- section.settings.heading -}}
          </{{ section.settings.heading_tag }}>
        </div>
      {%- endunless -%}
      {%- unless section.settings.text == blank -%}
        <div
          class="{% if section.settings.video_layout == 'layout-one' %}page-width--narrow{% endif %} rich-text-text rte"
          {{ section.shopify_attributes }}
        >
          {{ section.settings.text }}
        </div>
      {%- endunless -%}

      <div
        class="banner-buttons"
        {{ section.shopify_attributes }}
      >
        {%- if section.settings.button_label_1 != blank -%}
          <a
            {% if section.settings.button_link_1 == blank %}
              role="link" aria-disabled="true"
            {% else %}
              href="{{ section.settings.button_link_1}}"
            {% endif %}
            class="button-arrow button button--primary"
          >
            {{ section.settings.button_label_1 }}
            {%- if settings.show_button_arrow -%}{% render 'icon-slider-arrows' %}{% endif %}
          </a>
        {%- endif -%}
      </div>
      
    </div>

  </div>
</div>
</div>
{% comment %} End Video Content {% endcomment %}

{% schema %}
{
  "name": "t:sections.video.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "text",
      "id": "caption",
      "default": "Caption",
      "label": "t:sections.video.settings.caption.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "subtitle",
          "label": "t:sections.all.text_style.options__1.label"
        },
        {
          "value": "caption-with-letter-spacing",
          "label": "t:sections.all.text_style.options__2.label"
        }
      ],
      "default": "caption-with-letter-spacing",
      "label": "t:sections.all.text_style.label"
    },
    {
      "type": "select",
      "id": "text_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.text_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.text_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.text_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.text_size.label"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Video",
      "label": "t:sections.video.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "extra-large",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.heading_style.options__1.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.all.heading_style.options__2.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.heading_style.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
        {
          "value": "h1",
          "label": "t:sections.all.heading_tag.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_tag.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_tag.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_tag.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_tag.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_tag.options__6.label"
        }
      ],
      "default": "h2",
      "label": "t:sections.all.heading_tag.label",
      "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "richtext",
      "id": "text",
      "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>",
      "label": "t:sections.video.settings.text.label"
    },
    {
      "type": "text",
      "id": "button_label_1",
      "default": "Button label",
      "label": "t:sections.video.settings.button_label_1.label"
    },
    {
      "type": "url",
      "id": "button_link_1",
      "label": "t:sections.video.settings.button_link_1.label"
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "t:sections.video.settings.cover_image.label"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": ["youtube", "vimeo"],
      "label": "t:sections.video.settings.video_url.label",
      "placeholder": "t:sections.video.settings.video_url.placeholder",
      "info": "t:sections.video.settings.video_url.info"
    },
    {
      "type": "text",
      "id": "description",
      "label": "t:sections.video.settings.description.label",
      "info": "t:sections.video.settings.description.info"
    },
    {
      "type": "select",
      "id": "video_layout",
      "options": [
        {
          "value": "layout-one",
          "label": "t:sections.video.settings.video_layout.options__1.label"
        },
        {
          "value": "layout-two",
          "label": "t:sections.video.settings.video_layout.options__2.label"
        }
      ],
      "default": "layout-two",
      "label": "t:sections.video.settings.video_layout.label"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.video.settings.full_width.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_1",
      "label": "t:sections.all.colors_box.label",
      "default": "option-2"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:sections.video.presets.name"
    }
  ]
}
{% endschema %}

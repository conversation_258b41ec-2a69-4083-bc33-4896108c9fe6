{% assign pasties = "Traditional Cornish Pasty, Hog Roast, Steak & Ale, Chicken Bacon & Chorizo, All Day Breakfast, Pork & Apple, Creamy Chicken & Leek, Steak & Stilton, Chicken Curry, Steak & Gravy, Peppered Steak, Cheese & Ham, Red Thai Chicken, Lamb & Mint, Chilli Beef, Cheese & Onion (V), Spinach & Ricotta (V), Broccoli Cheese & Sweetcorn (V), Vegetable (V), Spicy Mediterranean Vegetable (Vegan), Red Thai Vegetable Curry (Vegan), Curried Cauliflower & Onion Bhaji (Vegan), Wholemeal Vegetable (Vegan), Steakless Steak (Vegan), Dutch Apple & Custard, Apple & Blackcurrant" | split: ', ' %}
{% if product.template == '10-Choice-Pasties' %}
{% assign item_amount = 10 %}
{% elsif product.template == '8-Choice-Pasties' %}
  {% assign item_amount = 8 %}
{% else %}
{% elsif product.template == '6-Choice-Pasties' %}
  {% assign item_amount = 6 %}
{% endif %}

{% for i in (1..item_amount) %}
  <div class="product-pasty-selector">
    <label for="Pasty">Pasty {{ i }}</label>
    <select required="required" data-pasty="{{ i }}" class="pastySelector">
      <option value="">Choose a pasty...</option>
      {% for pasty in pasties %}
        <option value="{{ pasty }}">{{ pasty }}</option>
      {% endfor %}
    </select>
  </div>
{% endfor %}

<div class="pastySaveBtn">
  <br>
  <button type="button" name="save" id="SaveChoices--{{ section.id }}" class="btn">
    <span class="btn__text">
      Save
    </span>
  </button>
</div>

<div id="pastyAttributes"></div>
<style>
  .product-pasty-selector select {
    font-style: italic;
    font-size: 1.125em;
    border-color: transparent;
    background-color: #f6f6f6;
    color: #1c1d1d;
    -webkit-transition: opacity .4s ease-out;
    -moz-transition: opacity .4s ease-out;
    -ms-transition: opacity .4s ease-out;
    -o-transition: opacity .4s ease-out;
    transition: opacity .4s ease-out;
    max-width: 100%;
    padding: 8px 10px;
    border-radius: 0;
  }
  .pasty-save-button {
    margin-top: 20px;
  }
  .disabledPasty {
    opacity: 0.5;
    pointer-events: none;
  }
  .pastySaveBtn button {
    background-color: #000000;
    color: #ffff;
    padding: 12px 20px;
    cursor: pointer;
  }
</style>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script>
  $(document).ready(function() {
    var pasties = new Array({{ item_amount }});
    var isSaved = false;
    $('.product-single__add-to-cart').addClass('disabledPasty');
    
    $("#SaveChoices--{{ section.id }}").on('click', function(e) {
      if (getPastyTotal() < {{ item_amount }}) {
        alert('Please select all pasties before saving.');
        return false;
      } else {
        isSaved = true;
        updatePastyAttributes();
        $('.product-single__add-to-cart').removeClass('disabledPasty');
      }
    });
    
    $(".pastySelector").on('change', function(e) {
      var pastyNumber = $(this).data('pasty');
      pasties[pastyNumber - 1] = $(this).val();
      
      // Reset saved state when any selection changes
      isSaved = false;
      $('.product-single__add-to-cart').addClass('disabledPasty');
    });
    
    // Intercept the add to cart form submission
    $('form[action="/cart/add"]').on('submit', function(e) {
      if (!isSaved) {
        e.preventDefault();
        alert('Please save your pasty selections before adding to cart.');
        return false;
      }
      
      // Ensure properties are updated before submission
      updatePastyAttributes();
    });
    
    function getPastyTotal() {
      var selected = 0;
      $('.pastySelector').each(function() {
        if ($(this).val() !== '') {
          selected++;
        }
      });
      return selected;
    }
    
    function updatePastyAttributes() {
      // Remove any existing properties
      $('input[name^="properties["]').remove();
      
      var pastyTotals = pastyCounter(pasties);
      var form = $('form[action="/cart/add"]');
      
      $.each(pastyTotals, function(pasty, count) {
        if (count > 0) {
          var input = $('<input>')
            .attr('type', 'hidden')
            .attr('name', 'properties[' + pasty + ']')
            .val(count);
          form.append(input);
        }
      });
    }
    
    function pastyCounter(pasties) {
      var result = {};
      for(var i = 0; i < pasties.length; i++) {
        if (pasties[i]) {
          if(!result[pasties[i]]) {
            result[pasties[i]] = 0;
          }
          result[pasties[i]]++;
        }
      }
      return result;
    }
  });
</script>

{{ 'customer.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="customer register section-{{ section.id }}-padding">
    <svg style="display: none">
      <symbol id="icon-error" viewBox="0 0 16 16">
        <path fill="#b30000" d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/>
      </symbol>
    </svg>

    <h1>
      {{ 'customer.register.title' | t }}
    </h1>
    {%- form 'create_customer', novalidate: 'novalidate' -%}
      {%- if form.errors -%}
        <h2 class="form-message" tabindex="-1" autofocus>
          <svg aria-hidden="true" focusable="false">
            <use href="#icon-error" />
          </svg>
          {{ 'templates.contact.form.error_heading' | t }}
        </h2>
        <ul>
          {%- for field in form.errors -%}
            <li>
              {%- if field == 'form' -%}
                {{ form.errors.messages[field] }}
              {%- else -%}
                <a href="#RegisterForm-{{ field }}">
                  {{ form.errors.translated_fields[field] | capitalize }}
                  {{ form.errors.messages[field] }}
                </a>
              {%- endif -%}
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}
      <div class="field">
        <input
          type="text"
          name="customer[first_name]"
          id="RegisterForm-FirstName"
          {% if form.first_name %}
            value="{{ form.first_name }}"
          {% endif %}
          autocomplete="given-name"
          placeholder="{{ 'customer.register.first_name' | t }}"
        >
        <label for="RegisterForm-FirstName">
          {{ 'customer.register.first_name' | t }}
        </label>
      </div>
      <div class="field">
        <input
          type="text"
          name="customer[last_name]"
          id="RegisterForm-LastName"
          {% if form.last_name %}
            value="{{ form.last_name }}"
          {% endif %}
          autocomplete="family-name"
          placeholder="{{ 'customer.register.last_name' | t }}"
        >
        <label for="RegisterForm-LastName">
          {{ 'customer.register.last_name' | t }}
        </label>
      </div>
      <div class="field">
        <input
          type="email"
          name="customer[email]"
          id="RegisterForm-email"
          {% if form.email %}
            value="{{ form.email }}"
          {% endif %}
          spellcheck="false"
          autocapitalize="off"
          autocomplete="email"
          aria-required="true"
          {% if form.errors contains 'email' %}
            aria-invalid="true"
            aria-describedby="RegisterForm-email-error"
          {% endif %}
          placeholder="{{ 'customer.register.email' | t }}"
        >
        <label for="RegisterForm-email">
          {{ 'customer.register.email' | t }}
        </label>
      </div>
      {%- if form.errors contains 'email' -%}
        <span id="RegisterForm-email-error" class="form-message">
          <svg aria-hidden="true" focusable="false">
            <use href="#icon-error" />
          </svg>
          {{ form.errors.translated_fields.email | capitalize }}
          {{ form.errors.messages.email }}.
        </span>
      {%- endif -%}
      <div class="field">
        <input
          type="password"
          name="customer[password]"
          id="RegisterForm-password"
          aria-required="true"
          {% if form.errors contains 'password' %}
            aria-invalid="true"
            aria-describedby="RegisterForm-password-error"
          {% endif %}
          placeholder="{{ 'customer.register.password' | t }}"
        >
        <label for="RegisterForm-password">
          {{ 'customer.register.password' | t }}
        </label>
      </div>
      {%- if form.errors contains 'password' -%}
        <span id="RegisterForm-password-error" class="form-message">
          <svg aria-hidden="true" focusable="false">
            <use href="#icon-error" />
          </svg>
          {{ form.errors.translated_fields.password | capitalize }}
          {{ form.errors.messages.password }}.
        </span>
      {%- endif -%}
      <button>
        {{ 'customer.register.submit' | t }}
      </button>
    {%- endform -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-register.name",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}

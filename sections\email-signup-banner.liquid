{{ 'newsletter-section.css' | asset_url | stylesheet_tag }}
{{ 'component-newsletter.css' | asset_url | stylesheet_tag }}
{{ 'component-rte.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .newsletter-banner .banner-media,
  .newsletter-banner .media > *:not(.zoom):not(.deferred-media-poster-button) {
    position: relative;
  }

  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{%- endstyle -%}

{% comment %} Start Newsletter Banner {% endcomment %}
<div class="ignore-{{ section.settings.ignore_spacing }} section-{{ section.id }}-padding">
  <div class="newsletter-layout {% if section.settings.full_width %}newsletter-full-width{% endif %} page-width {% if section.settings.newsletter_style =='style_three' %}style-three{% endif %} {% if section.settings.newsletter_style =='style_two' %}style-two{% endif %} margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin">
    <div class="grid newsletter-banner banner ignore-{{ section.settings.ignore_spacing }} {% if section.settings.layout == 'text_first' %} layout-reverse{% endif %} color-{{ section.settings.color_scheme }} gradient">
      {% comment %} Start Image Background {% endcomment %}
      <div class="grid-item banner-media{% if section.settings.image != blank %} media{% endif %}" data-aos="fade-up">
        {%- if section.settings.image != blank -%}
          {%- capture sizes -%}
            (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
            (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
          {%- endcapture -%}
          {{
            section.settings.image
            | image_url: width: 1500
            | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
          }}
        {%- else -%}
          {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
        {%- endif -%}
      </div>
      {% comment %} End Image Background {% endcomment %}

      {% if section.settings.newsletter_style == 'style_two' %}
        {% comment %} Start Image Background {% endcomment %}
        <div
          class="grid-item banner-media{% if section.settings.image_2 != blank %} media{% endif %}"
          data-aos="fade-up"
        >
          {%- if section.settings.image_2 != blank -%}
            {%- capture sizes -%}
            (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
            (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
          {%- endcapture -%}
            {{
              section.settings.image_2
              | image_url: width: 1500
              | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
            }}
          {%- else -%}
            {{ 'product-2' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}
        </div>
        {% comment %} End Image Background {% endcomment %}
      {% endif %}

      {% comment %} Start Newsletter Content {% endcomment %}
      <div class="grid-item newsletter-content page-width" data-aos="fade-up">
        <div class="newsletter-banner-box">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'caption' -%}
                <p
                  class="newsletter-banner-caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.caption | escape }}
                </p>
              {%- when 'heading' -%}
                <{{ block.settings.heading_tag }}
                  class="newsletter-banner-heading heading-bold {{ block.settings.heading_size }} heading-{{ block.settings.heading_style }}"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.heading | escape }}
                </{{ block.settings.heading_tag }}>
              {%- when 'paragraph' -%}
                <div class="newsletter-subheading rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                  {{ block.settings.text }}
                </div>
              {%- when 'email_form' -%}
                <div {{ block.shopify_attributes }}>
                  {% form 'customer', class: 'newsletter-form' %}
                    <input type="hidden" name="contact[tags]" value="newsletter">
                    <div class="newsletter-form__field-wrapper animate-arrow">
                      <div class="field">
                        <input
                          id="NewsletterForm--{{ section.id }}"
                          type="email"
                          name="contact[email]"
                          class="field-input email-input"
                          value="{{ form.email }}"
                          aria-required="true"
                          autocorrect="off"
                          autocapitalize="off"
                          autocomplete="email"
                          {% if form.errors %}
                            autofocus
                            aria-invalid="true"
                            aria-describedby="Newsletter-error--{{ section.id }}"
                          {% elsif form.posted_successfully? %}
                            aria-describedby="Newsletter-success--{{ section.id }}"
                          {% endif %}
                          placeholder="{{ 'newsletter.label' | t }}"
                          required
                        >
                        <label class="field-label" for="NewsletterForm--{{ section.id }}">
                          {{ 'newsletter.label' | t }}
                        </label>
                        <button
                          type="submit"
                          class="newsletter-form__button field__button"
                          name="commit"
                          id="BannerSubscribe"
                          aria-label="{{ 'newsletter.button_label' | t }}"
                        >
                          {% render 'icon-arrow' %}
                        </button>
                      </div>
                      {%- if form.errors -%}
                        <small class="newsletter-form-message form-message" id="Newsletter-error--{{ section.id }}">
                          {%- render 'icon-error' -%}
                          {{- form.errors.translated_fields.email | capitalize }}
                          {{ form.errors.messages.email -}}
                        </small>
                      {%- endif -%}
                    </div>
                    {%- if form.posted_successfully? -%}
                      <h3
                        class="newsletter-form-message newsletter-form-message--success form-message"
                        id="Newsletter-success--{{ section.id }}"
                        tabindex="-1"
                        autofocus
                      >
                        {% render 'icon-success' -%}
                        {{- 'newsletter.success' | t }}
                      </h3>
                    {%- endif -%}
                  {% endform %}
                </div>
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
      {% comment %} End Newsletter Content {% endcomment %}
    </div>
  </div>
</div>
{% comment %} End Newsletter Banner {% endcomment %}

{% schema %}
{
  "name": "t:sections.newsletter-banner.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.newsletter-banner.settings.paragraph.content"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.newsletter-banner.settings.image.label"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "t:sections.newsletter-banner.settings.image_2.label",
      "info": "t:sections.newsletter-banner.settings.image_2.info"
    },
    {
      "type": "select",
      "id": "newsletter_style",
      "options": [
        {
          "value": "style_one",
          "label": "t:sections.newsletter-banner.settings.newsletter_style.options__1.label"
        },
        {
          "value": "style_two",
          "label": "t:sections.newsletter-banner.settings.newsletter_style.options__2.label"
        },
        {
          "value": "style_three",
          "label": "t:sections.newsletter-banner.settings.newsletter_style.options__3.label"
        }
      ],
      "default": "style_two",
      "label": "t:sections.newsletter-banner.settings.newsletter_style.label"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.newsletter-banner.settings.layout.options__1.label"
        },
        {
          "value": "text_first",
          "label": "t:sections.newsletter-banner.settings.layout.options__2.label"
        }
      ],
      "default": "image_first",
      "label": "t:sections.newsletter-banner.settings.layout.label"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.newsletter-banner.settings.full_width.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "default": false,
      "label": "t:sections.all.ignore_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_margin_heading"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "caption",
      "name": "t:sections.newsletter-banner.blocks.caption.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Add a tagline",
          "label": "t:sections.newsletter-banner.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.newsletter-banner.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.newsletter-banner.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.newsletter-banner.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.newsletter-banner.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.newsletter-banner.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.newsletter-banner.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.newsletter-banner.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.newsletter-banner.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Subscribe to our Newsletter",
          "label": "t:sections.newsletter-banner.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "extra-large",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "large",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "medium",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "large",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "heading_style",
          "options": [
            {
              "value": "default",
              "label": "t:sections.all.heading_style.options__1.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.all.heading_style.options__2.label"
            }
          ],
          "default": "default",
          "label": "t:sections.all.heading_style.label"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "options": [
            {
              "value": "h1",
              "label": "t:sections.all.heading_tag.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_tag.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_tag.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_tag.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_tag.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_tag.options__6.label"
            }
          ],
          "default": "h2",
          "label": "t:sections.all.heading_tag.label",
          "info": "t:sections.all.heading_tag.info"
        }
      ]
    },
    {
      "type": "paragraph",
      "name": "t:sections.newsletter-banner.blocks.paragraph.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Don't miss out on this opportunity to stay in the loop on all things organic skincare. Sign up for our newsletter today!</p>",
          "label": "t:sections.newsletter-banner.blocks.paragraph.settings.paragraph.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.newsletter-banner.blocks.paragraph.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.newsletter-banner.blocks.paragraph.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.newsletter-banner.blocks.paragraph.settings.text_style.label"
        }
      ]
    },
    {
      "type": "email_form",
      "name": "t:sections.newsletter-banner.blocks.email_form.name",
      "limit": 1
    }
  ],
  "presets": [
    {
      "name": "t:sections.newsletter-banner.presets.name",
      "blocks": [
        {
          "type": "caption"
        },
        {
          "type": "heading"
        },
        {
          "type": "paragraph"
        },
        {
          "type": "email_form"
        }
      ]
    }
  ]
}
{% endschema %}

{"settings_schema": {"global": {"settings": {"header__border": {"content": "Bordure"}, "header__shadow": {"content": "Ombre"}, "header__exclusions": {"content": "Exclusions"}, "global_shadow_opacity": {"label": "Opacité"}, "global_shadow_blur": {"label": "<PERSON><PERSON>"}, "global_border_radius": {"label": "Rayon d'angle"}, "show_button_arrow": {"label": "Afficher la flèche du bouton"}, "button_style": {"options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Moderne"}, "options__3": {"label": "Élégant"}, "label": "Style de bouton"}, "global_shadow_horizontal_offset": {"label": "Décalage horizontal"}, "global_shadow_vertical_offset": {"label": "Décalage vertical"}, "global_border_thickness": {"label": "Épaisseur"}, "global_border_opacity": {"label": "Opacité"}, "exclude_drawer": {"label": "Exclure les tiroirs (mobile)"}, "exclude_popup": {"label": "Exclure les popups"}, "exclude_inputs": {"label": "Exclure les entrées"}, "image_padding": {"label": "Marge de l'image"}, "text_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du texte"}, "card_icons_size": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "label": "Taille des icônes"}}}, "global_design": {"name": "Design global"}, "breadcrumbs": {"name": "Fil d'Ariane", "settings": {"show_breadcrumb_nav": {"label": "Afficher la navigation par fil d'Ariane"}, "breadcrumbs_style": {"options__1": {"label": "Bordure"}, "options__2": {"label": "<PERSON><PERSON> de bordure"}, "options__3": {"label": "Arrière-plan"}, "label": "Style"}}}, "animations": {"name": "Animations", "settings": {"deactivate_animation": {"label": "Désactiver l'animation au défilement"}, "deactivate_menu_animation": {"label": "Désactiver l'animation du menu de navigation"}, "page_scroll_indicator": {"label": "Activer l'indicateur de défilement de page"}, "scroll_indicator_color": {"label": "<PERSON><PERSON>ur de l'indicateur de défilement"}, "heading__1": {"content": "<PERSON>ur de page"}, "page_loader_enable": {"label": "<PERSON><PERSON> le chargeur"}, "loader_background_color": {"label": "Couleur de fond du chargeur"}, "loader_text_color": {"label": "Couleur du texte du chargeur"}, "page_loader_text": {"label": "Texte du chargeur de page"}, "page_loader_style": {"options__1": {"label": "Toujours"}, "options__2": {"label": "Première fois"}, "label": "Le texte du chargeur apparaît"}}}, "color_swatches": {"name": "Nuanciers de Couleurs", "settings": {"info": {"content": "Ren<PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches) pour en savoir plus"}, "swatch_enable": {"label": "Activer les Nuanciers de Couleurs"}, "color_map": {"label": "Couleurs:", "info": "Ajoutez des noms de couleurs de variantes et des valeurs de couleur dans ce format : 'Noir : #000000'. Une couleur par ligne. Vous pouvez également ajouter une image en utilisant l'identifiant de l'image dans ce format : 'Naturel : nature.png'."}}}, "cards": {"name": "Cartes produit", "settings": {"card_metafield_key": {"label": "Clé du métachamp de la carte"}, "enable_tooltip": {"label": "Activer l’info-bulle"}}}, "quick_view": {"name": "Vue rapide", "settings": {"quick_view_product_gallery_width": {"label": "Largeur de la galerie de produits"}, "quick_view_height": {"label": "Hauteur de la vue rapide"}}}, "collection_cards": {"name": "Cartes de collection"}, "blog_cards": {"name": "Cartes d'article de blog"}, "badges": {"name": "Insignes", "settings": {"position": {"options__1": {"label": "En bas à gauche"}, "options__2": {"label": "En bas à droite"}, "options__3": {"label": "En haut à gauche"}, "options__4": {"label": "En haut à droite"}, "label": "Position sur les cartes"}, "badge_discount": {"label": "Afficher le pourcentage de réduction"}, "header": {"content": "<PERSON><PERSON><PERSON>"}, "info": {"content": "Ren<PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/product-card-badges) pour en savoir plus"}, "custom_badge_text": {"label": "Texte du badge personnalisé"}, "custom_badge_tag": {"label": "Étiquette du badge personnalisé", "info": "Assurez-vous d'utiliser la même étiquette que celle que vous avez ajoutée aux balises des produits."}, "custom_badge_color": {"label": "Couleur du texte"}, "custom_badge_background": {"label": "Couleur de l'arrière-plan"}}}, "colors": {"name": "<PERSON><PERSON><PERSON> de Couleurs", "settings": {"background": {"label": "Arrière-plan"}, "background_gradient": {"label": "Dégradé d'arrière-plan", "info": "Le dégradé d'arrière-plan remplace l'arrière-plan lorsque c'est possible."}, "text": {"label": "Texte"}, "button_background": {"label": "Arrière-plan de bouton solide"}, "button_label": {"label": "Étiquette de bouton solide"}, "secondary_button_label": {"label": "Bouton de contour"}, "color_link": {"label": "Couleur du lien"}, "shadow": {"label": "Ombre"}}}, "colors_add": {"name": "Couleurs Supplémentaires", "settings": {"heading__1": {"content": "Arrière-plans spéciaux"}, "background_color_3": {"label": "Arrière-plan de la boîte de contenu", "info": "Utilisé comme couleur d'arrière-plan pour les boîtes de contenu à l'intérieur des sections."}, "background_color_2": {"label": "Arrière-plan de décoration", "info": "Utilisé comme couleur d'arrière-plan de décoration dans diverses sections."}, "heading__3": {"content": "Couleurs d'accentuation"}, "accent_background_color_1": {"label": "Couleur d'arrière-plan d'accentuation 1", "info": "Utilisée comme couleur d'arrière-plan d'accentuation primaire."}, "accent_color_1": {"label": "Couleur de texte d'accentuation 1", "info": "Utilisée comme couleur de texte d'accentuation primaire."}, "accent_color_2": {"label": "Couleur d'arrière-plan d'accentuation 2", "info": "Utilisée comme couleur d'arrière-plan d'accentuation secondaire."}, "accent_text_color_2": {"label": "Couleur de texte d'accentuation 2", "info": "Utilisée comme couleur de texte d'accentuation secondaire."}, "heading__4": {"content": "Couleurs de bordure"}, "border_color_1": {"label": "<PERSON><PERSON><PERSON> de bordure", "info": "Utilisée comme couleur de bordure."}, "border_color_2": {"label": "Couleur de bordure 2", "info": "Utilisée comme couleur de bordure secondaire."}, "heading__5": {"content": "Boutons Spéciaux"}, "button_quick_add_background_color": {"label": "Couleur de fond du bouton Ajouter rapidement", "info": "Utilisé pour la position par défaut (style collection élégante)"}, "button_quick_add_text_color": {"label": "Couleur du texte du bouton Ajouter rapidement", "info": "Utilisé pour la position par défaut (style collection élégante)"}, "button_quick_add_background_color_hover": {"label": "Couleur de fond du bouton Ajouter rapidement au survol", "info": "Utilisé pour la position par défaut (style collection élégante)"}, "button_quick_add_text_color_hover": {"label": "Couleur du texte du bouton Ajouter rapidement au survol", "info": "Utilisé pour la position par défaut (style collection élégante)"}, "heading__6": {"content": "Autres couleurs"}, "countdown_background_top": {"label": "Arrière-plan du compte à rebours premier"}, "countdown_text_top": {"label": "Texte du compte à rebours premier"}, "countdown_background_bottom": {"label": "Arrière-plan du compte à rebours second"}, "countdown_text_bottom": {"label": "Texte du compte à rebours second"}, "opacity_color": {"label": "Couleur d'opacité globale"}, "color_link": {"label": "Couleur du lien"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_h1": {"label": "Attribuer h1 au logo et au titre"}, "logo_width": {"label": "Largeur du logo pour ordinateur de bureau"}, "logo_width_mobile": {"label": "Largeur du logo mobile"}, "favicon": {"label": "Image favicon", "info": "Sera réduite à 32 x 32 pixels"}}}, "brand_information": {"name": "Informations sur la marque", "settings": {"paragraph": {"content": "Ajoutez une description de la marque au pied de page de votre magasin."}, "brand_headline": {"label": "Titre"}, "brand_description": {"label": "Description"}, "brand_image": {"label": "Image"}, "brand_image_width": {"label": "Largeur de l'image"}}}, "typography": {"name": "Typographie", "settings": {"type_header_font": {"label": "Police de caractères", "info": "Sélectionner une police différente peut affecter la vitesse de votre magasin. [En savoir plus sur les polices système.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "type_header_weight": {"options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Gras"}, "label": "Poids de la police"}, "heading_scale": {"label": "Échelle de taille de police"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "Corps de texte"}, "type_body_font": {"label": "Police de caractères", "info": "Sélectionner une police différente peut affecter la vitesse de votre magasin. [En savoir plus sur les polices système.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "body_scale": {"label": "Échelle de taille de police"}, "header__3": {"content": "Menu de navigation"}, "navigation_font": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Corps"}, "label": "Police"}, "text_style": {"options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Style de texte"}, "navigation_scale": {"label": "Échelle de police du menu principal"}, "subnavigation_scale": {"label": "Échelle de police du sous-menu"}}}, "buttons": {"name": "Boutons"}, "variant_pills": {"name": "Pastilles de variantes", "paragraph": "Les pastilles de variantes sont l'un des moyens d'afficher les variantes de vos produits. [En savoir plus](https://help.shopify.com/fr/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Champs de saisie"}, "content_containers": {"name": "Conteneurs de contenu"}, "popups": {"name": "Listes déroulantes et pop-ups", "paragraph": "Affecte des zones comme les listes déroulantes de navigation, les modales pop-up et les pop-ups de panier."}, "media": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "drawers": {"name": "<PERSON><PERSON><PERSON>"}, "styles": {"name": "Icônes", "settings": {"accent_icons": {"options__3": {"label": "Bouton de contour"}, "options__4": {"label": "Texte"}, "label": "<PERSON><PERSON><PERSON>"}}}, "social-media": {"name": "Réseaux sociaux", "settings": {"header": {"content": "Co<PERSON><PERSON> sociaux"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}}}, "search_input": {"name": "Comportement de la recherche", "settings": {"header": {"content": "Suggestions de recherche"}, "predictive_search_enabled": {"label": "Activer les suggestions de recherche"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur du produit", "info": "Visible lorsque les suggestions de recherche sont activées."}, "predictive_search_show_price": {"label": "Aff<PERSON><PERSON> le prix du produit", "info": "Visible lorsque les suggestions de recherche sont activées."}}}, "currency_format": {"name": "Format de devise", "settings": {"content": "Codes de devise", "paragraph": "Les prix du panier et de la caisse affichent toujours les codes de devise. Exemple : 1,00 $ USD.", "currency_code_enabled": {"label": "Afficher les codes de devise"}}}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Type de panier", "drawer": {"label": "Tiroir"}, "page": {"label": "Page"}, "notification": {"label": "Notification popup"}}, "cart_icon": {"label": "Icône du panier", "bag": {"label": "Sac"}, "cart": {"label": "<PERSON><PERSON>"}}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le fournisseur"}, "show_cart_note": {"label": "<PERSON>r la note du panier"}, "cart_note_open": {"label": "O<PERSON><PERSON><PERSON>r la note du panier au chargement"}, "header_shipping": {"content": "Message de livraison gratuite"}, "enable_free_shipping_message": {"label": "Activer le message de livraison gratuite"}, "free_shipping_message": {"label": "Message", "info": "Utilisez le champ de remplacement *amount* pour afficher le montant calculé"}, "free_shipping_success": {"label": "Message de succès"}, "header_promo": {"content": "Message promotionnel"}, "enable_promo_message": {"label": "Activer le message promotionnel"}, "promo_message": {"label": "Message"}, "header_cross_sell": {"content": "Vente croisée"}, "enable_cross_sell": {"label": "<PERSON><PERSON> la vente croisée"}, "info": {"content": "Visitez la [documentation du thème](https://manathemes.com/docs/flux-theme/flux-theme-settings/cart) pour en savoir plus"}, "cross_sell_product": {"label": "Collection de vente croisée"}, "cross_sell_label": {"label": "<PERSON><PERSON><PERSON> de la vente croisée"}, "header_terms": {"content": "Conditions générales"}, "enable_terms": {"label": "Activer l'exigence des CGV"}, "terms_label": {"label": "Libellé"}, "header_empty_cart": {"content": "Panier vide"}, "cart_drawer": {"header": "T<PERSON>ir du panier", "collection": {"label": "Collection", "info": "Visible lorsque le tiroir du panier est vide."}}, "enable_empty_cart_message": {"label": "Activer le message de panier vide"}, "empty_cart_message": {"label": "Message de panier vide"}, "button_link": {"label": "<PERSON>n du bouton"}, "header_buttons": {"content": "Boutons"}, "disable_cart_button": {"label": "Désactiver le bouton 'Voir le panier'"}, "disable_checkout_button": {"label": "Désactiver le bouton 'Passer à la caisse'"}}}, "layout": {"name": "Disposition", "settings": {"page_width": {"label": "<PERSON><PERSON> de la page"}, "spacing_sections": {"label": "Espace entre les sections du modèle"}, "header__grid": {"content": "Grille"}, "paragraph__grid": {"content": "Affecte les zones avec plusieurs colonnes ou lignes."}, "spacing_grid_horizontal": {"label": "Espace horizontal"}, "spacing_grid_vertical": {"label": "Espace vertical"}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Disposition de bureau", "padding_top": "Marge supérieure", "padding_bottom": "Marge inférieure", "padding_left": "Marge intérieure gauche", "padding_right": "Marge intérieure droite"}, "section_margin_heading": "Disposition mobile", "spacing": "Espace", "margin_spacing": {"options__1": {"label": "-"}, "options__2": {"label": "+"}, "label": "Marge supérieure"}, "margin_top": "<PERSON><PERSON> <PERSON> marge", "header_color_box": {"content": "Couleurs"}, "colors_box": {"label": "Schéma de couleurs de la boîte de contenu"}, "colors": {"option_1": {"label": "Fond 1 - Texte 1"}, "option_2": {"label": "Fond 1 - Texte 2"}, "option_3": {"label": "Fond 2 - Texte 1"}, "option_4": {"label": "Fond 2 - Texte 2"}, "option_5": {"label": "Arrière-plan d'accentuation 1 - Texte d'accentuation 1"}, "option_6": {"label": "Arrière-plan d'accentuation 2 - Texte d'accentuation 2"}, "label": "Schéma de couleurs", "info": "Pour modifier le schéma de couleurs, mettez à jour vos [paramètres de thème](/editor?context=theme&category=colors).", "has_cards_info": "Pour modifier le schéma de couleurs, mettez à jour les paramètres de votre thème."}, "text_color": {"option_none": {"label": "Aucun (tel que défini dans le schéma de couleurs)"}, "option_1": {"label": "Couleur du texte 1"}, "option_2": {"label": "Couleur du texte 2"}, "option_3": {"label": "Couleur d'accentuation 1"}, "option_4": {"label": "Couleur d'accentuation 2"}, "label": "Couleur du texte", "info": "<PERSON><PERSON>, vous pouvez changer la couleur du texte indépendamment du schéma de couleurs."}, "heading_style": {"label": "Style de titre", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "heading_size": {"label": "<PERSON><PERSON> de l'en-tête", "options__1": {"label": "Très grande"}, "options__2": {"label": "Grande"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Petite"}}, "text_block_size": {"label": "<PERSON>lle du texte", "options__1": {"label": "Très grand"}, "options__2": {"label": "Grand"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON>"}}, "price_size": {"label": "Taille du prix", "options__1": {"label": "Très grand"}, "options__2": {"label": "Grand"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON>"}}, "heading_tag": {"label": "<PERSON><PERSON> d'<PERSON>t<PERSON>", "info": "Spécifiez les types de balises d'en-tête pour le référencement et les moteurs de recherche à des fins de crawl.", "options__1": {"label": "H1"}, "options__2": {"label": "H2"}, "options__3": {"label": "H3"}, "options__4": {"label": "H4"}, "options__5": {"label": "H5"}, "options__6": {"label": "H6"}}, "text_size": {"label": "<PERSON><PERSON> de la sous-titre", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}}, "text_style": {"label": "Style du texte de la sous-titre", "options__1": {"label": "Défaut"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "ignore_spacing": {"label": "Ignorer l'espace entre les sections du modèle"}, "disable_arrow_mobile": {"label": "Masquer les flèches du curseur"}, "animate_slider": {"label": "<PERSON><PERSON>r l'image"}, "gradient_position": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position de l'arrière-plan du dégradé"}, "countdown-text": {"label": "Texte"}, "countdown-date": {"label": "Date", "info": "Exemple de format : 30 sep. 2025"}, "countdown-time": {"label": "<PERSON><PERSON>", "info": "Exemple de format : 9:00"}, "countdown-date-time-style": {"label": "Style de compte à rebours", "options__1": {"label": "Option 1"}, "options__2": {"label": "Option 2"}}, "countdown-text-position": {"label": "Position du texte", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "G<PERSON><PERSON>"}}, "large-countdown": {"label": "Grand compte à rebours"}, "countdown_finished_message": {"label": "Message lorsque le compte à rebours est terminé", "info": "Si vide, le minuteur est caché lorsqu'il atteint 0"}, "countdown_timer_tag": {"label": "Afficher uniquement sur les produits avec le tag 'timer'"}}, "announcement-bar": {"name": "Barre d'info/social", "settings": {"header_layout": {"content": "Disposition"}, "announcement_position": {"label": "Disposition de la barre", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical Gauche"}, "options__3": {"label": "Vertical Droite"}}, "sticky": {"content": "<PERSON>e <PERSON>", "info": "Si vous choisissez l'option barre fixe, veuillez ajuster la marge supérieure dans la section d'en-tête en conséquence."}, "enable_announcement_bar_desktop_sticky": {"label": "Rendre fixe sur le bureau"}, "enable_announcement_bar_mobile_sticky": {"label": "Rendre fixe sur mobile"}, "header_vertical_bar": {"content": "Options de la barre verticale"}, "vertical_position": {"label": "Position"}, "header_top_bar": {"content": "Options de la barre supérieure"}, "text": {"label": "Texte"}, "link": {"label": "<PERSON><PERSON>"}, "text_animation": {"label": "Activer l'animation du texte"}, "show_countdown": {"label": "Aff<PERSON><PERSON> le compte à rebours", "info": "L'activation du compte à rebours remplacera le texte ajouté dans les blocs d'annonce"}, "countdown": {"content": "Minuteur du compte à rebours"}, "show_social_content": {"content": "Réseaux sociaux"}, "show_social_info": {"info": "Pour afficher vos comptes de réseaux sociaux, liez-les dans vos [paramètres de thème](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Afficher les icônes de réseaux sociaux"}, "country_selector_content": {"content": "Sélecteur de pays/région"}, "country_selector_info": {"info": "Pour ajouter un pays/région, accédez à vos [paramètres de marché.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activer le sélecteur de pays/région"}}, "presets": {"name": "Barre d'info/social"}}, "apps": {"name": "Applications", "settings": {"include_margins": {"label": "Rendre les marges de la section identiques à celles du thème"}}, "presets": {"name": "Applications"}}, "featured-collections": {"name": "Collections en vedette", "settings": {"featured_collection_1": {"label": "Moderne"}, "featured_collection_2": {"label": "Élégant"}, "collection_style": {"label": "Style", "options__1": {"label": "Élégant"}, "options__2": {"label": "Moderne"}, "options__3": {"label": "Sans image"}}, "layout": {"label": "Disposition sur ordinateur de bureau", "options__1": {"label": "Collection en premier"}, "options__2": {"label": "Texte en premier"}}, "desktop_content_position": {"label": "Position du contenu sur ordinateur de bureau", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Milieu"}, "options__3": {"label": "Bas"}}, "show_text_box": {"label": "Affiche<PERSON> la boîte de texte"}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "info": "Ajoutez des images en modifiant vos collections. [En savoir plus](https://help.shopify.com/manual/products/collections)"}, "desktop_content_alignment": {"label": "Alignement du contenu sur ordinateur de bureau", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_content_overlap": {"label": "Ajouter un chevauchement"}, "desktop_collections_alignment": {"label": "Alignement des collections sur ordinateur de bureau", "options__1": {"label": "Première vers le bas"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Seconde vers le bas"}}, "full_width": {"label": "Mettre en page pleine largeur"}, "header_mobile": {"content": "Disposition sur mobile"}, "layout_mobile": {"label": "Disposition sur mobile", "options__1": {"label": "Texte en premier"}, "options__2": {"label": "Collection en premier"}}}, "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}}}, "caption": {"name": "sous-titre", "settings": {"heading": {"label": "sous-titre"}, "text": {"label": "Texte"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte"}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Étiquette du bouton"}, "button_link": {"label": "URL du bouton"}}}}, "presets": {"name": "Collections en vedette"}}, "subcollections": {"name": "Sous-collections", "settings": {"info": {"content": "Ren<PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/how-to-set-up-subcollections-on-collection-pages) pour en savoir plus"}, "title": {"label": "Titre"}, "caption": {"label": "sous-titre"}, "enable_desktop_slider": {"label": "<PERSON><PERSON> le curseur sur ordinateur de bureau"}, "collections_to_show": {"label": "Nombre maximal de sous-collections à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur de bureau"}, "subcollection_list_style": {"label": "Style de la liste de collections", "options__1": {"label": "Élégant"}, "options__2": {"label": "Moderne"}, "options__3": {"label": "Sans image"}}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "info": "Ajoutez des images en modifiant vos collections. [En savoir plus](https://help.shopify.com/manual/products/collections)"}, "header_mobile": {"content": "Disposition sur mobile"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}}, "presets": {"name": "Sous-collections"}}, "collection-list": {"name": "Liste de collections", "settings": {"title": {"label": "Titre"}, "caption": {"label": "sous-titre"}, "enable_desktop_slider": {"label": "<PERSON><PERSON> le curseur sur ordinateur de bureau"}, "collections_to_show": {"label": "Nombre maximal de sous-collections à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur de bureau"}, "collection_list_style": {"label": "Style de liste de collection", "options__1": {"label": "Élégant"}, "options__2": {"label": "Moderne"}, "options__3": {"label": "Sans image"}}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "info": "Ajoutez des images en modifiant vos collections. [En savoir plus](https://help.shopify.com/manual/products/collections)"}, "header_mobile": {"content": "Disposition sur mobile"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Liste de collections"}}, "collection-tabs": {"name": "Onglets de collection", "settings": {"collection_style": {"label": "Style de collection", "options__1": {"label": "Moderne"}, "options__2": {"label": "Élégant"}}, "content_alignment": {"label": "Alignement du contenu", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centré"}}}, "blocks": {"collection": {"name": "Collection", "settings": {"collection": {"label": "Sélectionner une collection"}, "tab_heading": {"label": "Titre de l'onglet"}}}}, "presets": {"name": "Onglets de collection"}}, "two-images-text": {"name": "Images avec du texte", "settings": {"image": {"label": "Image un"}, "image_2": {"label": "Image deux"}, "layout": {"label": "Disposition sur ordinateur de bureau", "options__1": {"label": "Image en premier"}, "options__2": {"label": "Texte en premier"}}, "header_mobile": {"content": "Disposition sur mobile"}, "layout_mobile": {"label": "Disposition sur mobile", "options__1": {"label": "Image en premier"}, "options__2": {"label": "Texte en premier"}}}, "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}}}, "caption": {"name": "sous-titre", "settings": {"heading": {"label": "sous-titre"}, "text": {"label": "Texte"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte"}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "image_1": {"name": "Chevauchement d'image", "settings": {"image_1": {"label": "Image"}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Étiquette du bouton"}, "button_link": {"label": "URL du bouton"}}}}, "presets": {"name": "Images avec du texte"}}, "location-map": {"name": "Carte de localisation", "settings": {"info": {"content": "<PERSON><PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/locations-map) pour en savoir plus"}, "title": {"label": "Titre"}, "caption": {"label": "sous-titre"}, "content": {"label": "Texte", "info": "Contenu pour la zone de texte de la section."}, "header_contact": {"content": "Formulaire de contact"}, "show_contact_form": {"label": "Afficher le formulaire de contact"}, "hide_phone_field": {"label": "Masquer le champ du téléphone"}, "api_key": {"label": "Clé API Google Maps", "info": "L'activation des API Google Maps et de géocodage est nécessaire pour que la carte fonctionne."}, "zoom_level": {"label": "Niveau de zoom", "info": "Niveau de zoom pour la carte (1-18)."}, "header": {"content": "Emplacement"}, "address": {"label": "<PERSON><PERSON><PERSON>", "info": "Entrez l'adresse que vous souhaitez afficher sur la carte."}, "marker_content": {"label": "Contenu du marqueur", "info": "Contenu pour la fenêtre contextuelle du marqueur."}, "layout": {"label": "Disposition", "options__1": {"label": "<PERSON><PERSON>e largeur"}, "options__2": {"label": "En boîte"}, "options__3": {"label": "Contenu à gauche, carte à droite"}, "options__4": {"label": "Carte à gauche, contenu à droite"}}}, "presets": {"name": "Carte de localisation"}}, "countdown": {"name": "Promo avec minuterie", "settings": {"image": {"label": "Image un"}, "image_2": {"label": "Image deux"}, "layout": {"label": "Disposition de bureau", "options__1": {"label": "Image en premier"}, "options__2": {"label": "Texte en premier"}}, "desktop_content_position": {"label": "Position du contenu sur bureau", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Milieu"}, "options__3": {"label": "Bas"}}, "desktop_content_alignment": {"label": "Alignement du contenu sur bureau", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}, "header_mobile": {"content": "Disposition mobile"}, "layout_mobile": {"label": "Disposition mobile", "options__1": {"label": "Image en premier"}, "options__2": {"label": "Texte en premier"}}, "mobile_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "caption": {"name": "sous-titre", "settings": {"heading": {"label": "sous-titre"}, "text": {"label": "Texte"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte"}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "countdown-timer": {"name": "Minuterie de compte à rebours"}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Étiquette du bouton"}, "button_link": {"label": "URL du bouton"}}}}, "presets": {"name": "Promo avec Minuterie"}}, "image-gallery": {"name": "Galerie d'images", "settings": {"title": {"label": "Titre"}, "caption": {"label": "Sous-titre"}, "scroll_height_mobile": {"label": "<PERSON><PERSON> <PERSON>"}}, "blocks": {"text": {"name": "Image"}}, "presets": {"name": "Galerie d'images"}}, "contact-form": {"name": "Formulaire de contact", "settings": {"contact_style": {"label": "Disposition", "options__1": {"label": "Colonne"}, "options__2": {"label": "Ligne"}}, "image_height": {"label": "<PERSON><PERSON> de la bannière", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}, "image_height_mobile": {"label": "<PERSON><PERSON> de la bannière", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}, "caption": {"label": "Sous-titre"}, "heading": {"label": "Titre"}, "button_label_1": {"label": "Étiquette du bouton"}, "button_link_1": {"label": "<PERSON>n du bouton"}, "text": {"label": "Info de la barre latérale"}, "header_mobile": {"content": "Disposition mobile"}, "desktop_enable_gradient": {"label": "Activer l'arrière-plan de décoration sur bureau"}, "mobile_enable_gradient": {"label": "Activer l'arrière-plan de décoration sur mobile"}}, "presets": {"name": "Formulaire de contact"}}, "custom-liquid": {"name": "Code liquid personnalisé", "settings": {"custom_liquid": {"label": "Code liquid personnalisé", "info": "Ajoutez des extraits d'application ou d'autres codes liquid pour créer des personnalisations avancées."}}, "presets": {"name": "Code liquid personnalisé"}}, "featured-blog": {"name": "Articles de blog en vedette", "settings": {"caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Nombre d'articles de blog à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur bureau"}, "show_view_all": {"label": "Activer le bouton \"Voir tout\" si le blog contient plus d'articles que ce qui est affiché"}, "show_image": {"label": "Afficher l'image mise en avant"}, "show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}, "show_excerpt": {"label": "Afficher l'extrait"}, "blog_style": {"label": "Style du blog", "options__1": {"label": "Moderne"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Élégant"}, "options__4": {"label": "Couverture"}}, "header_mobile": {"content": "Disposition mobile"}, "swipe_on_mobile": {"label": "Activer le glissement sur mobile"}}, "presets": {"name": "Articles de blog en vedette"}}, "featured-collection": {"name": "Collection mise en avant", "settings": {"caption": {"label": "sous-titre"}, "title": {"label": "Titre"}, "description": {"label": "Description"}, "show_description": {"label": "Afficher la description de la collection depuis l'admin"}, "description_style": {"label": "Style de la description", "options__1": {"label": "Corps du texte"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "collection": {"label": "Collection"}, "collection_style": {"label": "Style de collection", "options__1": {"label": "Moderne"}, "options__3": {"label": "Élégant"}}, "products_to_show": {"label": "Nombre maximum de produits à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur bureau"}, "show_view_all": {"label": "Activer le bouton \"Voir tout\" si la collection contient plus de produits que ce qui est affiché"}, "view_all_style": {"label": "Style du bouton \"Voir tout\"", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Bouton"}}, "enable_desktop_slider": {"label": "<PERSON>r le carrousel sur bureau"}, "full_width": {"label": "Rendre les produits pleine largeur"}, "header": {"content": "Carte de produit"}, "image_ratio": {"label": "Ratio d'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image au survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application de notation de produit."}, "enable_quick_buy": {"label": "Activer la vue rapide", "info": "Optimal avec un type de panier en superposition ou en tiroir."}, "quick_add_position": {"label": "Position de l'ajout rapide", "options__1": {"label": "Superposition"}, "options__2": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}}, "header_overlap": {"content": "Superposition", "info": "Vous pouvez placer la collection en vedette au-dessus de la section précédente avec un effet de superposition. <PERSON>la masquera le titre et la description pour obtenir un aspect plus propre."}, "enable_overlap": {"label": "Activer la superposition"}, "margin_top": {"label": "Marge de superposition"}, "mobile_margin_top": {"label": "Marge de superposition sur mobile"}, "header_mobile": {"content": "Disposition mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "disable_quick_add": {"label": "Désactiver l'ajout rapide sur mobile lorsqu'il est activé sur bureau"}, "swipe_on_mobile": {"label": "Activer le glissement sur mobile"}, "header_banner_height": {"content": "<PERSON><PERSON> de la bannière"}, "banner_height_desktop": {"label": "Hauteur pour bureau"}, "banner_height_mobile": {"label": "Hauteur pour mobile"}}, "presets": {"name": "Collection mise en avant"}}, "featured-product": {"name": "Produit vedette", "blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style du texte", "options__1": {"label": "Défaut"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "vendor": {"name": "Fournisseur"}, "title": {"name": "Titre"}, "product-meta": {"name": "Inventaire et notation"}, "price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "countdown-timer": {"name": "Compte à rebours"}, "variant_picker": {"name": "Sélecteur de variantes", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Past<PERSON>s"}, "options__3": {"label": "Simple"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Afficher les boutons de paiement dynamique", "info": "En utilisant les méthodes de paiement disponibles sur votre boutique, les clients voient leur option préférée, comme PayPal. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "variant": {"content": "Sélecteur de variante"}, "swatch_shape": {"label": "Style Swatch", "info": "Consultez la documentation [En savoir plus](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches)", "options__1": {"label": "Cercle"}, "options__2": {"label": "Carré"}, "options__3": {"label": "Aucun"}}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "description": {"name": "Description"}, "share": {"name": "Partager", "settings": {"text": {"label": "Texte"}, "featured_image_info": {"content": "Si vous incluez un lien dans les publications sur les réseaux sociaux, l'image vedette de la page s'affichera comme image d'aperçu. [En savoir plus](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}}}, "custom_liquid": {"name": "Liquide <PERSON>", "settings": {"custom_liquid": {"label": "Liquide <PERSON>"}}}, "rating": {"name": "Évaluation du produit", "settings": {"paragraph": {"content": "Pour afficher une évaluation, ajoutez une application d'évaluation de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}}, "settings": {"product": {"label": "Produit"}, "product_background_color": {"label": "<PERSON><PERSON><PERSON> de fond"}, "secondary_background": {"label": "Afficher l'arrière-plan secondaire"}, "header": {"content": "<PERSON><PERSON><PERSON><PERSON>", "info": "En savoir plus sur les [types de médias](https://help.shopify.com/manual/products/product-media)"}, "media_position": {"label": "Position des médias sur bureau", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "hide_variants": {"label": "Masquer les médias des variantes non sélectionnées sur bureau"}, "enable_video_looping": {"label": "Activer la lecture en boucle de la vidéo"}, "desktop_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}, "header_mobile": {"content": "Mise en page mobile"}, "mobile_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}}, "presets": {"name": "Produit vedette"}}, "footer": {"name": "Pied de page", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON>-tête"}, "menu": {"label": "<PERSON><PERSON>", "info": "Affiche uniquement les éléments de menu de premier niveau."}}}, "brand_information": {"name": "Informations sur la marque", "settings": {"brand_headline": {"label": "Titre"}, "brand_description": {"label": "Description"}, "brand_image": {"label": "Image"}, "brand_image_width": {"label": "Largeur de l'image"}, "header__2": {"content": "Bulletin"}, "newsletter_enable": {"label": "Afficher l'inscription par e-mail"}, "header__1": {"content": "Icônes de réseaux sociaux"}, "show_social": {"label": "Afficher les icônes des réseaux sociaux", "info": "Pour afficher vos comptes de réseaux sociaux, liez-les dans vos [paramètres de thème](/editor?context=theme&category=social%20media)."}, "social_icons_size": {"options__1": {"label": "<PERSON><PERSON><PERSON> petit"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}, "label": "Taille des icônes sociales"}}}, "text": {"name": "Texte", "settings": {"heading": {"label": "<PERSON>-tête"}, "subtext": {"label": "Sous-texte"}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}, "image_width": {"label": "Largeur de l'image"}, "image_headline": {"label": "Titre"}, "image_description": {"label": "Description"}, "button_label": {"label": "Étiquette du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "button_style_secondary_1": {"label": "Utiliser le style de bouton contour"}, "show_text_under": {"label": "<PERSON><PERSON><PERSON><PERSON> le contenu dessous"}}}}, "settings": {"newsletter_enable": {"label": "Afficher l'inscription par e-mail"}, "newsletter_heading": {"label": "<PERSON>-tête"}, "header__1": {"content": "Inscription par e-mail", "info": "Les abonnés sont automatiquement ajoutés à votre liste de clients “acceptant le marketing”. [En savoir plus](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Icônes des réseaux sociaux", "info": "Pour afficher vos comptes de réseaux sociaux, liez-les dans vos [paramètres de thème](/editor?context=theme&category=social%20media)."}, "socials_heading": {"label": "<PERSON>-tête"}, "show_social": {"label": "Afficher les icônes des réseaux sociaux"}, "header__3": {"content": "Sélecteur de pays/région"}, "header__4": {"info": "Pour ajouter un pays/région, accédez à vos [paramètres de marché.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activer le sélecteur de pays/région"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON> de langue"}, "header__6": {"info": "Pour ajouter une langue, accédez à vos [paramètres de langue.](/admin/settings/languages)"}, "enable_language_selector": {"label": "<PERSON><PERSON> le sélecteur de langue"}, "header__10": {"content": "Propulsé par Shopify"}, "show_powered_by_link": {"label": "Afficher propulsé par Shopify"}, "header__7": {"content": "Moyens de paiement"}, "payment_enable": {"label": "Afficher les icônes de paiement"}, "header__8": {"content": "Liens de politique", "info": "Pour ajouter des politiques de boutique, accédez à vos [paramètres de politiques](/admin/settings/legal)."}, "show_policy": {"label": "Afficher les liens de politique"}, "margin_top": {"label": "Marge supérieure de bureau/mobile"}, "margin_bottom": {"label": "Marge inférieure mobile"}, "centered_content": {"label": "Centrer le contenu sur mobile"}, "header__9": {"content": "Suivre sur la boutique", "info": "Afficher le bouton de suivi pour votre boutique sur l'application de la boutique. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Activer le suivi sur la boutique"}, "header_back_to_top_desktop": {"content": "Bouton de retour en haut sur bureau"}, "back_to_top_desktop": {"label": "<PERSON><PERSON> le bouton de retour en haut"}, "back_to_top_bottom": {"label": "Position verticale"}, "back_to_top_right": {"label": "Position horizontale"}, "header_back_to_top_mobile": {"content": "Bouton de retour en haut sur mobile"}, "back_to_top_mobile": {"label": "<PERSON><PERSON> le bouton de retour en haut"}, "footer_border": {"label": "Activer la bordure supérieure du pied de page"}, "make_columns_even": {"label": "Uniformiser les colonnes"}}}, "header": {"name": "<PERSON>-tête", "blocks": {"mega_promotion": {"name": "Méga promotion", "settings": {"info": {"content": "Ren<PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/mega-menu-promotions) pour en savoir plus"}, "mega_promotion_item": {"label": "Élément du menu", "info": "Entrez le nom de l'élément du méga menu auquel vous souhaitez ajouter une carte promotionnelle."}, "mega_promotion_image": {"label": "Image"}, "mega_promotion_caption": {"label": "sous-titre"}, "mega_promotion_title": {"label": "Titre"}, "mega_promotion_link_label": {"label": "Étiquette du lien"}, "mega_promotion_link": {"label": "URL du lien"}}}, "mega_image_menu": {"name": "Menu méga image", "settings": {"heading_1": {"content": "Élément 1"}, "heading_2": {"content": "Élément 2"}, "heading_3": {"content": "Élément 3"}, "heading_4": {"content": "Élément 4"}, "heading_5": {"content": "Élément 5"}, "heading_6": {"content": "Élément 6"}, "heading_7": {"content": "Élément 7"}, "heading_8": {"content": "Élément 8"}}}}, "settings": {"header_layout": {"content": "Disposition de l'en-tête"}, "header_additional_links": {"content": "Liens supplémentaires"}, "button_label": {"label": "Étiquette de lien 1"}, "button_link": {"label": "Lien 1"}, "button_label_one": {"label": "Étiquette de lien 2"}, "button_link_one": {"label": "Lien 2"}, "disable_additional_links": {"label": "Activer les liens supplémentaires"}, "logo_help": {"content": "Modifiez votre logo dans les [paramètres du thème](/editor?context=theme&category=logo)."}, "desktop_header_layout": {"label": "Disposition de l'en-tête de bureau", "options__1": {"label": "<PERSON>ux Lignes"}, "options__2": {"label": "Logo-Menü-Icones"}, "options__3": {"label": "Menü-Logo-Icones"}, "options__4": {"label": "Logo-Centre-Menü-Icones"}}, "show_search_icon": {"label": "Style alternatif", "info": "Ce paramètre s'applique uniquement lorsque 'Deux lignes' est sélectionné. Il permet de choisir un style alternatif pour cette mise en page."}, "menu": {"label": "<PERSON><PERSON>"}, "featured_collection_1": {"label": "Collection 1"}, "featured_collection_2": {"label": "Collection 2"}, "menu_type_desktop": {"label": "Type de menu sur bureau", "info": "Le type de menu est automatiquement optimisé pour les mobiles.", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Méga menu"}, "options__3": {"label": "Tiroir"}}, "all_items_mega": {"label": "Appliquer le méga menu à tous les éléments du menu", "info": "Si le méga menu est sélectionné. <PERSON><PERSON> <PERSON><PERSON>, seuls les éléments de menu avec 3 niveaux sont transformés en méga menu"}, "submenu_animation_position": {"label": "Animation du méga menu", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "De bas en haut"}, "options__3": {"label": "De gauche à droite"}}, "sticky_header_type": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> collan<PERSON>", "options__1": {"label": "Aucun"}, "options__2": {"label": "Au défilement vers le haut"}, "options__3": {"label": "Toujours"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>, ré<PERSON>ire la taille du logo"}}, "country_selector_content": {"content": "Sélecteur de pays/région"}, "country_selector_info": {"info": "Pour ajouter un pays/région, accédez à vos [paramètres de marché.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activer le sélecteur de pays/région"}, "enable_fixed_header_type": {"label": "En-tête alternatif sur la page d'accueil"}, "enable_fixed_header_type_collection": {"label": "En-tête alternatif sur la page de collection"}, "enable_fixed_header_type_all": {"label": "En-tête alternatif sur toutes les pages"}, "enable_fixed_header_transparent": {"label": "Rendre l'en-tête alternatif transparent"}, "enable_header_full_width": {"label": "<PERSON><PERSON> l'en-tête en pleine largeur"}, "fixed_header_type_margin": {"label": "Marge supérieure de bureau"}, "fixed_header_type_margin_mobile": {"label": "Marge supérieure mobile"}, "header_transparent": {"content": "En-tête alternatif"}, "transparent_menu": {"label": "Pages personnal<PERSON>", "info": "Sélectionnez un menu lié aux pages sur lesquelles vous souhaitez appliquer l’en-tête alternatif."}, "header_sticky": {"content": "En-tête fixe"}, "header_highlight": {"content": "Surligner l'élément de menu"}, "enable_item_highlight": {"label": "Activer la mise en évidence de l'élément de menu"}, "item_highlight_text": {"label": "Texte de mise en évidence de l'élément de menu"}, "item_highlight_position": {"label": "Position de mise en évidence de l'élément de menu", "options__1": {"label": "Premier élément de menu"}, "options__2": {"label": "Deuxième élément de menu"}, "options__3": {"label": "Troisième élément de menu"}, "options__4": {"label": "Quatrième élément de menu"}, "options__5": {"label": "Cinquième élément de menu"}, "options__6": {"label": "Sixième élément de menu"}, "options__7": {"label": "Septième élément de menu"}, "options__8": {"label": "Huitième élément de menu"}, "options__9": {"label": "Neuvième élément de menu"}, "options__10": {"label": "Dixième élément de menu"}}, "adjust_item_highlight_position": {"label": "Ajuster la position de surbrillance de l'élément"}, "item_highlight_background_color": {"label": "Couleur de l'arrière-plan de mise en évidence"}, "item_highlight_color": {"label": "Couleur du texte en évidence"}, "margin_bottom": {"label": "Marge inférieure"}, "header_icons_deco": {"content": "Décoration des icônes d'en-tête"}, "header_icons_decoration": {"label": "Décoration des icônes", "options__1": {"label": "Aucune"}, "options__2": {"label": "Cercle"}, "options__3": {"label": "Ligne"}}, "header_mobile": {"content": "Disposition sur mobile"}, "mobile_desktop_header_layout": {"label": "Disposition de l'en-tête sur mobile", "options__1": {"label": "Centré"}, "options__2": {"label": "G<PERSON><PERSON>"}}}}, "image-banner": {"name": "Bannière d'image", "settings": {"image": {"label": "Image"}, "image_overlay_opacity": {"label": "Opacité de la superposition d'image", "info": "Vous pouvez changer la couleur d'opacité globalement depuis les [paramètres du thème](/editor?context=theme&category=colors)."}, "image_height": {"label": "<PERSON><PERSON> de la bannière", "options__1": {"label": "S'adapter à l'image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Haut gauche"}, "options__2": {"label": "Haut centre"}, "options__3": {"label": "<PERSON>ut droit"}, "options__4": {"label": "Milieu gauche"}, "options__5": {"label": "Milieu centre"}, "options__6": {"label": "Milieu droit"}, "options__7": {"label": "Bas gauche"}, "options__8": {"label": "Bas centre"}, "options__9": {"label": "Bas droit"}, "label": "Position du contenu sur le bureau"}, "show_text_box": {"label": "Afficher la boîte de texte sur le bureau"}, "box_padding_top": {"label": "Marge supérieure de la boîte de texte"}, "box_padding_bottom": {"label": "Marge inférieure de la boîte de texte"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur le bureau"}, "show_image_circle": {"label": "Masquer le cercle d'image"}, "ignore_image_circle_animation": {"label": "Désactiver l'animation du cercle d'image"}, "color_scheme": {"info": "Visible lorsque le conteneur est affiché."}, "header": {"content": "Disposition sur mobile"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}, "stack_images_on_mobile": {"label": "Empiler les images sur mobile"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON><PERSON> le conteneur sur mobile"}, "adapt_height_first_image": {"label": "Adapter la hauteur de la section à la taille de la première image", "info": "Remplace le paramètre de hauteur de la bannière d'image lorsque coché."}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "heading": {"name": "Titre", "settings": {"heading": {"label": "Titre", "info": "Met<PERSON>z un mot important en italique dans votre titre pour le mettre en valeur, puis choisissez une option de surlignage ci-dessous."}, "word_animation_color": {"label": "<PERSON>uleur de mise en évidence"}, "highlight_option": {"options__1": {"label": "Italique"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Cercle Un"}, "options__5": {"label": "Cercle Deux"}, "label": "Option de mise en évidence"}}}, "caption": {"name": "sous-titre", "settings": {"heading": {"label": "sous-titre"}, "text": {"label": "Texte"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Style de texte"}}}, "countdown-timer": {"name": "Compte à rebours", "settings": {"countdown_small": {"label": "Rendre le compte à rebours plus petit"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Étiquette du premier bouton", "info": "Laissez l'étiquette vide pour masquer le bouton."}, "button_link_1": {"label": "Lien du premier bouton"}, "button_style_secondary_1": {"label": "Utiliser le style de bouton de contour"}, "button_label_2": {"label": "Étiquette du deuxième bouton", "info": "Laissez l'étiquette vide pour masquer le bouton."}, "button_link_2": {"label": "Lien du deuxième bouton"}, "button_style_secondary_2": {"label": "Utiliser le style de bouton de contour"}}}}, "presets": {"name": "Bannière d'image"}}, "image-banner-with-featured-collection": {"name": "Bannière d'image avec collection en vedette", "settings": {"image": {"label": "Image"}, "hide_image": {"label": "Utiliser un fond uni"}, "image_overlay_opacity": {"label": "Opacité du recouvrement de l'image", "info": "Vous pouvez changer la couleur d'opacité globalement depuis les [paramètres du thème](/editor?context=theme&category=colors)."}, "content_text_color": {"label": "Couleur du texte du contenu"}, "content_button_background_color": {"label": "Couleur de fond du contenu"}, "content_button_text_color": {"label": "Couleur du texte du contenu"}, "image_height": {"label": "<PERSON><PERSON> de la bannière"}, "image_height_mobile": {"label": "<PERSON><PERSON> de la bannière"}, "full_width_banner": {"label": "Rendre la mise en page en pleine largeur"}, "desktop_content_position": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position du contenu sur le bureau"}, "show_text_box": {"label": "Afficher la zone de texte sur le bureau"}, "box_padding_top": {"label": "Remplissage supérieur de la zone de texte"}, "box_padding_bottom": {"label": "Remplissage inférieur de la zone de texte"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur le bureau"}, "header_mobile_image_banner": {"content": "Mise en page mobile - Bannière d'image"}, "image_mobile": {"label": "Image"}, "image_overlay_opacity_mobile": {"label": "Opacité de superposition d'image", "info": "Vous pouvez modifier l'opacité de la couleur globalement depuis les [paramètres du thème](/editor?context=theme&category=colors)."}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu mobile"}, "stack_images_on_mobile": {"label": "Empiler les images sur mobile"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON><PERSON> le conteneur sur mobile"}, "adapt_height_first_image": {"label": "Adapter la hauteur de la section à la taille de la première image", "info": "Remplace le réglage de hauteur de la bannière d'image lorsqu'il est coché."}, "header_featured_collection": {"content": "Collection en vedette"}, "enable_collection": {"label": "Activer la collection mise en avant"}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Nombre maximal de produits à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur le bureau"}, "show_view_all": {"label": "Activer \"Tout voir\" si la collection comporte plus de produits que ceux affichés"}, "view_all_style": {"label": "Style de \"Tout voir\"", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Bouton"}}, "enable_desktop_slider": {"label": "<PERSON><PERSON> le carrousel sur le bureau"}, "full_width": {"label": "Rendre les produits en pleine largeur"}, "header": {"content": "Carte de produit"}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image au survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur"}, "show_rating": {"label": "Afficher la notation du produit", "info": "Pour afficher une notation, ajoutez une application de notation de produit."}, "enable_quick_buy": {"label": "Activer la vue rapide", "info": "Optimal avec un type de panier popup ou tiroir."}, "quick_add_position": {"label": "Position d'ajout rapide", "options__1": {"label": "Superposition"}, "options__2": {"label": "Défaut"}}, "header_overlap": {"content": "Chevauchement"}, "desktop_margin_top": {"label": "Chevauchement sur le bureau"}, "mobile_margin_top": {"label": "Chevauchement sur mobile"}, "header_mobile_featured_collection": {"content": "Mise en page mobile - Collection en vedette"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "disable_quick_add": {"label": "Désactiver l'ajout rapide sur mobile lorsque activé sur le bureau"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}}, "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}}}, "caption": {"name": "Sous-titre", "settings": {"heading": {"label": "Sous-titre"}, "text": {"label": "Texte"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Style du texte"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Étiquette du premier bouton", "info": "Laissez l'étiquette vide pour masquer le bouton."}, "button_link_1": {"label": "Lien du premier bouton"}}}}, "presets": {"name": "Bannière d'image avec collection en vedette"}}, "image-banner-with-collections": {"name": "Bannière d’image avec liste de collections", "settings": {"image": {"label": "Image"}, "hide_image": {"label": "Utiliser un fond de couleur unie"}, "image_overlay_opacity": {"label": "Opacité de la superposition d’image", "info": "Vous pouvez modifier la couleur de l’opacité globalement dans les [paramètres du thème](/editor?context=theme&category=colors)."}, "content_text_color": {"label": "Couleur du texte du contenu"}, "content_button_background_color": {"label": "Couleur de fond du contenu"}, "content_button_text_color": {"label": "Couleur du texte du contenu"}, "image_height": {"label": "<PERSON><PERSON> de la bannière"}, "image_height_mobile": {"label": "<PERSON><PERSON> de la bannière"}, "desktop_content_position": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position du contenu sur ordinateur"}, "show_text_box": {"label": "Afficher la boîte de texte sur ordinateur"}, "box_padding_top": {"label": "Marge supérieure de la boîte de texte"}, "box_padding_bottom": {"label": "Marge inférieure de la boîte de texte"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur ordinateur"}, "header_mobile_image_banner": {"content": "Mise en page mobile"}, "image_mobile": {"label": "Image"}, "image_overlay_opacity_mobile": {"label": "Opacité de la superposition d’image", "info": "Vous pouvez modifier la couleur de l’opacité globalement dans les [paramètres du thème](/editor?context=theme&category=colors)."}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}, "stack_images_on_mobile": {"label": "Empiler les images sur mobile"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON><PERSON> le conteneur sur mobile"}, "adapt_height_first_image": {"label": "Adapter la hauteur de la section à la taille de la première image", "info": "Remplace le paramètre de hauteur de la bannière lorsqu’il est activé."}, "header_featured_collection": {"content": "Collection mise en avant"}, "enable_collection": {"label": "Activer la collection mise en avant"}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Nombre maximal de produits à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "show_view_all": {"label": "Activer \"Tout voir\" si la collection contient plus de produits que ceux affichés"}, "view_all_style": {"label": "Style de \"Tout voir\"", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Bouton"}}, "enable_desktop_slider": {"label": "<PERSON><PERSON> le carrousel sur ordinateur"}, "full_width": {"label": "Afficher les produits en pleine largeur"}, "header": {"content": "Carte produit"}, "image_ratio": {"label": "Proportion de l’image", "options__1": {"label": "Adapter à l’image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image au survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application d’évaluation des produits."}, "enable_quick_buy": {"label": "Activer la vue rapide", "info": "Optimal avec un panier en popup ou en tiroir."}, "quick_add_position": {"label": "Position de la vue rapide", "options__1": {"label": "Superposition"}, "options__2": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}}, "header_overlap": {"content": "Chevauchement"}, "desktop_margin_top": {"label": "Chevauchement sur ordinateur"}, "mobile_margin_top": {"label": "Chevauchement sur mobile"}, "header_mobile_featured_collection": {"content": "Mise en page mobile - Collection mise en avant"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "disable_quick_add": {"label": "Désactiver l’ajout rapide sur mobile lorsqu’il est activé sur ordinateur"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "heading": {"label": "Titre", "info": "Met<PERSON>z un mot important en italique dans votre titre pour le faire ressortir, puis choisissez une option de mise en valeur ci-dessous."}, "caption": {"label": "Légende"}, "text": {"label": "Description"}, "text_style": {"options__1": {"label": "Corps de texte"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Style du texte"}, "button_label_1": {"label": "Libellé du premier bouton", "info": "Laissez le libellé vide pour masquer le bouton."}, "button_link_1": {"label": "Lien du premier bouton"}, "highlight_option": {"options__1": {"label": "Italique"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Cercle Un"}, "options__5": {"label": "Cercle Deux"}, "label": "Option de mise en valeur"}, "word_animation_color": {"label": "<PERSON><PERSON>ur de mise en valeur"}, "header_collections": {"content": "Collections"}, "disable_arrow_mobile": {"label": "Masquer les flèches du carrousel sur mobile"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Bannière d’image avec liste de collections"}}, "banner-two-columns": {"name": "Bannière à colonnes", "settings": {"banner_layout": {"label": "Disposition", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Supplémentaire"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "full_width": {"label": "Rendre la mise en page en pleine largeur"}, "accessibility": {"content": "Accessibilité", "label": "Description du diaporama", "info": "Décrivez le diaporama pour les clients utilisant des lecteurs d'écran."}}, "blocks": {"slide": {"name": "Bannière", "settings": {"image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "subheading": {"label": "Texte"}, "link_label": {"label": "Étiquette du lien"}, "link": {"label": "<PERSON><PERSON>"}, "box_align": {"label": "Position du contenu sur le bureau", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "Haut gauche"}, "options__2": {"label": "Haut centre"}, "options__3": {"label": "<PERSON>ut droit"}, "options__4": {"label": "Milieu gauche"}, "options__5": {"label": "Milieu centre"}, "options__6": {"label": "Milieu droit"}, "options__7": {"label": "Bas gauche"}, "options__8": {"label": "Bas centre"}, "options__9": {"label": "Bas droit"}}, "show_text_box": {"label": "Afficher la boîte de texte sur le bureau"}, "text_alignment": {"label": "Alignement du contenu sur le bureau", "option_1": {"label": "G<PERSON><PERSON>"}, "option_2": {"label": "Centre"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacité de la superposition d'image"}, "color_scheme": {"info": "Visible lorsque le conteneur est affiché."}, "text_alignment_mobile": {"label": "Alignement du contenu sur mobile", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Bannière à colonnes"}}, "sticky-banners": {"name": "Funky bannière", "settings": {"banner_height": {"label": "<PERSON>ur de l'image", "options__1": {"label": "S'adapter à la première image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "full_width": {"label": "Rendre la mise en page en pleine largeur"}, "accessibility": {"content": "Accessibilité", "label": "Description du diaporama", "info": "Décrivez le diaporama pour les clients utilisant des lecteurs d'écran."}}, "blocks": {"slide": {"name": "Bannière", "settings": {"image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "subheading": {"label": "Texte"}, "link_label": {"label": "Étiquette du lien"}, "link": {"label": "<PERSON><PERSON>"}, "box_align": {"label": "Position du contenu sur le bureau", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "Haut gauche"}, "options__2": {"label": "Haut centre"}, "options__3": {"label": "<PERSON>ut droit"}, "options__4": {"label": "Milieu gauche"}, "options__5": {"label": "Milieu centre"}, "options__6": {"label": "Milieu droit"}, "options__7": {"label": "Bas gauche"}, "options__8": {"label": "Bas centre"}, "options__9": {"label": "Bas droit"}}, "show_text_box": {"label": "Afficher la boîte de texte sur le bureau"}, "text_alignment": {"label": "Alignement du contenu sur le bureau", "option_1": {"label": "G<PERSON><PERSON>"}, "option_2": {"label": "Centre"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacité de la superposition d'image"}, "color_scheme": {"info": "Visible lorsque le conteneur est affiché."}, "text_alignment_mobile": {"label": "Alignement du contenu sur mobile", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Sticky bannière"}}, "image-with-text": {"name": "Image avec texte", "settings": {"image": {"label": "Image"}, "caption": {"label": "Légende verticale"}, "height": {"options__1": {"label": "S'adapter à l'image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}, "label": "<PERSON>ur de l'image"}, "desktop_image_width": {"options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}, "label": "Largeur de l'image sur bureau", "info": "L'image est automatiquement optimisée pour les mobiles."}, "layout": {"options__1": {"label": "Image d'abord"}, "options__2": {"label": "Image ensuite"}, "label": "Placement de l'image sur le bureau", "info": "L'image d'abord est la disposition mobile par défaut."}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur le bureau"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Milieu"}, "options__3": {"label": "Bas"}, "label": "Position du contenu sur le bureau"}, "content_layout": {"options__1": {"label": "Aucun chevauchement"}, "options__2": {"label": "Chevauchement"}, "label": "Disposition du contenu"}, "desktop_enable_gradient": {"label": "Activer l'arrière-plan décoratif"}, "header_mobile": {"content": "Disposition mobile"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}, "mobile_enable_gradient": {"label": "Activer l'arrière-plan décoratif"}, "full_width": {"label": "Rendre la mise en page en boîte"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "caption": {"name": "sous-titre", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style du texte", "options__1": {"label": "Sous-titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON>lle du texte", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Contenu"}, "text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image", "info": "Pour une apparence optimale, veuillez vous assurer que les dimensions de l'image sont de 300x300 pixels."}, "mobile_disable_image": {"label": "Désactiver l'image sur mobile"}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Étiquette du bouton", "info": "Laissez le champ vide pour masquer le bouton."}, "button_link": {"label": "<PERSON>n du bouton"}}}}, "presets": {"name": "Image avec texte"}}, "image-hotspots": {"name": "Points chauds de l'image", "settings": {"image": {"label": "Image"}, "image_size": {"options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "label": "<PERSON>lle de l'image"}, "heading": {"label": "Titre"}, "caption": {"label": "Sous-titre"}, "text_style": {"label": "Style de texte du sous-titre", "options__1": {"label": "Défaut"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "text_size": {"label": "Taille de texte du sous-titre", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}, "product": {"label": "Produit"}, "layout": {"options__1": {"label": "Image d'abord"}, "options__2": {"label": "Texte d'abord"}, "label": "Placement de l'image"}, "layout_mobile": {"options__1": {"label": "Image d'abord"}, "options__2": {"label": "Texte d'abord"}, "label": "Placement de l'image"}, "header_colors": {"content": "Couleurs"}, "tooltip_background_color": {"label": "Couleur de fond de l'infobulle"}, "full_width": {"label": "Rendre la mise en page en pleine largeur"}, "hide_content": {"label": "Masquer le contenu"}}, "blocks": {"tooltip": {"name": "Infobulle", "settings": {"title": {"label": "Titre"}, "link": {"label": "Lien du produit"}, "product": {"label": "Produit"}, "content": {"label": "Contenu"}, "top": {"label": "Position supérieure"}, "left": {"label": "Position gauche"}}}}, "presets": {"name": "Points chauds de l'image"}}, "quick-info-bar": {"name": "Barre d'informations rapides", "settings": {"first_column_content": {"content": "Contenu de la première colonne"}, "image_1": {"label": "Image"}, "heading_1": {"label": "Titre"}, "caption_1": {"label": "sous-titre"}, "second_column_content": {"content": "Contenu de la deuxième colonne"}, "image_2": {"label": "Image"}, "heading_2": {"label": "Titre"}, "caption_2": {"label": "sous-titre"}, "third_column_content": {"content": "Contenu de la troisième colonne"}, "image_3": {"label": "Image"}, "heading_3": {"label": "Titre"}, "caption_3": {"label": "sous-titre"}, "fourth_column_content": {"content": "Contenu de la quatrième colonne"}, "image_4": {"label": "Image"}, "heading_4": {"label": "Titre"}, "caption_4": {"label": "sous-titre"}, "info_bar_options": {"content": "Options de la barre d'informations rapides"}, "image_size": {"options_1": {"label": "<PERSON>"}, "options_2": {"label": "<PERSON><PERSON><PERSON>"}, "options_3": {"label": "Grand"}, "label": "<PERSON>lle de l'image"}, "desktop_bar_position": {"options_1": {"label": "<PERSON><PERSON><PERSON>"}, "options_2": {"label": "G<PERSON><PERSON>"}, "options_3": {"label": "Centre"}, "options_4": {"label": "Centre sans chevauchement"}, "label": "Position de la barre sur le bureau"}, "bar_full_width": {"label": "Affiche<PERSON> la barre en pleine largeur"}, "add_border": {"label": "Ajouter une bordure à la section"}, "full_width_background": {"label": "Rendre l'arrière-plan de la section en pleine largeur"}}, "presets": {"name": "Barre d'informations rapides"}}, "multirow": {"name": "Multirangée", "settings": {"image": {"label": "Image"}, "image_height": {"options__1": {"label": "S'adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}, "label": "<PERSON>ur de l'image"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "label": "Largeur de l'image sur bureau", "info": "L'image est automatiquement optimisée pour mobile."}, "button_style": {"options__1": {"label": "Bouton plein"}, "options__2": {"label": "Bouton contour"}, "label": "Style de bouton"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur bureau"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Milieu"}, "options__3": {"label": "Bas"}, "label": "Position du contenu sur bureau", "info": "La position est automatiquement optimisée pour mobile."}, "image_layout": {"options__1": {"label": "Alterné à partir de la gauche"}, "options__2": {"label": "Alterné à partir de la droite"}, "options__3": {"label": "Aligné à gauche"}, "options__4": {"label": "Ali<PERSON><PERSON> droite"}, "label": "Placement de l'image sur bureau", "info": "Le placement est automatiquement optimisé pour mobile."}, "ignore_row_spacing": {"label": "Ignorer l'espace entre les rangées"}, "full_width": {"label": "Rendre la mise en page pleine largeur"}, "content_gradient_position": {"options__1": {"label": "Haut gauche"}, "options__2": {"label": "Haut droite"}, "options__3": {"label": "Bas gauche"}, "options__4": {"label": "Bas droite"}, "label": "Position de la décoration du contenu"}, "ignore_content_gradient": {"label": "Ignorer la décoration du contenu"}, "ignore_content_overlap": {"label": "Ignorer le chevauchement du contenu"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}, "header_mobile": {"content": "Mise en page mobile"}}, "blocks": {"row": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "heading_size": {"options__1": {"label": "Très grand"}, "options__2": {"label": "Grand"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON> du titre"}, "text": {"label": "Texte"}, "button_label": {"label": "Libellé du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}}}}, "presets": {"name": "Multirangée"}}, "main-account": {"name": "<PERSON><PERSON><PERSON>"}, "main-activate-account": {"name": "Activation du compte"}, "main-addresses": {"name": "Adresses"}, "main-article": {"name": "Article de blog", "blocks": {"featured_image": {"name": "Image à la une", "settings": {"image_height": {"label": "Hauteur de l'image à la une", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}, "info": "Pour de meilleurs résultats, utilisez une image au format 16:9. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Titre", "settings": {"blog_show_date": {"label": "Affiche<PERSON> la date"}, "blog_show_author": {"label": "Afficher l'auteur"}}}, "content": {"name": "Contenu"}, "tags": {"name": "Tags"}, "buttons": {"name": "Boutons Article précédent/suivant"}, "share": {"name": "Partager", "settings": {"text": {"label": "Texte"}, "featured_image_info": {"content": "Si vous incluez un lien dans les publications sur les réseaux sociaux, l'image à la une de la page sera affichée comme image de prévisualisation. [En savoir plus](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un titre et une description du magasin sont inclus avec l'image de prévisualisation. [En savoir plus](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}}}, "main-blog": {"name": "Articles de blog", "settings": {"header": {"content": "Carte d'article de blog"}, "make_first_post_featured": {"label": "Mettez en avant le premier article de blog en haut"}, "show_image": {"label": "Afficher l'image à la une"}, "tags": {"label": "Afficher les étiquettes"}, "tag_label": {"label": "Filtrer par étiquette"}, "tag_default": {"label": "Tous les articles"}, "show_page_title": {"label": "<PERSON><PERSON><PERSON><PERSON> le titre de la page"}, "show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}, "paragraph": {"content": "Modifiez les extraits en éditant vos articles de blog. [En savoir plus](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "layout": {"label": "Disposition pour ordinateur de bureau", "options__1": {"label": "1 Colonne"}, "options__2": {"label": "2 Colonnes"}, "options__3": {"label": "3 Colonnes"}, "info": "Les articles sont empilés sur mobile."}, "blog_style": {"label": "Style de blog", "options__1": {"label": "Moderne"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Élégant"}}, "image_height": {"label": "Hauteur de l'image à la une", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}, "info": "Pour de meilleurs résultats, utilisez une image au format 3:2. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Sous-total", "blocks": {"subtotal": {"name": "Prix sous-total"}, "buttons": {"name": "Bouton de paiement"}, "text-with-image": {"name": "Texte avec image", "settings": {"image": {"label": "Image"}, "image_width": {"label": "Largeur de l’image"}, "hide_image": {"label": "Masquer l’image"}, "text": {"label": "Texte"}, "text_style": {"label": "Texte", "options__1": {"label": "Corps"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "centered_content": {"label": "<PERSON><PERSON> le contenu"}}}}}, "main-cart-items": {"name": "Articles", "settings": {"desktop_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}, "header_mobile": {"content": "Mise en page mobile"}, "mobile_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}}}, "main-404": {"name": "Page 404", "settings": {"heading": {"label": "Titre"}, "text": {"label": "Texte"}, "image": {"label": "Image"}}}, "main-collection-banner": {"name": "Bannière de la collection principale", "settings": {"paragraph": {"content": "Ajoutez une description ou une image en modifiant votre collection. [En savoir plus](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Afficher la description de la collection"}, "show_breadcrumbs": {"label": "Afficher les fil d'Ariane"}, "collection_style": {"label": "Style", "options__1": {"label": "Deux colonnes"}, "options__2": {"label": "Fond coloré"}, "options__3": {"label": "Couverture"}}, "desktop_content_alignment": {"label": "Alignement", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "header_style_three": {"content": "Opacité du style de couverture"}, "image_height": {"label": "<PERSON><PERSON> de la bannière", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}, "show_text_box": {"label": "Affiche<PERSON> la boîte de texte"}, "text_box_color": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON>"}, "label": "Schéma de couleur de la boîte de texte"}, "header_style_one_two": {"content": "Deux colonnes/Fond coloré"}, "banner_background_color": {"label": "<PERSON><PERSON><PERSON> de fond"}, "padding_heading": {"content": "Rembourrage"}, "all_products_collection_header": {"content": "Collection de tous les produits", "info": "<PERSON><PERSON>, vous pouvez définir l'image et le titre pour la collection par défaut de tous les produits"}, "image": {"label": "Image"}, "fallback_heading": {"label": "Titre"}}, "presets": {"name": "Bannière de la collection principale"}}, "main-collection-product-grid": {"name": "Grille de produits", "settings": {"header__layout": {"content": "Disposition"}, "collection_layout": {"label": "Disposition de la grille de produits", "options__1": {"label": "Moderne"}, "options__2": {"label": "Élégant"}}, "product_card_style": {"label": "Style de la carte produit", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__3": {"label": "Élégant"}}, "add_border": {"label": "Ajouter une bordure à l'image du produit"}, "products_per_page": {"label": "Produits par page"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur de bureau"}, "pagination": {"label": "Style de pagination", "options__1": {"label": "Standard"}, "options__2": {"label": "Bouton charger plus"}}, "load_button": {"label": "Bouton de chargement"}, "enable_filtering": {"label": "<PERSON>r le filtrage", "info": "Personnalisez les filtres avec l'application de recherche et de découverte. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_switcher": {"label": "<PERSON><PERSON> le commutateur"}, "filter_type": {"label": "Disposition du filtre sur ordinateur de bureau", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Tiroir"}, "info": "Le tiroir est la mise en page par défaut sur mobile."}, "open_filter": {"label": "Style de filtre vertical", "options__1": {"label": "<PERSON><PERSON><PERSON> ouvert"}, "options__2": {"label": "<PERSON><PERSON> ouvert"}}, "enable_sorting": {"label": "<PERSON><PERSON> le tri"}, "filter_layout": {"label": "Disposition du filtre", "options__1": {"label": "Encadré"}, "options__2": {"label": "<PERSON><PERSON>e largeur"}}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adaptation à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image au survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application de notation des produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "header__1": {"content": "Filtrage et tri"}, "header__3": {"content": "Carte produit"}, "enable_tags": {"label": "<PERSON>r le filtrage", "info": "Personnalisez les filtres avec l'application de recherche et de découverte. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_quick_buy": {"label": "Activer la vue rapide", "info": "Optimal avec un type de panier en pop-up ou tiroir."}, "enable_quick_add": {"label": "Activer l’ajout rapide au panier"}, "list_color_variants_in_collection": {"label": "Afficher les variantes de couleur du produit dans la collection", "info": "Assurez-vous de définir des images en vedette pour toutes vos variantes de produit"}, "list_size_variants_in_collection": {"label": "Afficher les variantes de taille du produit dans la collection"}, "hide_unavailable": {"label": "Masquer les produits indisponibles et épuisés"}, "quick_add_position": {"label": "Position de l'ajout rapide", "options__1": {"label": "Superposition"}, "options__2": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "disable_quick_add": {"label": "Désactiver l'ajout rapide sur mobile lorsque activé sur ordinateur de bureau"}, "product_count": {"label": "Activer le décompte des produits", "info": "Le nombre ne comprend pas les variantes de produits"}}, "blocks": {"promo_row": {"name": "Ligne promotionnelle", "settings": {"text": {"label": "Placez la promotion après le produit:", "info": "Entrez un nombre entier"}, "image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "subheading": {"label": "Texte"}, "link_label": {"label": "Libellé du lien"}, "link": {"label": "<PERSON><PERSON>"}, "banner_height": {"label": "<PERSON>ur de l'image", "options__1": {"label": "S'adapter à la première image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}}, "banner_layout": {"label": "Disposition de la bannière", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Grille"}}, "show_text_box": {"label": "Affiche<PERSON> la boîte de texte"}, "image_overlay_opacity": {"label": "Opacité du superposition de l'image"}}}}}, "main-list-collections": {"name": "Page de liste de collections", "settings": {"title": {"label": "<PERSON>-tête"}, "sort": {"label": "Trier les collections par :", "options__1": {"label": "Alphabétiquement, A-Z"}, "options__2": {"label": "Alphabétiquement, Z-A"}, "options__3": {"label": "Date, du plus récent au plus ancien"}, "options__4": {"label": "Date, du plus ancien au plus récent"}, "options__5": {"label": "Nombre de produits, du plus élevé au plus faible"}, "options__6": {"label": "Nombre de produits, du plus faible au plus élevé"}}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adaptation à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "info": "Ajoutez des images en éditant vos collections. [En savoir plus](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur de bureau"}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}}, "main-login": {"name": "Connexion"}, "main-order": {"name": "Commande"}, "main-page": {"name": "Page"}, "main-password-footer": {"name": "Pied de page de mot de passe"}, "main-password-header": {"name": "En-tête de mot de passe", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Modifiez votre logo dans les paramètres du thème."}}}, "main-product": {"name": "Informations sur le produit", "blocks": {"spacer": {"name": "Séparateur", "settings": {"margin_top": {"label": "Marge supérieure"}}}, "tabs": {"name": "Onglets", "settings": {"centered_tabs": {"label": "Centrer la navigation des onglets"}, "remove_border_tabs": {"label": "Masquer la bordure inférieure de la navigation des onglets"}, "header_item_1": {"content": "Élément 1"}, "header_item_2": {"content": "Élément 2"}, "header_item_3": {"content": "Élément 3"}, "heading_1": {"label": "Titre"}, "heading_2": {"label": "Titre"}, "heading_3": {"label": "Titre"}, "row_content_1": {"label": "Contenu"}, "row_content_2": {"label": "Contenu"}, "row_content_3": {"label": "Contenu"}}}, "payment_enable": {"name": "Icônes de paiement", "settings": {"header": {"content": "Masquer sur mobile / ordinateur", "info": "Cas d'utilisation : positionnement différent pour ordinateur et mobile. Exemple : positionnez ce bloc sur ordinateur et masquez-le sur mobile. Ensuite, utilisez une autre instance du même bloc pour le positionner sur mobile et le masquer sur ordinateur."}, "hide_mobile": {"label": "Masquer le bloc sur mobile"}, "hide_desktop": {"label": "Masquer le bloc sur ordinateur"}, "header_quick_view": {"content": "Fenêtre modale de vue rapide", "info": "Choisissez d'afficher ou de masquer le bloc dans la vue rapide"}, "hide_quick_view": {"label": "Masquer le bloc dans la vue rapide"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "show_text_background": {"label": "Marge intérieure du texte"}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}, "image_link": {"label": "<PERSON><PERSON>"}, "text_1": {"label": "Texte"}, "text_1_style": {"label": "Style de texte", "options__1": {"label": "Corps de texte"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "vendor": {"name": "Fournisseur"}, "title": {"name": "Titre", "settings": {"column": {"label": "Position", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Sous la galerie"}}, "margin_top": {"label": "Marge supérieure"}}}, "price": {"name": "Prix", "settings": {"text": {"label": "Texte"}}}, "waiting_list": {"name": "Liste d'Attente", "settings": {"paragraph": {"content": "Inscription sur la liste d'attente pour les produits en rupture de stock"}, "waiting_list_title": {"label": "Titre"}, "waiting_list_tagline": {"label": "<PERSON><PERSON><PERSON>"}, "waiting_list_notice": {"label": "<PERSON><PERSON>"}, "waiting_list_button": {"label": "Bouton"}}}, "product-meta": {"name": "Inventaire et notation", "settings": {"header_inventory": {"content": "Inventaire"}, "show_product_inventory": {"label": "Afficher l'inventaire"}, "text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Seuil d'inventaire bas", "info": "Choisissez 0 pour toujours afficher en stock si disponible."}, "show_inventory_quantity": {"label": "A<PERSON><PERSON><PERSON> le compte de l'inventaire"}, "header_sku": {"content": "Numéro de référence"}, "show_product_sku": {"label": "<PERSON><PERSON><PERSON><PERSON> le numéro de référence"}, "header_rating": {"content": "Évaluation"}, "info": {"content": "Ren<PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/product-ratings-and-reviews) pour en savoir plus"}, "show_product_rating": {"label": "Afficher l'évaluation"}}}, "dynamic_card_icons": {"name": "Icônes dynamiques", "settings": {"info": {"content": "Ren<PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/dynamic-icons-with-text) pour en savoir plus"}, "card_metafield_text": {"label": "Texte"}, "card_metafield_key": {"label": "Clé de méta-champ de la carte"}, "card_metafield_image_size": {"label": "<PERSON>lle de l'image", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Très grande"}}, "card_metafield_layout": {"label": "Mise en page", "options__1": {"label": "Large"}, "options__2": {"label": "Étroite"}, "options__3": {"label": "En ligne"}}, "card_metafield_icon_title_font_weight": {"label": "Poids de la police du titre de l'icône", "options__1": {"label": "Gras"}, "options__2": {"label": "Normal"}}, "card_metafield_border": {"label": "Bordure de l'image", "options__1": {"label": "Avec bordure"}, "options__2": {"label": "Sans bordure"}}, "card_metafield_enable_border_radius": {"label": "<PERSON><PERSON> le rayon de la bordure - cercle"}, "icons_tooltip": {"label": "Afficher le titre de l’icône comme info-bulle"}}}, "inventory": {"name": "Statut d'inventaire", "settings": {"text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Seuil de faible inventaire", "info": "Choisissez 0 pour toujours afficher en stock si disponible."}, "show_inventory_quantity": {"label": "<PERSON><PERSON><PERSON><PERSON> le compte d'inventaire"}}}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variantes", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Past<PERSON>s"}, "options__3": {"label": "Simple"}}}}, "countdown-timer": {"name": "Compte à rebours"}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Afficher les boutons de paiement dynamique", "info": "En utilisant les méthodes de paiement disponibles sur votre boutique, les clients verront leur option préférée, comme PayPal. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Afficher le formulaire d'informations du destinataire pour les cartes-cadeaux", "info": "Permet aux acheteurs d'envoyer des cartes-cadeaux à une date programmée avec un message personnel. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}, "hide_unavailable": {"label": "Masquer les variantes indisponibles et épuisées"}, "variant": {"content": "Sélecteur de variante"}, "swatch_shape": {"label": "Style Swatch", "info": "Consultez la documentation [En savoir plus](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches)", "options__1": {"label": "Cercle"}, "options__2": {"label": "Carré"}, "options__3": {"label": "Aucun"}}}}, "pickup_availability": {"name": "Disponibilité du retrait en magasin"}, "description": {"name": "Description"}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "delivery_estimator": {"name": "Estimate<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "settings": {"info": {"content": "Ren<PERSON>-vous sur [documentation du thème](https://manathemes.com/docs/flux-theme/how-to-guides/delivery-estimator) pour en savoir plus"}, "delivery_estimator_text": {"label": "Texte de l'estimateur de livraison"}, "earliest_delivery": {"label": "Livraison la plus rapide", "info": "Nombre minimum de jours pour la livraison, par exemple : 2"}, "latest_delivery": {"label": "Livraison la plus tardive", "info": "Nombre maximum de jours pour la livraison, par exemple : 5"}, "text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "share": {"name": "Partager", "settings": {"text": {"label": "Texte"}, "featured_image_info": {"content": "Si vous incluez un lien dans les publications sur les réseaux sociaux, l'image vedette de la page sera affichée en tant qu'image de prévisualisation. [En savoir plus](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un titre de boutique et une description sont inclus avec l'image de prévisualisation. [En savoir plus](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "share_top_right_corner": {"label": "Positionner le partage en haut à droite"}}}, "custom_liquid": {"name": "Liquid personnalisé", "settings": {"custom_liquid": {"label": "Liquid personnalisé", "info": "Ajoutez des extraits d'application ou d'autres codes Liquid pour créer des personnalisations avancées."}}}, "collapsible_tab": {"name": "Ligne pliable", "settings": {"heading": {"info": "Incluez un titre qui explique le contenu.", "label": "Titre"}, "no_padding": {"label": "Pas de marge intérieure"}, "show_spacer": {"label": "Afficher l’espaceur"}, "image": {"label": "Image"}, "content": {"label": "Contenu de la ligne"}, "page": {"label": "Contenu de la ligne à partir de la page"}, "icon": {"label": "Icône", "options__1": {"label": "Aucune"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Cœur de sac"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON> <PERSON> b<PERSON>"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Bulle de discussion"}, "options__9": {"label": "Coche"}, "options__10": {"label": "Presse-papiers"}, "options__11": {"label": "<PERSON><PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON><PERSON>"}, "options__13": {"label": "Enveloppe"}, "options__14": {"label": "Œil"}, "options__15": {"label": "<PERSON><PERSON><PERSON>-gouttes"}, "options__16": {"label": "Point d'exclamation"}, "options__17": {"label": "<PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "Globe"}, "options__20": {"label": "<PERSON><PERSON><PERSON>"}, "options__21": {"label": "Casque d'écoute"}, "options__22": {"label": "Liste"}, "options__23": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__24": {"label": "Verrou"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "É<PERSON>le de carte"}, "options__27": {"label": "Tasse"}, "options__28": {"label": "<PERSON>p<PERSON><PERSON><PERSON> de patte"}, "options__29": {"label": "Shop"}, "options__30": {"label": "<PERSON><PERSON>"}, "options__31": {"label": "Avion"}, "options__32": {"label": "Plante"}, "options__33": {"label": "Étiquette de prix"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "Recyclage"}, "options__36": {"label": "Retour"}, "options__37": {"label": "Flocon de neige"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "Chronomètre"}, "options__40": {"label": "Étiquette"}, "options__41": {"label": "Arbre"}, "options__42": {"label": "<PERSON><PERSON> lev<PERSON>"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Point d'interrogation"}}}}, "popup": {"name": "Fenêtre contextuelle", "settings": {"link_label": {"label": "Libellé du lien"}, "page": {"label": "Page"}}}, "rating": {"name": "Note du produit", "settings": {"paragraph": {"content": "Pour afficher une note, ajoutez une application de notation de produit. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Produits complémentaires", "settings": {"paragraph": {"content": "Pour sélectionner des produits complémentaires, ajoutez l'application de recherche et de découverte. [En savoir plus](https://manathemes.com/docs/flux-theme/how-to-guides/complementary-and-related-products)"}, "heading": {"label": "Titre"}, "make_collapsible_row": {"label": "Afficher en tant que ligne pliable"}, "icon": {"info": "Visible lorsque la ligne pliable est affichée."}, "product_list_limit": {"label": "Nombre maximal de produits à afficher"}, "products_per_page": {"label": "Nombre de produits par page"}, "pagination_style": {"label": "Style de pagination", "options": {"option_1": "Points", "option_2": "Compteur", "option_3": "<PERSON><PERSON><PERSON><PERSON>"}}, "product_card": {"heading": "Fiche produit"}, "image_ratio": {"label": "Ratio de l'image", "options": {"option_1": "Portrait", "option_2": "Carré"}}, "enable_quick_add": {"label": "Activer la vue rapide et ajouter des boutons au panier"}, "columns": {"label": "Colonnes", "options": {"option_1": "Un", "option_2": "<PERSON><PERSON>"}}}}, "ingredient_details": {"name": "Détails des ingrédients", "settings": {"header_block_content": {"content": "Contenu des ingrédients"}, "left_column_label": {"label": "Étiquette de la colonne de gauche"}, "right_column_label": {"label": "Étiquette de la colonne de droite"}, "content": {"label": "Contenu", "info": "Séparez l'étiquette et la valeur par une virgule. Utilisez SHIFT + ENTER pour ajouter une nouvelle ligne. Utilisez un tiret pour indenter les lignes. IMPORTANT : N’utilisez pas de titres ni de styles de liste."}}}, "icon_with_text": {"name": "Icône avec texte", "settings": {"layout": {"label": "Disposition", "options__1": {"label": "Horizontale"}, "options__2": {"label": "Verticale"}}, "content": {"label": "Contenu", "info": "Choisissez une icône ou ajoutez une image pour chaque colonne ou rangée."}, "heading": {"info": "Laissez le libellé du titre vide pour masquer la colonne d'icônes."}, "icon_1": {"label": "Première icône"}, "image_1": {"label": "Première image"}, "heading_1": {"label": "Premier titre"}, "icon_2": {"label": "Deuxième icône"}, "image_2": {"label": "Deuxième image"}, "heading_2": {"label": "Deuxième titre"}, "icon_3": {"label": "Troisième icône"}, "image_3": {"label": "Troisième image"}, "heading_3": {"label": "Troisième titre"}}}}, "settings": {"info": {"content": "Visitez la [documentation du thème](https://manathemes.com/docs/flux-theme/product-page/) pour en savoir plus"}, "header_layout": {"content": "Mise en page"}, "header": {"content": "<PERSON><PERSON><PERSON><PERSON>", "info": "En savoir plus sur les [types de médias.](https://help.shopify.com/manual/products/product-media)"}, "enable_full_width": {"label": "Rendre la mise en page pleine largeur"}, "enable_sticky_info": {"label": "Activer le contenu adhérent sur ordinateur"}, "color_scheme": {"label": "Schéma de couleurs des informations sur le produit"}, "enable_info_padding": {"label": "Activer le remplissage de la boîte d'informations sur le produit"}, "product_gallery_width": {"label": "Largeur de la galerie de produits"}, "product_layout": {"label": "Mise en page de bureau", "options__1": {"label": "Mise en page 1"}, "options__2": {"label": "Mise en page 2"}, "options__3": {"label": "Mise en page 3"}, "options__4": {"label": "Mise en page 4"}, "options__5": {"label": "Mise en page 5"}}, "product_background_color": {"label": "<PERSON><PERSON><PERSON> de fond"}, "title": {"label": "Titre"}, "gallery_layout": {"label": "Disposition sur ordinateur", "options__1": {"label": "Simple"}, "options__2": {"label": "Grille"}, "options__3": {"label": "Vignettes à gauche"}, "options__4": {"label": "Vignettes en bas"}, "options__5": {"label": "Vignettes à droite"}, "options__6": {"label": "Diapositive"}}, "constrain_to_viewport": {"label": "Restreindre les médias à la hauteur de l'écran"}, "media_size": {"label": "Largeur des médias sur ordinateur", "info": "Les médias sont automatiquement optimisés pour les mobiles.", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Grand"}}, "image_zoom": {"label": "Zoom de l'image", "info": "Cliquez et survolez pour ouvrir la lightbox sur mobile.", "options__1": {"label": "Ouvrir la lightbox"}, "options__2": {"label": "Cliquez et survolez"}, "options__3": {"label": "Pas de zoom"}}, "media_style": {"label": "Style des médias et des informations", "options__1": {"label": "Style un"}, "options__2": {"label": "Style deux"}}, "media_position": {"label": "Position des médias sur ordinateur", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "media_fit": {"label": "Ajustement des médias", "options__1": {"label": "Original"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "thumbnail_size": {"label": "<PERSON><PERSON> de vignette", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}}, "under_gallery": {"label": "Position du contenu sous la galerie", "options__1": {"label": "Premier"}, "options__2": {"label": "Deuxième"}}, "mobile_thumbnails": {"label": "Mise en page de la galerie", "options__1": {"label": "2 Colonnes"}, "options__2": {"label": "Aff<PERSON><PERSON> les vignettes"}, "options__3": {"label": "Masquer les vignettes"}}, "hide_variants": {"label": "Masquer les médias des autres variantes après la sélection d'une variante"}, "enable_video_looping": {"label": "Activer la lecture en boucle de la vidéo"}, "header_in_stock_colors": {"content": "Couleurs en stock"}, "in_stock_background_color": {"label": "Couleur de fond en stock"}, "in_stock_color": {"label": "Couleur du texte en stock"}, "desktop_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}, "header_quantity_selector": {"content": "Sélecteur de quantité"}, "quantity_selector": {"label": "Bordure du sélecteur de quantité"}, "header_mobile": {"content": "Disposition mobile"}, "mobile_enable_gradient": {"label": "Activer l'arrière-plan de décoration"}}}, "main-register": {"name": "Inscription"}, "main-reset-password": {"name": "Réinitialisation de mot de passe"}, "main-search": {"name": "Résultats de recherche", "settings": {"columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image au survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application de notation de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "header__1": {"content": "Fiche produit"}, "header__2": {"content": "Fiche de blog", "info": "Les styles de la fiche de blog s'appliquent également aux fiches de page dans les résultats de recherche. Pour changer les styles des fiches, mettez à jour les paramètres de votre thème."}, "header__3": {"content": "<PERSON><PERSON>"}, "show_article_posts": {"label": "Afficher les cartes d’articles"}, "show_page_posts": {"label": "Afficher les cartes de pages"}, "article_show_date": {"label": "Affiche<PERSON> la date"}, "article_show_author": {"label": "Afficher l'auteur"}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}}, "quick-order-list": {"name": "Liste de commande rapide", "settings": {"enable_card_background": {"label": "<PERSON>r le fond de carte"}, "show_image": {"label": "Afficher l'image"}, "show_sku": {"label": "Afficher le SKU"}}, "presets": {"name": "Liste de commande rapide"}}, "multicolumn": {"name": "Multicolumn", "settings": {"multicolumn_style": {"label": "Disposition", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Élégant"}}, "enable_card_background": {"label": "Activer l'arrière-plan de la carte"}, "caption": {"label": "sous-titre"}, "title": {"label": "Titre"}, "image_width": {"label": "Largeur de l'image", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "options__4": {"label": "Cercle"}, "options__5": {"label": "Asymétrique"}}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "column_alignment": {"label": "Alignement des colonnes", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}}, "button_label": {"label": "Libellé du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "desktop_enable_gradient": {"label": "<PERSON>r le fond décoratif"}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "mobile_enable_gradient": {"label": "<PERSON>r le fond décoratif"}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "hide_image": {"label": "Masquer l'image"}, "title": {"label": "Titre"}, "text": {"label": "Description"}, "link_label": {"label": "Libellé du lien"}, "link": {"label": "<PERSON><PERSON>"}}}}, "presets": {"name": "Multicolumn"}}, "events-calendar": {"name": "Calendrier des événements", "settings": {"enable_card_background": {"label": "<PERSON>r le fond de carte"}, "caption": {"label": "Sous-titre"}, "title": {"label": "Titre"}, "image_width": {"label": "Largeur de l'image", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "options__4": {"label": "Complet"}}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "options__4": {"label": "Cercle"}, "options__5": {"label": "Asymétrique"}}, "event_layout": {"label": "Disposition", "options__1": {"label": "Grille"}, "options__2": {"label": "Liste"}}, "columns_desktop": {"label": "Nombre de colonnes sur le bureau"}, "column_alignment": {"label": "Alignement des colonnes", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centré"}}, "button_label": {"label": "Étiquette du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "desktop_enable_gradient": {"label": "<PERSON>r le fond décoratif"}, "header_mobile": {"content": "Disposition mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "mobile_enable_gradient": {"label": "<PERSON>r le fond décoratif"}}, "blocks": {"event": {"name": "Événement", "settings": {"image": {"label": "Image"}, "hide_image": {"label": "Masquer l'image"}, "event_date": {"label": "Date"}, "event_month": {"label": "<PERSON><PERSON>", "options__1": {"label": "Jan"}, "options__2": {"label": "Fév"}, "options__3": {"label": "Mar"}, "options__4": {"label": "Avr"}, "options__5": {"label": "<PERSON>"}, "options__6": {"label": "Juin"}, "options__7": {"label": "<PERSON><PERSON>"}, "options__8": {"label": "Août"}, "options__9": {"label": "Sep"}, "options__10": {"label": "Oct"}, "options__11": {"label": "Nov"}, "options__12": {"label": "Déc"}}, "hide_date": {"label": "Masquer la date"}, "event_time": {"label": "Jour et heure"}, "event_price": {"label": "Prix"}, "event_heading": {"label": "Titre"}, "event_description": {"label": "Texte"}, "event_location": {"label": "<PERSON><PERSON>"}, "link_label": {"label": "Étiquette du lien"}, "link": {"label": "<PERSON><PERSON>"}}}}, "presets": {"name": "Calendrier des événements"}}, "multicolumn-cover": {"name": "Multicolumn cover", "settings": {"caption": {"label": "sous-titre"}, "title": {"label": "Titre"}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "min_overlay_height": {"label": "Ajuster la hauteur du recouvrement"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "column_alignment": {"label": "Alignement des colonnes", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}}, "background_style": {"label": "Arrière-plan secondaire", "options__1": {"label": "Aucun"}, "options__2": {"label": "Afficher comme arrière-plan de colonne"}}, "button_label": {"label": "Libellé du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "show_text_box": {"label": "Afficher la zone de texte"}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "full_width": {"label": "Rendre la section pleine largeur"}, "image_overlay_opacity": {"label": "Opacité", "info": "Vous pouvez changer la couleur d'opacité globalement depuis les [paramètres du thème](/editor?context=theme&category=colors)."}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "title": {"label": "Titre"}, "text": {"label": "Description"}, "link_label": {"label": "Libellé du lien"}, "link": {"label": "<PERSON><PERSON>"}}}}, "presets": {"name": "Multicolumn cover"}}, "icons-with-text": {"name": "Icônes avec texte", "settings": {"image_width": {"label": "Largeur de l'image", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}, "options__4": {"label": "<PERSON><PERSON>e largeur"}}, "image_ratio": {"label": "Ratio de l'image", "options__1": {"label": "Adapté à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "options__4": {"label": "Cercle"}, "options__5": {"label": "Asymétrique"}}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "column_alignment": {"label": "Alignement des colonnes", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}}, "enable_desktop_slider": {"label": "<PERSON><PERSON> le carrousel sur ordinateur"}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "title": {"label": "Titre"}, "text": {"label": "Description"}, "link_label": {"label": "Libellé du lien"}, "link": {"label": "<PERSON><PERSON>"}}}}, "presets": {"name": "Icônes avec texte"}}, "infocards": {"name": "Cartes d'informations", "settings": {"cards_style": {"label": "Disposition", "options__1": {"label": "1 Colonne"}, "options__2": {"label": "2 Colonnes"}, "options__3": {"label": "3 Colonnes"}}}, "blocks": {"infocard": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Image"}, "hide_image": {"label": "Masquer l'image"}, "title": {"label": "Titre"}, "caption": {"label": "Légende"}, "text": {"label": "Description"}, "link_label": {"label": "Libellé du lien"}, "link": {"label": "<PERSON><PERSON>"}, "infocards_background_color": {"label": "Couleur de fond de la carte"}, "infocards_color": {"label": "Couleur du texte de la carte"}}}}, "presets": {"name": "Cartes d'informations"}}, "testimonials": {"name": "Témoignages", "settings": {"title": {"label": "<PERSON>-tête"}, "caption": {"label": "sous-titre"}, "desktop_title_caption_position": {"label": "Position de l'en-tête et de la légende sur ordinateur", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "button_label": {"label": "Libellé du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "desktop_enable_gradient": {"label": "<PERSON>r le fond de décoration"}, "testimonials_style": {"label": "Style des témoignages", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Moderne"}}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "mobile_enable_gradient": {"label": "<PERSON>r le fond de décoration"}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "title": {"label": "Titre"}, "text": {"label": "Description"}, "rating": {"label": "Afficher les étoiles"}, "rating_stars": {"label": "Étoiles de notation", "options__1": {"label": "5 Étoiles"}, "options__2": {"label": "4 Étoiles"}, "options__3": {"label": "3 Étoiles"}, "options__4": {"label": "2 Étoiles"}, "options__5": {"label": "1 Étoile"}}, "product": {"label": "Produit"}}}}, "presets": {"name": "Témoignages"}}, "promo-popup": {"name": "Fenêtre contextuelle promotionnelle", "settings": {"enable_popup": {"label": "<PERSON>r la fenêtre contextuelle"}, "popup_test": {"label": "Afficher la fenêtre contextuelle de test"}, "layout": {"label": "Disposition", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Grille"}, "options__3": {"label": "Grille-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "popup_title": {"label": "Titre"}, "header_font_size": {"label": "Taille de police du titre"}, "popup_message": {"label": "Message"}, "message_font_size": {"label": "Taille de police du message"}, "submit_button_text": {"label": "Texte du bouton de soumission"}, "button_color": {"label": "<PERSON>uleur du bouton"}, "button_text_color": {"label": "Couleur du texte du bouton"}, "button_hover_color": {"label": "Couleur au survol du bouton"}, "success_message": {"label": "Message de succès"}, "fadein": {"label": "Temps avant l'apparition de la fenêtre contextuelle"}, "popup_count": {"label": "Nombre de fenêtres contextuelles"}, "popup_image": {"label": "Sélectionner une image pour la fenêtre contextuelle"}, "image_width": {"label": "Largeur de l'image"}, "image_alt": {"label": "Texte alternatif de l'image"}}, "presets": {"name": "Fenêtre contextuelle promotionnelle"}}, "promotion-cards": {"name": "Cartes promotionnelles", "settings": {"title": {"label": "<PERSON>-tête"}, "caption": {"label": "sous-titre"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "header_mobile": {"content": "Mise en page mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "promotion_cards_style": {"label": "Style de la carte promotionnelle", "options__1": {"label": "Moderne"}, "options__2": {"label": "Élégant"}, "options__3": {"label": "Couverture"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "title": {"label": "Titre"}, "link_label": {"label": "Libellé du lien"}, "link": {"label": "<PERSON><PERSON>"}, "product": {"label": "Produit"}}}}, "presets": {"name": "Cartes promotionnelles"}}, "newsletter": {"name": "Inscription par e-mail", "settings": {"full_width": {"label": "Rendre la section en largeur complète", "info": "Visible uniquement avec un arrière-plan de couleur."}, "paragraph": {"content": "Chaque inscription par e-mail crée un compte client. [En savoir plus](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}}}, "paragraph": {"name": "Sous-titre", "settings": {"paragraph": {"label": "Description"}}}, "email_form": {"name": "Formulaire d'e-mail"}}, "presets": {"name": "Inscription par e-mail"}}, "newsletter-banner": {"name": "Bannière d'inscription par e-mail", "settings": {"paragraph": {"content": "Chaque inscription par e-mail crée un compte client. [En savoir plus](https://help.shopify.com/manual/customers)"}, "image": {"label": "Image"}, "image_2": {"label": "Image", "info": "Cette image sera affichée lorsque vous sélectionnez 'Style deux' pour le style de votre newsletter."}, "newsletter_style": {"label": "Style de la newsletter", "options__1": {"label": "Une image"}, "options__2": {"label": "Deux images"}, "options__3": {"label": "Pas d'image"}}, "layout": {"label": "Disposition sur ordinateur", "options__1": {"label": "Image d'abord"}, "options__2": {"label": "Texte d'abord"}}, "full_width": {"label": "Rendre la section en largeur complète"}}, "blocks": {"caption": {"name": "sous-titre", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Sous-titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON>lle du texte", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}}}}, "heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}}}, "paragraph": {"name": "Paragraphe", "settings": {"paragraph": {"label": "Description"}, "text_style": {"options__1": {"label": "Texte principal"}, "options__2": {"label": "Sous-titre"}, "label": "Style de texte"}}}, "email_form": {"name": "Formulaire d'e-mail"}}, "presets": {"name": "Bannière d'inscription par e-mail"}}, "recently-viewed-products": {"name": "Productos vistos recientemente", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"label": "Subtítulo"}}, "presets": {"name": "Productos vistos recientemente"}}, "page": {"name": "Page", "settings": {"page": {"label": "Page"}}, "presets": {"name": "Page"}}, "related-products": {"name": "Produits associés", "settings": {"heading": {"label": "<PERSON>-tête"}, "caption": {"label": "sous-titre"}, "text_style": {"label": "Style de texte de légende", "options__1": {"label": "Sous-titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "text_size": {"label": "Taille de texte de légende", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}, "products_to_show": {"label": "Nombre maximal de produits à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur de bureau"}, "paragraph__1": {"content": "Les recommandations dynamiques utilisent l'ordre et les informations sur les produits pour évoluer et s'améliorer avec le temps. [En savoir plus](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Fiche produit"}, "image_ratio": {"label": "Ratio d'image", "options__1": {"label": "S'adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image au survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application d'évaluation de produit. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Disposition mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}}, "rich-text": {"name": "Texte enrichi", "settings": {"desktop_content_position": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position du contenu sur ordinateur de bureau", "info": "La position est automatiquement optimisée pour les mobiles."}, "content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu"}, "header_mobile": {"content": "Disposition mobile"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu"}, "full_width": {"label": "Rendre l'arrière-plan de la section pleine largeur", "info": "Visible uniquement avec un arrière-plan de couleur."}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image", "info": "Pour une apparence optimale, veuillez vous assurer que les dimensions de l'image sont de 120x120 pixels."}}}, "heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "caption": {"name": "sous-titre", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Sous-titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON>lle du texte", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Libellé du premier bouton", "info": "Laissez le libellé vide pour masquer le bouton."}, "button_link_1": {"label": "Lien du premier bouton"}, "button_style_secondary_1": {"label": "Utiliser le style de bouton en contour"}, "button_label_2": {"label": "Libellé du deuxième bouton", "info": "Laissez le libellé vide pour masquer le bouton."}, "button_link_2": {"label": "Lien du deuxième bouton"}, "button_style_secondary_2": {"label": "Utiliser le style de bouton en contour"}}}}, "presets": {"name": "Texte enrichi"}}, "scrolling-text": {"name": "Texte/Image défilant", "settings": {"scroll_direction": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "G<PERSON><PERSON>"}, "label": "Direction du défilement"}, "enable_scroll_decoration": {"label": "Activer la décoration"}, "scroll_decoration": {"label": "Décoration de défilement", "options__1": {"label": "Cercle"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Hexagone"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "enable_stencil_text": {"label": "Style de texte pochoir"}, "scroll_speed": {"label": "Vitesse"}, "scroll_height": {"label": "<PERSON><PERSON> <PERSON>"}, "scroll_text_size": {"label": "<PERSON>lle du texte"}, "hover_stop": {"label": "<PERSON><PERSON><PERSON><PERSON> au survol"}, "keep_small_mobile": {"label": "Garder sur petits mobiles"}, "border": {"label": "Ajouter une bordure"}, "enable_announcement_bar_desktop_sticky": {"label": "Activer la mise en page fixe sur le bureau"}, "enable_announcement_bar_mobile_sticky": {"label": "Activer la mise en page fixe sur mobile"}}, "blocks": {"text": {"name": "Texte/Image", "settings": {"text": {"label": "Description"}, "image": {"label": "Image", "info": "Si vous ajoutez une image, le texte sera caché !"}, "image_link": {"label": "Lien de l'image"}}}}, "presets": {"name": "Texte/Image défilant"}}, "video": {"name": "Vidéo", "settings": {"caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "text": {"label": "Description"}, "button_label_1": {"label": "Étiquette du bouton"}, "button_link_1": {"label": "<PERSON>n du bouton"}, "video_layout": {"label": "Disposition", "options__1": {"label": "Disposition un"}, "options__2": {"label": "Disposition deux"}}, "cover_image": {"label": "Image de couverture"}, "video_url": {"label": "URL", "placeholder": "Utilisez une URL YouTube ou Vimeo", "info": "Accepte l'URL de YouTube ou Vimeo"}, "description": {"label": "Texte alternatif de la vidéo", "info": "Décrivez la vidéo pour les clients utilisant des lecteurs d'écran. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Ajouter un espacement à l'image", "info": "Sélectionnez un espacement pour l'image si vous ne souhaitez pas que votre image de couverture soit recadrée."}, "full_width": {"label": "Rendre la section pleine largeur"}}, "presets": {"name": "Vidéo"}}, "video-background": {"name": "Arrière-plan Vidéo", "settings": {"video_url": {"label": "Sélectionner une vidéo", "info": "Téléchargez votre vidéo dans Contenu - Fichiers, puis copiez le lien et collez-le ici."}, "poster": {"label": "Ajoutez une image de fond de secours au cas où la vidéo ne se chargerait pas"}, "caption": {"label": "Sous-titre"}, "heading": {"label": "Titre"}, "text": {"label": "Description"}, "button_label": {"label": "Libellé du bouton", "info": "Laissez le libellé vide pour masquer le bouton."}, "link": {"label": "<PERSON>n du bouton"}, "secondary_style": {"label": "Utiliser le style de bouton en contour"}, "full_width_background": {"label": "Rendre la section pleine largeur"}, "background_height": {"label": "Hauteur de l'arrière-plan du bureau", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}, "background_height_mobile": {"label": "Hauteur de l’arrière-plan sur mobile", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}, "text_align": {"label": "Alignement du texte", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "box_align": {"label": "Position du contenu", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "ignore_box": {"label": "<PERSON><PERSON> la boîte solide"}, "blur": {"label": "Ajouter un flou pour les vidéos de faible qualité"}, "opacity": {"label": "Modifier l'opacité de la vidéo"}, "header_video": {"content": "Vidéo"}, "video_style": {"label": "Style de vidéo", "info": "Le mode natif affiche la vidéo dans sa taille d'origine sans superposition de contenu. Pour inclure du contenu, sélectionnez l'option d'arrière-plan.", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Arrière-plan"}}}, "presets": {"name": "Arrière-plan Vidéo"}}, "slideshow": {"name": "Diaporama", "settings": {"layout": {"label": "Disposition", "options__1": {"label": "<PERSON><PERSON>e largeur"}, "options__2": {"label": "En boîte"}}, "slide_height": {"label": "<PERSON><PERSON> de la diapositive", "options__1": {"label": "S'adapter à la première image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Style de pagination", "options__1": {"label": "Flèches"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Faire tourner automatiquement les diapositives"}, "change_slides_speed": {"label": "Changer de diapositive toutes les"}, "slideshow_controls_background": {"label": "Ajouter un arrière-plan aux contrôles du diaporama"}, "slider_animations": {"label": "Animations du diaporama", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Zoom vertical"}, "options__3": {"label": "Zoom horizontal"}, "options__4": {"label": "Révélation verticale"}, "options__5": {"label": "Révélation horizontale"}}, "mobile": {"content": "Disposition mobile"}, "show_text_below": {"label": "Afficher le contenu sous les images sur mobile"}, "accessibility": {"content": "Accessibilité", "label": "Description du diaporama", "info": "Décrivez le diaporama pour les clients utilisant des lecteurs d'écran."}}, "blocks": {"slide": {"name": "Diapositive", "settings": {"image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "subheading": {"label": "Texte"}, "button_label": {"label": "Libellé du bouton", "info": "Laissez le libellé vide pour masquer le bouton."}, "link": {"label": "<PERSON>n du bouton"}, "secondary_style": {"label": "Utiliser le style de bouton en contour"}, "box_align": {"label": "Position du contenu sur ordinateur de bureau", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "En haut à gauche"}, "options__2": {"label": "En haut au centre"}, "options__3": {"label": "En haut à droite"}, "options__4": {"label": "Au milieu à gauche"}, "options__5": {"label": "Au milieu au centre"}, "options__6": {"label": "Au milieu à droite"}, "options__7": {"label": "En bas à gauche"}, "options__8": {"label": "En bas au centre"}, "options__9": {"label": "En bas à droite"}}, "show_text_box": {"label": "Afficher la boîte de texte sur ordinateur de bureau"}, "text_alignment": {"label": "Alignement du contenu sur ordinateur de bureau", "option_1": {"label": "G<PERSON><PERSON>"}, "option_2": {"label": "Centre"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacité de la superposition de l'image"}, "color_scheme": {"info": "Visible lorsque le conteneur est affiché."}, "text_alignment_mobile": {"label": "Alignement du contenu sur mobile", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Diaporama"}}, "slideshow-two-columns": {"name": "Diaporama deux colonnes", "settings": {"layout": {"label": "Disposition", "options__1": {"label": "<PERSON><PERSON>e largeur"}, "options__2": {"label": "En boîte"}}, "slide_height": {"label": "<PERSON><PERSON> de la diapositive", "options__1": {"label": "S'adapter à la première image"}, "options__2": {"label": "Petite"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Style de pagination", "options__1": {"label": "Flèches"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Faire tourner automatiquement les diapositives"}, "change_slides_speed": {"label": "Changer de diapositive toutes les"}, "slideshow_controls_background": {"label": "Ajouter un arrière-plan aux contrôles du diaporama"}, "slider_animations": {"label": "Animations du diaporama", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Zoom vertical"}, "options__3": {"label": "Zoom horizontal"}, "options__4": {"label": "Révélation verticale"}, "options__5": {"label": "Révélation horizontale"}}, "mobile": {"content": "Disposition mobile"}, "hide_slider_controls": {"label": "Masquer les contrôles du curseur"}, "show_text_below": {"label": "Afficher le contenu sous les images sur mobile"}, "accessibility": {"content": "Accessibilité", "label": "Description du diaporama", "info": "Décrivez le diaporama pour les clients utilisant des lecteurs d'écran."}}, "blocks": {"slide": {"name": "Diapositive", "settings": {"image": {"label": "Image"}, "caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "subheading": {"label": "Texte"}, "button_label": {"label": "Libellé du bouton", "info": "Laissez le libellé vide pour masquer le bouton."}, "link": {"label": "<PERSON>n du bouton"}, "secondary_style": {"label": "Utiliser le style de bouton en contour"}, "text_alignment": {"label": "Alignement du contenu sur ordinateur de bureau", "option_1": {"label": "G<PERSON><PERSON>"}, "option_2": {"label": "Centre"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "color_scheme": {"info": "Visible lorsque le conteneur est affiché."}, "text_alignment_mobile": {"label": "Alignement du contenu sur mobile", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Diaporama deux colonnes"}}, "advanced-slideshow": {"name": "Parallax Slider", "settings": {"auto_rotate": {"label": "Rotation automatique des diapositives"}, "slider_direction": {"label": "Direction du diaporama", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "slider_loop": {"label": "<PERSON><PERSON><PERSON> infinie"}, "slider_interval": {"label": "Intervalle du diaporama"}, "slider_height": {"label": "Hauteur du diaporama"}, "full_width": {"label": "<PERSON><PERSON> le <PERSON>ur pleine largeur"}, "box": {"content": "Paramètres de la boîte de contenu"}, "content_height": {"label": "Position verticale du contenu"}, "content_position": {"label": "Position horizontale du contenu"}, "content_size": {"label": "<PERSON><PERSON> de <PERSON> bo<PERSON> de contenu"}, "content_align": {"label": "Alignement du contenu", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "content_box": {"label": "Activer l'arrière-plan de la boîte de contenu"}, "content_opacity": {"label": "Opacité de la boîte de contenu"}, "text": {"content": "Paramètres de texte"}, "heading_size": {"label": "<PERSON><PERSON> du titre"}, "heading_style": {"label": "Style du texte du titre", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Capituler"}}, "caption_size": {"label": "Taille de la légende"}, "link_size": {"label": "<PERSON>lle du lien"}, "caption_style": {"label": "Style du texte de la légende", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Capituler"}}, "mobile": {"content": "Paramètres mobiles"}, "mobile_heading_size": {"label": "<PERSON><PERSON> du titre"}, "mobile_caption_size": {"label": "Taille de la légende"}, "mobile_height": {"label": "Hauteur du diaporama"}, "mobile_content_height": {"label": "Position verticale du contenu"}, "mobile_content_size": {"label": "<PERSON><PERSON> de <PERSON> bo<PERSON> de contenu"}, "other": {"content": "Autres paramètres"}}, "blocks": {"slide": {"name": "Diapositive", "settings": {"image": {"label": "Image"}, "caption": {"label": "Légende"}, "heading": {"label": "Titre"}, "button_label": {"label": "Étiquette du bouton", "info": "Laissez l'étiquette vide pour masquer le bouton."}, "link": {"label": "<PERSON>n du bouton"}, "show_link_button": {"label": "<PERSON><PERSON> le bouton"}, "secondary_style": {"label": "Utiliser le style de bouton de contour"}}}}, "presets": {"name": "Diaporama Avancé"}}, "slick-slider": {"name": "<PERSON><PERSON>lider", "settings": {"header": {"content": "Paramètres du Slider"}, "image_opacity": {"label": "Opacité de l'image"}, "opacity_mobile": {"label": "Opacité uniquement sur mobile"}}, "presets": {"name": "<PERSON><PERSON>lider"}}, "comparison-slider": {"name": "<PERSON>ur de comparaison", "settings": {"title": {"label": "Titre"}, "caption": {"label": "sous-titre"}, "image": {"label": "Image un"}, "image_2": {"label": "Image deux"}, "comparison_slider_layout": {"label": "Disposition", "options__1": {"label": "Vertical"}, "options__2": {"label": "Colonne de d<PERSON>"}, "options__3": {"label": "Colonne de gauche"}}, "height": {"label": "Hauteur sur ordinateur de bureau"}, "mouse_percent": {"label": "Position par défaut du curseur"}, "disable_before_after": {"label": "Désactiver avant/après"}, "before_text": {"label": "Texte avant"}, "after_text": {"label": "<PERSON><PERSON> a<PERSON>rès"}, "header_mobile": {"content": "Mobile"}, "mobile_height": {"label": "Hauteur sur mobile"}, "full_width": {"label": "Rendre la mise en page pleine largeur"}, "accessibility": {"content": "Accessibilité", "label": "Description du diaporama", "info": "Décrivez le diaporama pour les clients utilisant des lecteurs d'écran."}}, "presets": {"name": "<PERSON>ur de comparaison"}}, "anchor_link": {"name": "<PERSON><PERSON> an<PERSON>", "settings": {"anchor": {"label": "Ancre HTM<PERSON>", "info": "Ajouter du texte d'ancre pour lier"}}, "presets": {"name": "<PERSON><PERSON> an<PERSON>"}}, "separator": {"name": "Séparateur", "settings": {"separator_style": {"label": "Style", "options__1": {"label": "Ligne"}, "options__2": {"label": "Vague 1"}, "options__3": {"label": "Vague 2"}, "options__4": {"label": "Vague 3"}, "options__5": {"label": "Vague 4"}, "options__6": {"label": "Nuage 1"}, "options__7": {"label": "Nuage 2"}, "options__8": {"label": "Collines 1"}, "options__9": {"label": "Collines 2"}, "options__10": {"label": "Bord festonné 1"}, "options__11": {"label": "Bord festonné 2"}}, "spacer_color": {"label": "Couleur de l’espaceur"}, "margin_top_desktop": {"label": "Marge supérieure sur ordinateur"}, "margin_bottom_desktop": {"label": "Marge inférieure sur ordinateur"}}, "presets": {"name": "Séparateur"}}, "collapsible_content": {"name": "Contenu extensible", "settings": {"caption": {"label": "sous-titre"}, "heading": {"label": "Titre"}, "heading_alignment": {"label": "Alignement du titre", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "content_style": {"label": "Disposition", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON>s bo<PERSON>"}}, "container_color_scheme": {"label": "Schéma de couleurs du conteneur", "info": "Visible lorsque la disposition est définie sur conteneur en ligne ou conteneur de section."}, "open_first_collapsible_row": {"label": "<PERSON><PERSON><PERSON><PERSON>r la première rangée extensible"}, "open_all_collapsible_row": {"label": "<PERSON><PERSON> ouvrir"}, "header_mobile": {"content": "Disposition mobile"}}, "blocks": {"collapsible_row": {"name": "Rangée extensible", "settings": {"heading": {"info": "Inclure un titre expliquant le contenu.", "label": "Titre"}, "row_content": {"label": "Contenu de la rangée"}, "page": {"label": "Contenu de la rangée depuis la page"}, "icon": {"label": "Icône", "options__1": {"label": "Aucune"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>ur <PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Bulle de discussion"}, "options__9": {"label": "Coche"}, "options__10": {"label": "Presse-papiers"}, "options__11": {"label": "<PERSON><PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON><PERSON>goutte"}, "options__13": {"label": "Enveloppe"}, "options__14": {"label": "Œil"}, "options__15": {"label": "<PERSON><PERSON><PERSON>-gouttes"}, "options__16": {"label": "Point d'exclamation"}, "options__17": {"label": "<PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "Globe"}, "options__20": {"label": "<PERSON><PERSON><PERSON>"}, "options__21": {"label": "Casque audio"}, "options__22": {"label": "Liste"}, "options__23": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__24": {"label": "Verrou"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Punaise de carte"}, "options__27": {"label": "Hydratation"}, "options__28": {"label": "<PERSON>p<PERSON><PERSON><PERSON> de patte"}, "options__29": {"label": "Shop"}, "options__30": {"label": "<PERSON><PERSON>"}, "options__31": {"label": "Avion"}, "options__32": {"label": "Plante"}, "options__33": {"label": "Étiquette de prix"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "Recyclage"}, "options__36": {"label": "Retour"}, "options__37": {"label": "Flocon de neige"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "Chronomètre"}, "options__40": {"label": "Étiquette"}, "options__41": {"label": "Arbre"}, "options__42": {"label": "Pouce en l'air"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Point d'interrogation"}}}}}, "presets": {"name": "Contenu extensible"}}, "tabs": {"name": "Onglets", "settings": {"tabs_style": {"label": "Style", "options__1": {"label": "<PERSON><PERSON> <PERSON><PERSON>"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Bouton"}}, "full_width": {"label": "Rendre les onglets pleine largeur"}}, "blocks": {"tab": {"name": "Onglet", "settings": {"image": {"label": "Image"}, "hide_image": {"label": "Masquer l'image"}, "tab_image_width": {"label": "Taille de l<PERSON>image"}, "heading": {"info": "Inclure un titre expliquant le contenu.", "label": "Titre"}, "row_content": {"label": "Contenu de l'onglet"}, "page": {"label": "Contenu de l'onglet depuis la page"}, "button_label": {"label": "Libellé du premier bouton", "info": "Laissez le libellé vide pour masquer le bouton."}, "button_link": {"label": "Lien du premier bouton"}, "button_style_secondary": {"label": "Utiliser le style de bouton en contour"}}}}, "presets": {"name": "Onglets"}}, "about": {"name": "À propos", "settings": {"caption": {"label": "sous-titre"}, "title": {"label": "Titre"}, "text": {"label": "Texte"}, "image": {"label": "Image"}, "open_first_collapsible_row": {"label": "<PERSON><PERSON><PERSON><PERSON>r la première rangée extensible"}, "header_mobile": {"content": "Disposition mobile"}}, "blocks": {"collapsible_row": {"name": "Rangée extensible", "settings": {"heading": {"info": "Inclure un titre expliquant le contenu.", "label": "Titre"}, "row_content": {"label": "Contenu de la rangée"}, "page": {"label": "Contenu de la rangée depuis la page"}, "icon": {"label": "Icône", "options__1": {"label": "Aucune"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>ur <PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Bulle de discussion"}, "options__9": {"label": "Coche"}, "options__10": {"label": "Presse-papiers"}, "options__11": {"label": "<PERSON><PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON><PERSON>goutte"}, "options__13": {"label": "Enveloppe"}, "options__14": {"label": "Œil"}, "options__15": {"label": "<PERSON><PERSON><PERSON>-gouttes"}, "options__16": {"label": "Point d'exclamation"}, "options__17": {"label": "<PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "Globe"}, "options__20": {"label": "<PERSON><PERSON><PERSON>"}, "options__21": {"label": "Casque audio"}, "options__22": {"label": "Liste"}, "options__23": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__24": {"label": "Verrou"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Punaise de carte"}, "options__27": {"label": "Hydratation"}, "options__28": {"label": "<PERSON>p<PERSON><PERSON><PERSON> de patte"}, "options__29": {"label": "Shop"}, "options__30": {"label": "<PERSON><PERSON>"}, "options__31": {"label": "Avion"}, "options__32": {"label": "Plante"}, "options__33": {"label": "Étiquette de prix"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "Recyclage"}, "options__36": {"label": "Retour"}, "options__37": {"label": "Flocon de neige"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "Chronomètre"}, "options__40": {"label": "Étiquette"}, "options__41": {"label": "Arbre"}, "options__42": {"label": "Pouce en l'air"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Point d'interrogation"}}}}}, "presets": {"name": "À propos"}}}}
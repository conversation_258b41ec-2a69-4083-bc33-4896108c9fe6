{"general": {"password_page": {"login_form_heading": "Geschäft durch Passwort betreten:", "login_password_button": "Mit Passwort betreten", "login_form_password_label": "Passwort", "login_form_password_placeholder": "<PERSON><PERSON>rt", "login_form_error": "Falsches Passwort!", "login_form_submit": "Eintreten", "admin_link_html": "Bist du der Geschäftsinhaber? <a href=\"/admin\" class=\"link underlined-link\">Hier einloggen</a>", "powered_by_shopify_html": "Dieser Shop wird unterstützt von {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Auf Facebook teilen", "share_on_twitter": "Auf Twitter tweeten", "share_on_pinterest": "<PERSON><PERSON>nen"}, "links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Mit dem Einkaufen fortfahren", "pagination": {"label": "Pa<PERSON><PERSON><PERSON>", "page": "Seite {{ number }}", "next": "Nächste Seite", "previous": "Vorherige Seite"}, "search": {"search": "Suche deine Artikel...", "reset": "Suchbegriff löschen"}, "cart": {"view": "<PERSON>nen Warenkorb anzeigen ({{ count }})", "view_empty_cart": "Meinen Warenkorb anzeigen", "item_added": "Artike<PERSON> wurde zu deinem Warenkorb hinzugefügt"}, "breadcrumbs": {"shop_url": "Startseite"}, "share": {"close": "Freigabe schließen", "copy_to_clipboard": "<PERSON>", "share_url": "Link", "success_message": "<PERSON> wurde in die Zwischenablage kopiert"}, "slider": {"of": "von", "next_slide": "Nächste Folie", "previous_slide": "Vorherige Folie", "name": "Slide<PERSON>"}}, "newsletter": {"label": "E-Mail", "success": "Vielen Dank fürs Abonnieren", "button_label": "Abonnieren"}, "accessibility": {"skip_to_text": "Zum Inhalt springen", "skip_to_product_info": "Zur Produktinformation springen", "close": "Schließen", "unit_price_separator": "pro", "vendor": "Anbieter:", "error": "<PERSON><PERSON>", "refresh_page": "Die Auswahl führt zu einer vollständigen Seitenaktualisierung.", "link_messages": {"new_window": "<PERSON><PERSON><PERSON> in einem neuen Fenster.", "external": "Öffnet externe Website."}, "loading": "Lade...", "total_reviews": "gesamte Bewertungen", "star_reviews_info": "{{ rating_value }} von {{ rating_max }} <PERSON><PERSON>", "collapsible_content_title": "Einklappbarer Inhalt", "complementary_products": "Ergänzende Produkte", "layout_switcher": "Layout-Umschalter"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "<PERSON><PERSON> lesen: {{ title }}", "comments": {"one": "{{ count }} Kommentar", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "moderated": "<PERSON>te beachten Sie, dass Kommentare vor der Veröffentlichung genehmigt werden müssen.", "comment_form_title": "Einen Kommentar hinterlassen", "name": "Name", "email": "E-Mail", "message": "Kommentar", "post": "Kommentar veröffentlichen", "back_to_blog": "Zurück zum Blog", "share": "<PERSON><PERSON> Artikel teilen", "success": "<PERSON>hr Kommentar wurde erfolgreich veröffentlicht! Vielen Dank!", "success_moderated": "<PERSON>hr Kommentar wurde erfolgreich veröffentlicht. Wir werden ihn in Kürze freischalten, da unser Blog moderiert wird.", "previous_post": "<PERSON><PERSON><PERSON><PERSON>", "next_post": "Nächster Beitrag"}}, "onboarding": {"product_title": "Beispielprodukt-Titel", "collection_title": "Name <PERSON>hrer Kollektion"}, "products": {"product": {"add_to_cart": "In den Warenkorb", "choose_options": "Schnellansicht", "select_variant": "Optionen auswählen", "choose_product_options": "Optionen für {{ product_name }} wählen", "description": "Beschreibung", "inventory_in_stock": "<PERSON><PERSON>", "inventory_in_stock_show_count": "{{ quantity }} auf Lager", "inventory_low_stock": "<PERSON><PERSON><PERSON>", "inventory_low_stock_show_count": "<PERSON><PERSON><PERSON> Bestand: {{ quantity }} übrig", "inventory_out_of_stock": "<PERSON>cht auf Lager", "inventory_out_of_stock_continue_selling": "<PERSON><PERSON>", "sku": "Artikelnummer", "on_sale": "<PERSON><PERSON><PERSON>", "product_variants": "Produktvarianten", "media": {"gallery_viewer": "Galerieansicht", "load_image": "Bild {{ index }} in Galerieansicht laden", "load_model": "3D-Modell {{ index }} in Galerieansicht laden", "load_video": "Video {{ index }} in Galerieansicht abspielen", "image_available": "Bild {{ index }} ist jetzt in Galerieansicht verfügbar", "open_media": "Medien {{ index }} im Modal öffnen", "play_model": "3D-Modell anzeigen", "play_video": "Video abspielen"}, "quantity": {"label": "<PERSON><PERSON>", "input_label": "Menge für {{ product }}", "increase": "Menge für {{ product }} erhöhen", "decrease": "Menge für {{ product }} verringern", "minimum_of": "Mindestens {{ quantity }}", "maximum_of": "<PERSON><PERSON><PERSON><PERSON> {{ quantity }}", "multiples_of": "Vielfache von {{ quantity }}", "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> im Warenkorb", "note": "Mengenregeln anzeigen"}, "pickup_availability": {"view_store_info": "Filialinformationen anzeigen", "check_other_stores": "Verfügbarkeit in anderen Filialen prüfen", "pick_up_available": "Abholung möglich", "pick_up_available_at_html": "Abholung möglich in <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Aktuell keine Abholung möglich in <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "Abholverfügbarkeit konnte nicht geladen werden", "refresh": "Aktualisieren"}, "price": {"from_price_html": "Ab {{ price }}", "regular_price": "<PERSON><PERSON>", "sale_price": "Sonderpreis", "unit_price": "Stückpreis"}, "volume_pricing": {"title": "Mengenpreise", "note": "Mengenrabatt verfügbar", "minimum": "{{ quantity }}+", "price_at_each": "zu {{ price }}/Stk", "price_range": "{{ minimum }} - {{ maximum }}"}, "share": "Dieses Produkt teilen", "sold_out": "Ausverkauft", "unavailable": "Nicht verfügbar", "vendor": "<PERSON><PERSON><PERSON>", "value_unavailable": "{{ option_value }} - <PERSON><PERSON> verfügbar", "variant_sold_out_or_unavailable": "Variante ausverkauft oder nicht verfügbar", "video_exit_message": "{{ title }} öff<PERSON> ein Vollbildvideo im selben Fenster.", "view_full_details": "Alle Details anzeigen", "xr_button": "In deinem Raum anzeigen", "xr_button_label": "In deinem Raum anzeigen, lädt das Element in einem Fenster für erweiterte Realität", "include_taxes": "Inklusive Steuern.", "shipping_policy_html": "<a href=\"{{ link }}\">Versand</a> wird beim Checkout berechnet."}, "modal": {"label": "Mediengalerie"}, "facets": {"apply": "<PERSON><PERSON><PERSON>", "clear": "Löschen", "clear_all": "Alle entfernen", "from": "<PERSON>", "filter_and_sort": "Filtern und Sortieren", "filter_by_label": "Filtern nach:", "filter_button": "Filtern", "filters_selected": {"one": "{{ count }} ausgewählt", "other": "{{ count }} ausgewählt"}, "filter_selected_accessibility": "{{ type }} ({{ count }} <PERSON><PERSON> ausgewählt)", "show_more": "<PERSON><PERSON> anzeigen", "show_less": "<PERSON><PERSON> anzeigen", "max_price": "Der höchste Preis beträgt {{ price }}", "product_count": {"one": "{{ product_count }} von {{ count }} Produkt", "other": "{{ product_count }} von {{ count }} Produkten"}, "product_count_simple": {"one": "{{ count }} Produkt", "other": "{{ count }} Produkte"}, "reset": "Z<PERSON>ücksetzen", "sort_button": "<PERSON><PERSON><PERSON><PERSON>", "sort_by_label": "Sortieren nach:", "to": "Bis", "clear_filter": "<PERSON><PERSON>"}}, "templates": {"search": {"no_results": "<PERSON><PERSON> Ergebnisse gefunden für „{{ terms }}“. Überprüfen Sie die Rechtschreibung oder verwenden Si<PERSON> ein anderes Wort oder einen anderen Ausdruck.", "page": "Seite", "products": "Produkte", "results_pages_with_count": {"one": "{{ count }} Seite", "other": "{{ count }} Seiten"}, "results_suggestions_with_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} Vorschläge"}, "results_products_with_count": {"one": "{{ count }} Produkt", "other": "{{ count }} Produkte"}, "results_with_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "results_with_count_and_term": {"one": "{{ count }} Ergebnis gefunden für „{{ terms }}“", "other": "{{ count }} Ergebnisse gefunden für „{{ terms }}“"}, "title": "Suchergebnisse", "search_for": "<PERSON><PERSON> nach „{{ terms }}“", "suggestions": "Vorschläge", "pages": "Seiten"}, "cart": {"cart": "<PERSON><PERSON><PERSON>"}, "contact": {"form": {"title": "Kontaktformular", "name": "Name", "email": "E-Mail", "phone": "Telefonnummer", "comment": "Kommentar", "send": "Senden", "post_success": "Vielen Dank für Ihre Kontaktaufnahme. Wir melden uns so bald wie möglich bei Ihnen.", "error_heading": "Bitte überprüfen Sie Folgendes:"}}, "404": {"title": "Seite nicht gefunden", "subtext": "404"}}, "sections": {"header": {"announcement": "Ankündigung", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} <PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON>"}}, "cart": {"title": "<PERSON><PERSON><PERSON>", "caption": "Warenkorb-Artikel", "remove_title": "{{ title }} ent<PERSON>nen", "subtotal": "Zwischensumme", "new_subtotal": "Neue Zwischensumme", "note": "Warenkorbnotiz", "checkout": "<PERSON><PERSON> Ka<PERSON> gehen", "view_cart": "Warenkorb anzeigen", "empty": "Ihr Warenkorb ist leer", "cart_error": "Beim Aktualisieren Ihres Warenkorbs ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "cart_quantity_error_html": "Sie können nur {{ quantity }} von diesem Artikel zu Ihrem Warenkorb hinzufügen.", "taxes_and_shipping_policy_at_checkout_html": "St<PERSON>ern und <a href=\"{{ link }}\">Versand</a> werden beim Checkout berechnet", "taxes_included_but_shipping_at_checkout": "Steuern inbegriffen, Versandkosten beim Checkout berechnet", "taxes_included_and_shipping_policy_html": "Steuern inbegriffen. <a href=\"{{ link }}\">Versand</a> wird beim Checkout berechnet.", "taxes_and_shipping_at_checkout": "Steuern und Versandkosten werden beim Checkout berechnet", "headings": {"product": "Produkt", "price": "Pre<PERSON>", "total": "Gesamt", "quantity": "<PERSON><PERSON>", "image": "Produktbild"}, "update": "Aktualisieren", "login": {"title": "Haben <PERSON> ein Konto?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Melden Sie sich an</a>, um schneller zur Kasse zu gehen."}}, "footer": {"payment": "Zahlungsmethoden"}, "featured_blog": {"view_all": "Alle anzeigen", "onboarding_title": "Blogbeitrag", "onboarding_content": "Geben Sie Ihren Kunden eine Zusammenfassung Ihres Blogbeitrags"}, "featured_collection": {"view_all": "Alle anzeigen", "view_all_label": "Alle Produkte in der {{ collection_name }} Kollektion anzeigen"}, "collection_list": {"view_all": "Alle anzeigen"}, "countdown": {"count_days": "Tage", "count_hours": "Stunden", "count_minutes": "Minuten", "count_seconds": "Sekunden"}, "collection_template": {"empty": "<PERSON>ine Produkte gefunden", "title": "Kollektion", "use_fewer_filters_html": "Verwenden Sie weniger Filter oder <a class=\"{{ class }}\" href=\"{{ link }}\">entfernen Sie alle</a>"}, "video": {"load_video": "Video laden: {{ description }}"}, "slideshow": {"load_slide": "Folie laden", "previous_slideshow": "Vorherige Folie", "next_slideshow": "Nächste Folie", "pause_slideshow": "<PERSON><PERSON><PERSON> pausieren", "play_slideshow": "Diashow abspielen", "carousel": "<PERSON><PERSON><PERSON>", "slide": "Folie"}, "page": {"title": "Seitentitel"}, "quick_order_list": {"product_total": "Produkt Zwischensumme", "view_cart": "<PERSON><PERSON><PERSON>", "each": "{{ money }}/Stk", "product": "Produkt", "variant": "<PERSON><PERSON><PERSON>", "variant_total": "<PERSON><PERSON><PERSON>", "items_added": {"one": "{{ quantity }} <PERSON><PERSON><PERSON> hi<PERSON>", "other": "{{ quantity }} <PERSON><PERSON><PERSON> hi<PERSON>"}, "items_removed": {"one": "{{ quantity }} <PERSON><PERSON><PERSON> entfernt", "other": "{{ quantity }} <PERSON><PERSON><PERSON> entfernt"}, "product_variants": "Produktvarianten", "total_items": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remove_all_single_item_confirmation": "1 Artikel aus Ihrem Warenkorb entfernen?", "remove_all_items_confirmation": "Alle {{ quantity }} Artikel aus Ihrem Warenkorb entfernen?", "remove_all": "Alle entfernen", "cancel": "Abbrechen"}}, "localization": {"country_label": "Land/Region", "language_label": "<PERSON><PERSON><PERSON>", "update_language": "Sprache aktualisieren", "update_country": "Land/Region aktualisieren"}, "customer": {"account": {"title": "Ko<PERSON>", "details": "Kontodetails", "view_addresses": "<PERSON><PERSON><PERSON> anzeigen", "return": "Zurück zu den Kontodetails"}, "account_fallback": "Ko<PERSON>", "activate_account": {"title": "Konto aktivieren", "subtext": "<PERSON>rstellen Sie Ihr Passwort, um Ihr Konto zu aktivieren.", "password": "Passwort", "password_confirm": "Passwort bestätigen", "submit": "Konto aktivieren", "cancel": "<PERSON><PERSON><PERSON>"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "default": "Standard", "add_new": "Neue Adresse hinzufügen", "edit_address": "<PERSON><PERSON><PERSON> bear<PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "company": "Firma", "address1": "Adresse 1", "address2": "Adresse 2", "city": "Stadt", "country": "Land/Region", "province": "Bundesland", "zip": "PLZ", "phone": "Telefonnummer", "set_default": "Als Standardadresse festlegen", "add": "<PERSON><PERSON><PERSON>", "update": "Adresse aktualisieren", "cancel": "Abbrechen", "edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "delete_confirm": "Sind <PERSON> sicher, dass Sie diese Adresse löschen möchten?"}, "log_in": "Anmelden", "log_out": "Abmelden", "login_page": {"cancel": "Abbrechen", "create_account": "<PERSON><PERSON> er<PERSON>", "email": "E-Mail", "forgot_password": "Passwort vergessen?", "guest_continue": "<PERSON><PERSON>", "guest_title": "<PERSON><PERSON> als Gast", "password": "Passwort", "title": "Anmelden", "sign_in": "Einloggen", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "order": {"title": "Bestellung {{ name }}", "date_html": "Aufgegeben am {{ date }}", "cancelled_html": "Bestellung storniert am {{ date }}", "cancelled_reason": "Grund: {{ reason }}", "billing_address": "Re<PERSON>nungsadress<PERSON>", "payment_status": "Zahlungsstatus", "shipping_address": "Lieferadresse", "fulfillment_status": "Erfüllungsstatus", "discount": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON>ers<PERSON>", "tax": "Steuern", "product": "Produkt", "sku": "SKU", "price": "Pre<PERSON>", "quantity": "<PERSON><PERSON>", "total": "Gesamt", "total_refunded": "Erstattet", "fulfilled_at_html": "Erfüllt am {{ date }}", "track_shipment": "Sendung verfolgen", "tracking_url": "Verfolgungslink", "tracking_company": "Versanddienst", "tracking_number": "Sendungsnummer", "subtotal": "Zwischensumme", "total_duties": "Abgaben"}, "orders": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order_number": "Bestellung", "order_number_link": "Bestellnummer {{ number }}", "date": "Datum", "payment_status": "Zahlungsstatus", "fulfillment_status": "Erfüllungsstatus", "total": "Gesamt", "none": "Sie haben bisher noch keine Bestellungen aufgegeben."}, "recover_password": {"title": "Passwort zurücksetzen", "subtext": "Wir werden Ihnen eine E-Mail senden, um Ihr Passwort zurückzusetzen.", "success": "Wir haben Ihnen eine E-Mail mit einem Link zum Aktualisieren Ihres Passworts gesendet."}, "register": {"title": "<PERSON><PERSON> er<PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "email": "E-Mail", "password": "Passwort", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "reset_password": {"title": "Passwort des Kontos zurücksetzen", "subtext": "<PERSON><PERSON><PERSON> Si<PERSON> ein neues Passwort ein", "password": "Passwort", "password_confirm": "Passwort bestätigen", "submit": "Passwort zurücksetzen"}}, "gift_cards": {"issued": {"title": "Hier ist Ihre {{ value }} Geschenkkarte für {{ shop }}!", "subtext": "<PERSON><PERSON>e Geschenkkarte", "gift_card_code": "Geschenkkartencode", "shop_link": "<PERSON><PERSON> e<PERSON>", "remaining_html": "<PERSON><PERSON><PERSON><PERSON><PERSON>aben: {{ balance }}", "add_to_droplet_wallet": "Zu Droplet Wallet hinzufügen", "qr_image_alt": "QR-Code — scannen Si<PERSON>, um die Geschenkkarte einzulösen", "copy_code": "Code kopieren", "expired": "Abgelaufen", "copy_code_success": "Code erfolgreich kopiert", "print_gift_card": "<PERSON><PERSON><PERSON>"}}, "recipient": {"form": {"checkbox": "Ich möchte dies als Geschenk senden", "expanded": "Geschenkkartenempfängerformular erweitert", "collapsed": "Geschenkkartenempfängerformular eingeklappt", "email_label": "Empfänger E-Mail", "email_label_optional_for_no_js_behavior": "Empfänger E-Mail (optional)", "email": "E-Mail", "name_label": "Empfänger Name (optional)", "name": "Name", "message_label": "<PERSON><PERSON><PERSON><PERSON> (optional)", "message": "Nachricht", "max_characters": "{{ max_chars }} <PERSON><PERSON><PERSON> maximal", "send_on": "JJJJ-MM-TT", "send_on_label": "Versenden am (optional)"}}}
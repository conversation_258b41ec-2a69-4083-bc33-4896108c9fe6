{{ 'slick-slider.css' | asset_url | stylesheet_tag }}

{% style %}
  .slick-content h2 {
    font-size: {{ section.settings.heading_size }}rem;
    text-transform: {{section.settings.heading_style}};
  }
  
  .slick-caption {
    font-size: {{ section.settings.caption_size }}rem;
    text-transform: {{section.settings.caption_style}};
  }

  .slick-content-link a {
    font-size: {{ section.settings.link_size }}rem;
  }

  .overlay {
    background-color: rgb(var(--color-background), {{section.settings.image_opacity}});
  }

  @media screen and (max-width: 768px) {
    .slick-content h2 {
      font-size: 4rem;
    }
    .slick-caption {
      max-width: 80%;
    }
    .slick-counter {
      font-size: 2.5rem;
    }
    .slick-content a {
        font-size: 1.8rem;
    }
    .slick-content a.button {
        font-size: 1.6rem;
    }
    .slick-caption {
        font-size: 1.6rem;
    }
  }

  @media screen and ( max-width: 768px ) {
    .overlay-mobile {
      background-color: rgb(var(--color-background), {{section.settings.image_opacity}});
    }
  }

  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  @media screen and (max-width: 990px) {
    .margin-spacing-negative.section-{{ section.id }}-margin {
      margin-top: -{{ section.settings.margin_top }}px;
    }
    .margin-spacing-positive.section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
    }
  }
{% endstyle %}

<div class="margin-spacing-{{ section.settings.margin_spacing }} section-{{ section.id }}-margin ignore-{{ section.settings.ignore_spacing }}">
  <div
    class="slick-main {% if section.settings.full_width == false %}page-width{% endif %} section-{{ section.id }}-padding"
    data-enable-autoplay="{{ section.settings.auto_rotate }}"
    data-autoplay-speed="{{ section.settings.slider_interval }}"
    data-aos="fade-up"
    data-aos-delay="100"
  >
    <slick-slider class="color-{{ section.settings.color_scheme }} gradient">
      <ul>
        {%- for block in section.blocks -%}
          <li>
            {%- if block.settings.image -%}
              {%- assign height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round -%}
              <div class="slick-image media" {{ block.shopify_attributes }} {{ forloop.index }}>
                {{
                  block.settings.image
                  | image_url: width: 3840
                  | image_tag:
                    loading: 'lazy',
                    height: height,
                    sizes: '100vw',
                    widths: '750, 1100, 1500, 1780, 2000, 3000, 3840'
                }}
                {% if section.settings.opacity_mobile == false %}
                  <div class="overlay">&nbsp;</div>
                {% else %}
                  <div class="overlay-mobile">&nbsp;</div>
                {% endif %}
              </div>
            {%- else -%}
              <div class="slick-image media">
                {%- assign placeholder_image = 'collection-' | append: forloop.index -%}
                {{ placeholder_image | placeholder_svg_tag: 'placeholder-svg' }}
              </div>
            {%- endif -%}

            <div class="slick-content">
              {%- if block.settings.heading != blank -%}
                <{{ section.settings.heading_tag }}>
                  {{- block.settings.heading | escape -}}
                </{{ section.settings.heading_tag }}>
              {%- endif -%}
              {%- if block.settings.button_label != blank -%}
              <div class="slick-content-link">
                <a class="{% if block.settings.show_link_button %}button-arrow button button--primary{% endif %}" href="{{ block.settings.link }}">{{ block.settings.button_label }}</a>
              </div>
              {% endif %}
              <div class="slick-caption">
                {%- if block.settings.caption != blank -%}
                  {{ block.settings.caption | escape }}
                {%- endif -%}
              </div>
            </div>
          </li>
        {%- endfor -%}
      </ul>
    </slick-slider>
  </div>
</div>

{% schema %}
  {
    "name": "t:sections.slick-slider.name",
    "tag":"section",
    "class": "section",
    "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "t:sections.slick-slider.settings.header.content"
    },
    {
      "type": "checkbox",
      "id": "auto_rotate",
      "label": "t:sections.advanced-slideshow.settings.auto_rotate.label",
      "default": true
    },
    {
      "type": "range",
      "id": "slider_interval",
      "min": 1,
      "max": 10,
      "step": 1,
      "unit": "s",
      "label": "t:sections.advanced-slideshow.settings.slider_interval.label",
      "default": 4
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.advanced-slideshow.settings.full_width.label",
      "default": false
    },
    {
      "type": "range",
      "id": "image_opacity",
      "min": 0,
      "max": 1,
      "step": 0.1,
      "label": "t:sections.slick-slider.settings.image_opacity.label",
      "default": 0.6
    },
    {
      "type": "checkbox",
      "id": "opacity_mobile",
      "label": "t:sections.slick-slider.settings.opacity_mobile.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.advanced-slideshow.settings.text.content"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 3,
      "max": 10,
      "default": 9,
      "step": 0.1,
      "label": "t:sections.advanced-slideshow.settings.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "options": [
      {
        "value": "h1",
        "label": "t:sections.all.heading_tag.options__1.label"
      },
      {
        "value": "h2",
        "label": "t:sections.all.heading_tag.options__2.label"
      },
      {
        "value": "h3",
        "label": "t:sections.all.heading_tag.options__3.label"
      },
      {
        "value": "h4",
        "label": "t:sections.all.heading_tag.options__4.label"
      },
      {
        "value": "h5",
        "label": "t:sections.all.heading_tag.options__5.label"
      },
      {
        "value": "h6",
        "label": "t:sections.all.heading_tag.options__6.label"
      }
      ],
        "default": "h2",
        "label": "t:sections.all.heading_tag.label",
        "info": "t:sections.all.heading_tag.info"
    },
    {
      "type": "select",
      "id": "heading_style",
      "options": [
      {
        "value": "none",
        "label": "t:sections.advanced-slideshow.settings.heading_style.options__1.label"
      },
      {
        "value": "uppercase",
        "label": "t:sections.advanced-slideshow.settings.heading_style.options__2.label"
      },
      {
        "value": "capitalize",
        "label": "t:sections.advanced-slideshow.settings.heading_style.options__3.label"
      }
      ],
        "default": "none",
        "label": "t:sections.advanced-slideshow.settings.heading_size.label"
    },
    {
      "type": "range",
      "id": "caption_size",
      "min": 1,
      "max": 2,
      "default": 1.9,
      "step": 0.1,
      "label": "t:sections.advanced-slideshow.settings.caption_size.label"
    },
    {
    "type": "select",
    "id": "caption_style",
    "options": [
    {
      "value": "none",
      "label": "t:sections.advanced-slideshow.settings.caption_style.options__1.label"
    },
    {
      "value": "uppercase",
      "label": "t:sections.advanced-slideshow.settings.caption_style.options__2.label"
    },
    {
      "value": "capitalize",
      "label": "t:sections.advanced-slideshow.settings.caption_style.options__3.label"
    }
    ],
      "default": "none",
      "label": "t:sections.advanced-slideshow.settings.caption_style.label"
    },
    {
      "type": "range",
      "id": "link_size",
      "min": 1,
      "max": 2,
      "default": 1.8,
      "step": 0.1,
      "label": "t:sections.advanced-slideshow.settings.link_size.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.header_color_box.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "option-1"
    },
    {
      "type": "header",
      "content": "t:sections.slideshow.settings.mobile.content"
    },
    {
      "type": "select",
      "id": "margin_spacing",
      "options": [
        {
          "value": "negative",
          "label": "t:sections.all.margin_spacing.options__1.label"
        },
        {
          "value": "positive",
          "label": "t:sections.all.margin_spacing.options__2.label"
        }
      ],
      "default": "negative",
      "label": "t:sections.all.margin_spacing.label"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.margin_top",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.advanced-slideshow.settings.other.content"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "ignore_spacing",
      "label": "t:sections.all.ignore_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slick slide",
      "limit": 20,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.advanced-slideshow.blocks.slide.settings.image.label"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Image slide heading",
          "label": "t:sections.advanced-slideshow.blocks.slide.settings.heading.label"
        },
        {
          "type": "textarea",
          "id": "caption",
          "default": "Image slide caption",
          "label": "t:sections.advanced-slideshow.blocks.slide.settings.caption.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.advanced-slideshow.blocks.slide.settings.button_label.label",
          "info": "t:sections.advanced-slideshow.blocks.slide.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.advanced-slideshow.blocks.slide.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "show_link_button",
          "label": "t:sections.advanced-slideshow.blocks.slide.settings.show_link_button.label",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.slick-slider.presets.name",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
     ]
    }
  ]
}
{% endschema %}

{"settings_schema": {"global": {"settings": {"header__border": {"content": "<PERSON>rde"}, "header__shadow": {"content": "Sombra"}, "header__exclusions": {"content": "Exclusiones"}, "global_shadow_opacity": {"label": "Opacidad"}, "global_shadow_blur": {"label": "Difuminado"}, "global_border_radius": {"label": "Radio de las esquinas"}, "show_button_arrow": {"label": "Mostrar flecha en botón"}, "button_style": {"options__1": {"label": "Predeterminado"}, "options__2": {"label": "Moderno"}, "options__3": {"label": "Elegante"}, "label": "Estilo <PERSON>"}, "global_shadow_horizontal_offset": {"label": "Desplazamiento horizontal"}, "global_shadow_vertical_offset": {"label": "Desplazamiento vertical"}, "global_border_thickness": {"label": "Espesor del borde"}, "global_border_opacity": {"label": "Opacidad del borde"}, "exclude_drawer": {"label": "Excluir cajon<PERSON> (móvil)"}, "exclude_popup": {"label": "Excluir ventanas emergentes"}, "exclude_inputs": {"label": "Excluir entradas"}, "image_padding": {"label": "<PERSON><PERSON> de la imagen"}, "text_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del texto"}, "card_icons_size": {"options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "label": "Tamaño de los íconos"}}}, "global_design": {"name": "Diseño global"}, "breadcrumbs": {"name": "Migas de pan", "settings": {"show_breadcrumb_nav": {"label": "Mostrar navegación de migas de pan"}, "breadcrumbs_style": {"options__1": {"label": "<PERSON>rde"}, "options__2": {"label": "<PERSON> borde"}, "options__3": {"label": "Fondo"}, "label": "<PERSON><PERSON><PERSON>"}}}, "animations": {"name": "Animaciones", "settings": {"deactivate_animation": {"label": "Desactivar animación al desplazarse"}, "deactivate_menu_animation": {"label": "Desactivar animación del menú de navegación"}, "page_scroll_indicator": {"label": "Activar indicador de desplazamiento de página"}, "scroll_indicator_color": {"label": "Color del indicador de desplazamiento"}, "heading__1": {"content": "Cargador de página"}, "page_loader_enable": {"label": "Activar cargador"}, "loader_background_color": {"label": "Color de fondo del cargador"}, "loader_text_color": {"label": "Color del texto del cargador"}, "page_loader_text": {"label": "Texto del cargador de página"}, "page_loader_style": {"options__1": {"label": "Siempre"}, "options__2": {"label": "Primera vez"}, "label": "El texto del cargador aparece"}}}, "color_swatches": {"name": "Muestras de Color", "settings": {"info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches) para aprender más"}, "swatch_enable": {"label": "Habilitar Muestras de Color"}, "color_map": {"label": "Colores:", "info": "Agrega nombres de colores de variantes y valores de color en este formato: 'Negro: #000000'. Un color por línea. También puedes agregar una imagen usando el identificador de imagen en este formato: 'Natural: natural.png'."}}}, "cards": {"name": "Tarjetas de producto", "settings": {"card_metafield_key": {"label": "Clave del metafield de la tarjeta"}, "enable_tooltip": {"label": "Habilitar tooltip"}}}, "quick_view": {"name": "Vista rápida", "settings": {"quick_view_product_gallery_width": {"label": "<PERSON><PERSON> de la galería de productos"}, "quick_view_height": {"label": "Altura de la vista rápida"}}}, "collection_cards": {"name": "Tarjetas de colección"}, "blog_cards": {"name": "Tarjetas de blog"}, "badges": {"name": "<PERSON><PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Inferior izquierda"}, "options__2": {"label": "Inferior derecha"}, "options__3": {"label": "Superior izquierda"}, "options__4": {"label": "Superior derecha"}, "label": "Posición en las tarjetas"}, "badge_discount": {"label": "Mostrar porcentaje de descuento"}, "header": {"content": "Insignia personalizada"}, "info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/product-card-badges) para aprender más"}, "custom_badge_text": {"label": "Texto de la insignia personalizada"}, "custom_badge_tag": {"label": "Etiqueta de la insignia personalizada", "info": "Asegúrate de usar la misma etiqueta que agregaste en las etiquetas del producto."}, "custom_badge_color": {"label": "Color del texto"}, "custom_badge_background": {"label": "Color de fondo"}}}, "colors": {"name": "Esquemas de Color", "settings": {"background": {"label": "Fondo"}, "background_gradient": {"label": "Degradado de fondo", "info": "El degradado de fondo reemplaza el fondo cuando sea posible."}, "text": {"label": "Texto"}, "button_background": {"label": "Fondo de botón sólido"}, "button_label": {"label": "Etiqueta de botón sólido"}, "secondary_button_label": {"label": "Botón de contorno"}, "color_link": {"label": "Color del enlace"}, "shadow": {"label": "Sombra"}}}, "colors_add": {"name": "Colores Adicionales", "settings": {"heading__1": {"content": "Fondos especiales"}, "background_color_3": {"label": "Fondo de caja de contenido", "info": "Se utiliza como color de fondo para cajas de contenido dentro de secciones."}, "background_color_2": {"label": "Fondo de decoración", "info": "Se utiliza como color de fondo decorativo dentro de diversas secciones."}, "heading__3": {"content": "Colores de acento"}, "accent_background_color_1": {"label": "Color de fondo de acento 1", "info": "Se utiliza como color de fondo de acento primario."}, "accent_color_1": {"label": "Color de texto de acento 1", "info": "Se utiliza como color de texto de acento primario."}, "accent_color_2": {"label": "Color de fondo de acento 2", "info": "Se utiliza como color de fondo de acento secundario."}, "accent_text_color_2": {"label": "Color de texto de acento 2", "info": "Se utiliza como color de texto de acento secundario."}, "heading__4": {"content": "Colores de borde"}, "border_color_1": {"label": "Color de borde", "info": "Se utiliza como color de borde."}, "border_color_2": {"label": "Color de borde 2", "info": "Se utiliza como color de borde secundario."}, "heading__5": {"content": "Botones Especiales"}, "button_quick_add_background_color": {"label": "Fondo del botón de agregar rápido", "info": "Se utiliza para la posición predeterminada (estilo de colección elegante)"}, "button_quick_add_text_color": {"label": "Texto del botón de agregar rápido", "info": "Se utiliza para la posición predeterminada (estilo de colección elegante)"}, "button_quick_add_background_color_hover": {"label": "Fondo del botón de agregar rápido en hover", "info": "Se utiliza para la posición predeterminada (estilo de colección elegante)"}, "button_quick_add_text_color_hover": {"label": "Texto del botón de agregar rápido en hover", "info": "Se utiliza para la posición predeterminada (estilo de colección elegante)"}, "heading__6": {"content": "Otros colores"}, "countdown_background_top": {"label": "Fondo del contador primero"}, "countdown_text_top": {"label": "Texto del contador primero"}, "countdown_background_bottom": {"label": "Fondo del contador segundo"}, "countdown_text_bottom": {"label": "Texto del contador segundo"}, "opacity_color": {"label": "Color de opacidad global"}, "color_link": {"label": "Color del enlace"}}}, "logo": {"name": "Logotipo", "settings": {"logo_image": {"label": "Logotipo"}, "logo_h1": {"label": "Asignar h1 al logo y título"}, "logo_width": {"label": "Ancho del logotipo en escritorio"}, "logo_width_mobile": {"label": "Ancho del logo en móvil"}, "favicon": {"label": "Imagen de favicon", "info": "Se reducirá a 32 x 32 píxeles"}}}, "brand_information": {"name": "Información de la marca", "settings": {"paragraph": {"content": "Agrega una descripción de la marca al pie de página de tu tienda."}, "brand_headline": {"label": "Titular"}, "brand_description": {"label": "Descripción"}, "brand_image": {"label": "Imagen"}, "brand_image_width": {"label": "<PERSON><PERSON>"}}}, "typography": {"name": "Tipografía", "settings": {"type_header_font": {"label": "Fuente", "info": "Seleccionar una fuente diferente puede afectar la velocidad de tu tienda. [Más información sobre fuentes del sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "type_header_weight": {"options__1": {"label": "Predeterminado"}, "options__2": {"label": "Negrita"}, "label": "Peso de la fuente"}, "heading_scale": {"label": "Escala de tamaño de fuente"}, "header__1": {"content": "Encabezados"}, "header__2": {"content": "<PERSON><PERSON><PERSON>"}, "type_body_font": {"label": "Fuente", "info": "Seleccionar una fuente diferente puede afectar la velocidad de tu tienda. [Más información sobre fuentes del sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "body_scale": {"label": "Escala de tamaño de fuente"}, "header__3": {"content": "Menú de navegación"}, "navigation_font": {"options__1": {"label": "Encabezados"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Fuente"}, "text_style": {"options__1": {"label": "Predeterminado"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Estilo de texto"}, "navigation_scale": {"label": "Escala de fuente del menú principal"}, "subnavigation_scale": {"label": "Escala de fuente del submenú"}}}, "buttons": {"name": "Botones"}, "variant_pills": {"name": "Pastillas de variante", "paragraph": "Las pastillas de variante son una forma de mostrar las variantes de tus productos. [Más información](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Campos de entrada"}, "content_containers": {"name": "Contenedores de contenido"}, "popups": {"name": "Desplegables y ventanas emergentes", "paragraph": "Afecta áreas como los desplegables de navegación, las ventanas emergentes modales y los desplegables de carrito."}, "media": {"name": "Medios"}, "drawers": {"name": "<PERSON><PERSON><PERSON>"}, "styles": {"name": "Iconos", "settings": {"accent_icons": {"options__3": {"label": "Botón de contorno"}, "options__4": {"label": "Texto"}, "label": "Color"}}}, "social-media": {"name": "Redes sociales", "settings": {"header": {"content": "Cuentas sociales"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}}}, "search_input": {"name": "Comportamiento de búsqueda", "settings": {"header": {"content": "Sugerencias de búsqueda"}, "predictive_search_enabled": {"label": "Habilitar sugerencias de búsqueda"}, "predictive_search_show_vendor": {"label": "<PERSON>rar proveedor de producto", "info": "Visible cuando las sugerencias de búsqueda están habilitadas."}, "predictive_search_show_price": {"label": "Mostrar precio del producto", "info": "Visible cuando las sugerencias de búsqueda están habilitadas."}}}, "currency_format": {"name": "Formato de moneda", "settings": {"content": "Códigos de moneda", "paragraph": "Los precios en el carrito y el proceso de pago siempre muestran los códigos de moneda. Ejemplo: $1.00 USD.", "currency_code_enabled": {"label": "Mostrar códigos de moneda"}}}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "<PERSON><PERSON><PERSON> de <PERSON>", "drawer": {"label": "Desplegable"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"label": "Notificación emergente"}}, "cart_icon": {"label": "Icono del carrito", "bag": {"label": "Bolsa"}, "cart": {"label": "<PERSON><PERSON>"}}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "show_cart_note": {"label": "Habilitar nota del carrito"}, "cart_note_open": {"label": "Abrir nota del carrito al cargar"}, "header_shipping": {"content": "Mensaje de envío gratuito"}, "enable_free_shipping_message": {"label": "Habilitar mensaje de envío gratuito"}, "free_shipping_message": {"label": "Men<PERSON><PERSON>", "info": "Utilice el marcador de posición *amount* donde deber<PERSON> aparecer el número calculado"}, "free_shipping_success": {"label": "Mensaje de éxito"}, "header_promo": {"content": "Mensaje promocional"}, "enable_promo_message": {"label": "Habilitar mensaje promocional"}, "promo_message": {"label": "Men<PERSON><PERSON>"}, "header_cross_sell": {"content": "<PERSON><PERSON><PERSON> cruzada"}, "enable_cross_sell": {"label": "Habilitar venta cruzada"}, "info": {"content": "Visita la [documentación del tema](https://manathemes.com/docs/flux-theme/flux-theme-settings/cart) para obtener más información"}, "cross_sell_product": {"label": "Colección de venta cruzada"}, "cross_sell_label": {"label": "<PERSON><PERSON><PERSON><PERSON> de venta cruzada"}, "header_terms": {"content": "Términos y condiciones"}, "enable_terms": {"label": "Habilitar requisito de T&C"}, "terms_label": {"label": "Etiqueta"}, "header_empty_cart": {"content": "Carrito vacío"}, "cart_drawer": {"header": "Desplegable del carrito", "collection": {"label": "Colección", "info": "Visible cuando el desplegable del carrito está vacío."}}, "enable_empty_cart_message": {"label": "Habilitar mensaje de carrito vacío"}, "empty_cart_message": {"label": "Mensaje de carrito vacío"}, "button_link": {"label": "Enlace del botón"}, "header_buttons": {"content": "Botones"}, "disable_cart_button": {"label": "Desactivar botón 'Ver carrito'"}, "disable_checkout_button": {"label": "Desactivar botón 'Finalizar compra'"}}}, "layout": {"name": "Diseño", "settings": {"page_width": {"label": "<PERSON><PERSON>"}, "spacing_sections": {"label": "Espacio entre secciones de plantilla"}, "header__grid": {"content": "Cuadrícula"}, "paragraph__grid": {"content": "Afecta áreas con múltiples columnas o filas."}, "spacing_grid_horizontal": {"label": "Espacio horizontal"}, "spacing_grid_vertical": {"label": "Espacio vertical"}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Diseño de escritorio", "padding_top": "Espaciado superior", "padding_bottom": "Espaciado inferior", "padding_left": "<PERSON><PERSON><PERSON>", "padding_right": "<PERSON><PERSON><PERSON> derecho"}, "section_margin_heading": "Diseño mó<PERSON>", "spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "margin_spacing": {"options__1": {"label": "-"}, "options__2": {"label": "+"}, "label": "Margen superior"}, "margin_top": "Valor del margen", "header_color_box": {"content": "Colores"}, "colors_box": {"label": "Esquema de colores del cuadro de contenido"}, "colors": {"option_1": {"label": "Fondo 1 - Texto 1"}, "option_2": {"label": "Fondo 1 - Texto 2"}, "option_3": {"label": "Fondo 2 - Texto 1"}, "option_4": {"label": "Fondo 2 - Texto 2"}, "option_5": {"label": "Fondo de acento 1 - Texto de acento 1"}, "option_6": {"label": "Fondo de acento 2 - Texto de acento 2"}, "label": "Esquema de colores", "info": "Para cambiar el esquema de colores, actualiza tus [ajustes de tema](/editor?context=theme&category=colors).", "has_cards_info": "Para cambiar el esquema de colores, actualiza tus ajustes de tema."}, "text_color": {"option_none": {"label": "Ninguno (como se establece en el esquema de colores)"}, "option_1": {"label": "Color de texto 1"}, "option_2": {"label": "Color de texto 2"}, "option_3": {"label": "Color de acento 1"}, "option_4": {"label": "Color de acento 2"}, "label": "Color de texto", "info": "Aquí puedes cambiar el color de texto independientemente del esquema de colores."}, "heading_style": {"label": "Estilo de en<PERSON>bezado", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "heading_size": {"label": "Tamaño de encabezado", "options__1": {"label": "<PERSON><PERSON> grande"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Pequeño"}}, "text_block_size": {"label": "Tamaño de texto", "options__1": {"label": "Extra grande"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Pequeño"}}, "price_size": {"label": "Tamaño del precio", "options__1": {"label": "Extra grande"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Pequeño"}}, "heading_tag": {"label": "Etiqueta de encabezado", "info": "Especifica tipos de etiquetas de encabezado para SEO y motores de búsqueda con fines de rastreo.", "options__1": {"label": "H1"}, "options__2": {"label": "H2"}, "options__3": {"label": "H3"}, "options__4": {"label": "H4"}, "options__5": {"label": "H5"}, "options__6": {"label": "H6"}}, "text_size": {"label": "Estilo de texto de subtítulo", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "text_style": {"label": "Tamaño del subtítulo", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "ignore_spacing": {"label": "Ignorar espacio entre secciones de plantilla"}, "disable_arrow_mobile": {"label": "Ocultar flechas del deslizador"}, "animate_slider": {"label": "Animar imagen"}, "gradient_position": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}, "label": "Posición de fondo de decoración"}, "countdown-text": {"label": "Texto"}, "countdown-date": {"label": "<PERSON><PERSON>", "info": "Ejemplo de formato: 30 de sep. de 2025"}, "countdown-time": {"label": "<PERSON><PERSON>", "info": "Ejemplo de formato: 9:00"}, "countdown-date-time-style": {"label": "Estilo de cuenta regresiva", "options__1": {"label": "Opción 1"}, "options__2": {"label": "Opción 2"}}, "countdown-text-position": {"label": "Posición del texto", "options__1": {"label": "Arriba"}, "options__2": {"label": "Iz<PERSON>erda"}}, "large-countdown": {"label": "Cuenta regresiva grande"}, "countdown_finished_message": {"label": "Mensaje al finalizar la cuenta regresiva", "info": "Si está vacío, el temporizador se oculta cuando llega a 0"}, "countdown_timer_tag": {"label": "Mostrar solo en productos con etiqueta 'temporizador'"}}, "announcement-bar": {"name": "Barra de información/social", "settings": {"header_layout": {"content": "Diseño"}, "announcement_position": {"label": "Diseño de la barra", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical Izquierda"}, "options__3": {"label": "Vertical Derecha"}}, "sticky": {"content": "Barra Fija", "info": "Si eliges la opción de barra fija, ajusta el relleno superior dentro de la sección del encabezado en consecuencia."}, "enable_announcement_bar_desktop_sticky": {"label": "Fijar en escritorio"}, "enable_announcement_bar_mobile_sticky": {"label": "Fijar en móvil"}, "header_vertical_bar": {"content": "Opciones de la barra vertical"}, "vertical_position": {"label": "Posición"}, "header_top_bar": {"content": "Opciones de la barra superior"}, "text": {"label": "Texto"}, "link": {"label": "Enlace"}, "text_animation": {"label": "Activar animación de texto"}, "show_countdown": {"label": "Mostrar cuenta regresiva", "info": "Activar la cuenta regresiva reemplazará el texto añadido en los bloques de anuncios"}, "countdown": {"content": "Temporizador de cuenta regresiva"}, "show_social_content": {"content": "Redes sociales"}, "show_social_info": {"info": "Para mostrar tus cuentas de redes sociales, vinc<PERSON><PERSON>s en tus [ajustes de tema](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Mostrar iconos de redes sociales"}, "country_selector_content": {"content": "Selector de país/región"}, "country_selector_info": {"info": "Para agregar un país/región, ve a tus [ajustes de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Habilitar selector de país/región"}}, "presets": {"name": "Barra de información/social"}}, "apps": {"name": "Aplicaciones", "settings": {"include_margins": {"label": "Hacer los márgenes de la sección iguales que los del tema"}}, "presets": {"name": "Aplicaciones"}}, "featured-collections": {"name": "Colecciones destacadas", "settings": {"featured_collection_1": {"label": "Moderno"}, "featured_collection_2": {"label": "Elegante"}, "collection_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Elegante"}, "options__2": {"label": "Moderno"}, "options__3": {"label": "<PERSON> imagen"}}, "layout": {"label": "Diseño de escritorio", "options__1": {"label": "Colección primero"}, "options__2": {"label": "Texto primero"}}, "desktop_content_position": {"label": "Posición de contenido en escritorio", "options__1": {"label": "Arriba"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Abajo"}}, "show_text_box": {"label": "Mostrar cuadro de texto"}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptarse a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}, "info": "Agrega imágenes editando tus colecciones. [Aprende más](https://help.shopify.com/manual/products/collections)"}, "desktop_content_alignment": {"label": "Alineación de contenido en escritorio", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}, "desktop_content_overlap": {"label": "Agregar superposición"}, "desktop_collections_alignment": {"label": "Alineación de colecciones en escritorio", "options__1": {"label": "Primera abajo"}, "options__2": {"label": "Igual"}, "options__3": {"label": "Segunda abajo"}}, "full_width": {"label": "<PERSON>cer el diseño a ancho completo"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "layout_mobile": {"label": "Diseño mó<PERSON>", "options__1": {"label": "Texto primero"}, "options__2": {"label": "Colección primero"}}}, "blocks": {"heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "caption": {"name": "Subtítulo", "settings": {"heading": {"label": "Subtítulo"}, "text": {"label": "Texto"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto"}}}, "button": {"name": "Botón", "settings": {"button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "URL del botón"}}}}, "presets": {"name": "Colecciones destacadas"}}, "subcollections": {"name": "Subcolecciones", "settings": {"info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/how-to-set-up-subcollections-on-collection-pages) para aprender más"}, "title": {"label": "Encabezado"}, "caption": {"label": "Subtítulo"}, "enable_desktop_slider": {"label": "Habilitar deslizador de escritorio"}, "collections_to_show": {"label": "Máximo de subcolecciones a mostrar"}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "subcollection_list_style": {"label": "Estilo de la lista de colecciones", "options__1": {"label": "Elegante"}, "options__2": {"label": "Moderno"}, "options__3": {"label": "<PERSON> imagen"}}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptarse a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}, "info": "Agrega imágenes editando tus colecciones. [Aprende más](https://help.shopify.com/manual/products/collections)"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móvil"}}, "presets": {"name": "Subcolecciones"}}, "collection-list": {"name": "Lista de Colecciones", "settings": {"title": {"label": "Encabezado"}, "caption": {"label": "Subtítulo"}, "enable_desktop_slider": {"label": "Habilitar deslizador de escritorio"}, "collections_to_show": {"label": "Máximo de subcolecciones a mostrar"}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "collection_list_style": {"label": "Estilo de lista de colección", "options__1": {"label": "Elegante"}, "options__2": {"label": "Moderno"}, "options__3": {"label": "<PERSON> imagen"}}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptarse a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}, "info": "Agrega imágenes editando tus colecciones. [Aprende más](https://help.shopify.com/manual/products/collections)"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móvil"}}, "blocks": {"featured_collection": {"name": "Colección", "settings": {"collection": {"label": "Colección"}}}}, "presets": {"name": "Lista de Colecciones"}}, "collection-tabs": {"name": "Pestañas de colección", "settings": {"collection_style": {"label": "Estilo de colección", "options__1": {"label": "Moderno"}, "options__2": {"label": "Elegante"}}, "content_alignment": {"label": "Alineación del contenido", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}}}, "blocks": {"collection": {"name": "Colección", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tab_heading": {"label": "Encabezado de la pestaña"}}}}, "presets": {"name": "Pestañas de colección"}}, "two-images-text": {"name": "Imágenes con texto", "settings": {"image": {"label": "Imagen uno"}, "image_2": {"label": "<PERSON><PERSON> dos"}, "layout": {"label": "Diseño de escritorio", "options__1": {"label": "Primero imagen"}, "options__2": {"label": "Primero texto"}}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "layout_mobile": {"label": "Diseño mó<PERSON>", "options__1": {"label": "Primero imagen"}, "options__2": {"label": "Primero texto"}}}, "blocks": {"heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "caption": {"name": "Subtítulo", "settings": {"heading": {"label": "Subtítulo"}, "text": {"label": "Texto"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto"}}}, "image": {"name": "Imagen", "settings": {"image": {"label": "Imagen"}}}, "image_1": {"name": "Superposición de imagen", "settings": {"image_1": {"label": "Imagen"}}}, "button": {"name": "Botón", "settings": {"button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "URL del botón"}}}}, "presets": {"name": "Imágenes con texto"}}, "location-map": {"name": "Mapa de Ubicación", "settings": {"info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/locations-map) para aprender más"}, "title": {"label": "Encabezado"}, "caption": {"label": "Subtítulo"}, "content": {"label": "Texto", "info": "Contenido para el área de texto de la sección."}, "header_contact": {"content": "Formulario de contacto"}, "show_contact_form": {"label": "Mostrar formulario de contacto"}, "hide_phone_field": {"label": "Ocultar campo de teléfono"}, "api_key": {"label": "Clave de API de Google Maps", "info": "Activar las API de Google Maps y Geocoding es necesario para que el mapa funcione."}, "zoom_level": {"label": "<PERSON><PERSON>", "info": "Nivel de zoom para el mapa (1-18)."}, "header": {"content": "Ubicación"}, "address": {"label": "Dirección", "info": "Ingresa la dirección que deseas mostrar en el mapa."}, "marker_content": {"label": "Contenido del Marcador", "info": "Contenido para el mensaje emergente del marcador."}, "layout": {"label": "Diseño", "options__1": {"label": "<PERSON><PERSON> completo"}, "options__2": {"label": "En caja"}, "options__3": {"label": "Contenido a la izquierda, mapa a la derecha"}, "options__4": {"label": "Mapa a la izquierda, contenido a la derecha"}}}, "presets": {"name": "Mapa de Ubicación"}}, "countdown": {"name": "Promoción con Temporizador", "settings": {"image": {"label": "Imagen uno"}, "image_2": {"label": "<PERSON><PERSON> dos"}, "layout": {"label": "Diseño en escritorio", "options__1": {"label": "Imagen primero"}, "options__2": {"label": "Texto primero"}}, "desktop_content_position": {"label": "Posición del contenido en escritorio", "options__1": {"label": "Arriba"}, "options__2": {"label": "Medio"}, "options__3": {"label": "Abajo"}}, "desktop_content_alignment": {"label": "Alineación del contenido en escritorio", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}, "desktop_enable_gradient": {"label": "Habilitar fondo decorativo"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "layout_mobile": {"label": "Diseño mó<PERSON>", "options__1": {"label": "Imagen primero"}, "options__2": {"label": "Texto primero"}}, "mobile_enable_gradient": {"label": "Habilitar fondo decorativo"}}, "blocks": {"heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "caption": {"name": "Subtítulo", "settings": {"heading": {"label": "Subtítulo"}, "text": {"label": "Texto"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto"}}}, "image": {"name": "Imagen", "settings": {"image": {"label": "Imagen"}}}, "countdown-timer": {"name": "Temporizador de Cuenta Regresiva"}, "button": {"name": "Botón", "settings": {"button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "URL del botón"}}}}, "presets": {"name": "Promoción con Temporizador"}}, "image-gallery": {"name": "Galería de imágenes", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"label": "Subtítulo"}, "scroll_height_mobile": {"label": "Altura de desplazamiento"}}, "blocks": {"text": {"name": "Imagen"}}, "presets": {"name": "Galería de imágenes"}}, "contact-form": {"name": "Formulario de Contacto", "settings": {"contact_style": {"label": "Diseño", "options__1": {"label": "Columna"}, "options__2": {"label": "<PERSON><PERSON>"}}, "image_height": {"label": "Altura del banner", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Medio"}, "options__4": {"label": "Grande"}}, "image_height_mobile": {"label": "Altura del banner", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Medio"}, "options__4": {"label": "Grande"}}, "caption": {"label": "Subtítulo"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "button_label_1": {"label": "Etiqueta del botón"}, "button_link_1": {"label": "Enlace del botón"}, "text": {"label": "Información de la Barra Lateral"}, "header_mobile": {"content": "Diseño en Dispositivos Móviles"}, "desktop_enable_gradient": {"label": "Habilitar Fondo Decorativo"}, "mobile_enable_gradient": {"label": "Habilitar Fondo Decorativo"}}, "presets": {"name": "Formulario de Contacto"}}, "custom-liquid": {"name": "Código Liquid Personalizado", "settings": {"custom_liquid": {"label": "Código Liquid Personalizado", "info": "Agrega fragmentos de código de aplicaciones u otro código Liquid para crear personalizaciones avanzadas."}}, "presets": {"name": "Código Liquid Personalizado"}}, "featured-blog": {"name": "Entradas Destacadas del Blog", "settings": {"caption": {"label": "Pie de Foto"}, "heading": {"label": "Encabezado"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Número de entradas de blog para mostrar"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "show_view_all": {"label": "Habilitar bot<PERSON> \"Ver todo\" si el blog incluye más entradas de blog de las que se muestran"}, "show_image": {"label": "Mostrar imagen destacada"}, "show_date": {"label": "<PERSON>rar fecha"}, "show_author": {"label": "Mostrar autor"}, "show_excerpt": {"label": "Mostrar extracto"}, "blog_style": {"label": "Estilo del blog", "options__1": {"label": "Moderno"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Elegante"}, "options__4": {"label": "Portada"}}, "header_mobile": {"content": "<PERSON><PERSON><PERSON>"}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en dispositivos móviles"}}, "presets": {"name": "Entradas Destacadas del Blog"}}, "featured-collection": {"name": "Colección Destacada", "settings": {"caption": {"label": "Pie de Foto"}, "title": {"label": "Encabezado"}, "description": {"label": "Descripción"}, "show_description": {"label": "Mostrar descripción de la colección desde el administrador"}, "description_style": {"label": "Estilo de la descripción", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "collection": {"label": "Colección"}, "collection_style": {"label": "Estilo de colección", "options__1": {"label": "Moderno"}, "options__3": {"label": "Elegante"}}, "products_to_show": {"label": "Máximo de productos a mostrar"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "show_view_all": {"label": "Habilitar \"Ver todo\" si la colección tiene más productos de los que se muestran"}, "view_all_style": {"label": "<PERSON><PERSON><PERSON> de \"Ver todo\"", "options__1": {"label": "Enlace"}, "options__2": {"label": "Botón"}}, "enable_desktop_slider": {"label": "Habilitar carrusel en el escritorio"}, "full_width": {"label": "<PERSON><PERSON> productos de ancho completo"}, "header": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Proporción de la imagen", "options__1": {"label": "Adaptarse a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrada"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el mouse"}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "show_rating": {"label": "Mostrar calificación del producto", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos."}, "enable_quick_buy": {"label": "Habilitar vista rápida", "info": "Óptimo con el tipo de carrito emergente o deslizable."}, "quick_add_position": {"label": "Posición de compra rápida", "options__1": {"label": "Superpuesta"}, "options__2": {"label": "Predeterminada"}}, "header_overlap": {"content": "Superposición", "info": "Puede colocar la colección destacada encima de la sección anterior con un efecto de superposición. Esto ocultará el encabezado y la descripción para lograr una apariencia más limpia."}, "enable_overlap": {"label": "Activar superposición"}, "margin_top": {"label": "Superposición superior"}, "mobile_margin_top": {"label": "Superposición en dispositivos móviles"}, "header_mobile": {"content": "<PERSON><PERSON><PERSON>"}, "columns_mobile": {"label": "Número de columnas en dispositivos móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}, "disable_quick_add": {"label": "Deshabilitar compra rápida en dispositivos móviles cuando está habilitada en el escritorio"}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en dispositivos móviles"}, "header_banner_height": {"content": "Altura del banner"}, "banner_height_desktop": {"label": "Altura de escritorio"}, "banner_height_mobile": {"label": "Altura móvil"}}, "presets": {"name": "Colección Destacada"}}, "featured-product": {"name": "Producto destacado", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Predeterminado"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "vendor": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "product-meta": {"name": "Inventario y calificación"}, "price": {"name": "Precio"}, "quantity_selector": {"name": "Selector de cantidad"}, "countdown-timer": {"name": "Temporizador de cuenta regresiva"}, "variant_picker": {"name": "Selector de variantes", "settings": {"picker_type": {"label": "Tipo", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Pastillas"}, "options__3": {"label": "Simple"}}}}, "buy_buttons": {"name": "Botones de compra", "settings": {"show_dynamic_checkout": {"label": "Mostrar botones de pago dinámico", "info": "Utilizando los métodos de pago disponibles en tu tienda, los clientes verán su opción preferida, como PayPal. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "variant": {"content": "Selector de variantes"}, "swatch_shape": {"label": "Estilo <PERSON>", "info": "Consulte la documentación [Más información](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches)", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cuadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "description": {"name": "Descripción"}, "share": {"name": "Compartir", "settings": {"text": {"label": "Texto"}, "featured_image_info": {"content": "Si incluyes un enlace en las publicaciones de redes sociales, la imagen destacada de la página se mostrará como la imagen de vista previa. [Más información](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Un título y descripción de la tienda se incluyen con la imagen de vista previa. [Más información](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}}}, "custom_liquid": {"name": "Líquido personalizado", "settings": {"custom_liquid": {"label": "Líquido personalizado"}}}, "rating": {"name": "Calificación del producto", "settings": {"paragraph": {"content": "Para mostrar una calificación, agrega una aplicación de calificación de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}}, "settings": {"product": {"label": "Producto"}, "product_background_color": {"label": "Color de fondo"}, "secondary_background": {"label": "Mostrar fondo secundario"}, "header": {"content": "Medios", "info": "Más información sobre [tipos de medios](https://help.shopify.com/manual/products/product-media)"}, "media_position": {"label": "Posición de los medios en el escritorio", "info": "La posición se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}}, "hide_variants": {"label": "Ocultar medios de variantes no seleccionadas en el escritorio"}, "enable_video_looping": {"label": "Habilitar bucle de video"}, "desktop_enable_gradient": {"label": "Habilitar fondo de decoración"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "mobile_enable_gradient": {"label": "Habilitar fondo de decoración"}}, "presets": {"name": "Producto destacado"}}, "footer": {"name": "Pie de página", "blocks": {"link_list": {"name": "Menú", "settings": {"heading": {"label": "Encabezado"}, "menu": {"label": "Menú", "info": "Muestra solo elementos del menú de nivel superior."}}}, "brand_information": {"name": "Información de la marca", "settings": {"brand_headline": {"label": "Titular"}, "brand_description": {"label": "Descripción"}, "brand_image": {"label": "Imagen"}, "brand_image_width": {"label": "<PERSON><PERSON>"}, "header__2": {"content": "Boletín"}, "newsletter_enable": {"label": "Mostrar registro por correo electrónico"}, "header__1": {"content": "Iconos de redes sociales"}, "show_social": {"label": "Mostrar iconos de redes sociales", "info": "Para mostrar tus cuentas de redes sociales, enlázalas en tu [configuración de tema](/editor?context=theme&category=social%20media)."}, "social_icons_size": {"options__1": {"label": "Extra pequeño"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Grande"}, "label": "Tamaño de los iconos sociales"}}}, "text": {"name": "Texto", "settings": {"heading": {"label": "Encabezado"}, "subtext": {"label": "Subtexto"}}}, "image": {"name": "Imagen", "settings": {"image": {"label": "Imagen"}, "image_width": {"label": "<PERSON><PERSON>"}, "image_headline": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_description": {"label": "Descripción"}, "button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "Enlace del botón"}, "button_style_secondary_1": {"label": "Usar estilo de botón contorneado"}, "show_text_under": {"label": "Mostrar contenido debajo"}}}}, "settings": {"newsletter_enable": {"label": "Mostrar suscripción por correo electrónico"}, "newsletter_heading": {"label": "Encabezado"}, "header__1": {"content": "Registro por correo electrónico", "info": "Los suscriptores se agregan automáticamente a tu lista de clientes de 'marketing aceptado'. [Más información](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Iconos de redes sociales", "info": "Para mostrar tus cuentas de redes sociales, enlázalas en tus [configuraciones de tema](/editor?context=theme&category=social%20media)."}, "socials_heading": {"label": "Encabezado"}, "show_social": {"label": "Mostrar iconos de redes sociales"}, "header__3": {"content": "Selector de país/región"}, "header__4": {"info": "Para agregar un país/región, ve a tus [configuraciones de mercado](/admin/settings/markets)"}, "enable_country_selector": {"label": "Habilitar selector de país/región"}, "header__5": {"content": "Selector de idioma"}, "header__6": {"info": "Para agregar un idioma, ve a tus [configuraciones de idioma](/admin/settings/languages)"}, "enable_language_selector": {"label": "Habilitar selector de idioma"}, "header__10": {"content": "Desarrollado por Shopify"}, "show_powered_by_link": {"label": "Mostrar desarrollado por Shopify"}, "header__7": {"content": "Métodos de pago"}, "payment_enable": {"label": "Mostrar iconos de pago"}, "header__8": {"content": "Enlaces de políticas", "info": "Para agregar políticas de tienda, ve a tus [configuraciones de políticas](/admin/settings/legal)."}, "show_policy": {"label": "Mostrar enlaces de políticas"}, "margin_top": {"label": "Margen superior de escritorio/móvil"}, "margin_bottom": {"label": "Margen inferior móvil"}, "centered_content": {"label": "Centrar contenido en móvil"}, "header__9": {"content": "<PERSON><PERSON><PERSON> en la tienda", "info": "Muestra el botón de seguir para tu tienda en la aplicación de la tienda. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Habilitar seguir en la tienda"}, "header_back_to_top_desktop": {"content": "Botón de volver al principio en la versión de escritorio"}, "back_to_top_desktop": {"label": "Habilitar botón de volver al principio"}, "back_to_top_bottom": {"label": "Posición vertical"}, "back_to_top_right": {"label": "Posición horizontal"}, "header_back_to_top_mobile": {"content": "Botón de volver al principio en dispositivos móviles"}, "back_to_top_mobile": {"label": "Habilitar botón de volver al principio"}, "footer_border": {"label": "Habilitar el borde superior del pie de página"}, "make_columns_even": {"label": "Hacer que las columnas sean iguales"}}}, "header": {"name": "Encabezado", "blocks": {"mega_promotion": {"name": "Mega promoción", "settings": {"info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/mega-menu-promotions) para aprender más"}, "mega_promotion_item": {"label": "Elemento del menú", "info": "Ingresa el nombre del elemento del mega menú al que deseas agregar una tarjeta de promoción."}, "mega_promotion_image": {"label": "Imagen"}, "mega_promotion_caption": {"label": "Leyenda"}, "mega_promotion_title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "mega_promotion_link_label": {"label": "Etiqueta del enlace"}, "mega_promotion_link": {"label": "URL del enlace"}}}, "mega_image_menu": {"name": "Menú de imagen mega", "settings": {"heading_1": {"content": "Elemento 1"}, "heading_2": {"content": "Elemento 2"}, "heading_3": {"content": "Elemento 3"}, "heading_4": {"content": "Elemento 4"}, "heading_5": {"content": "Elemento 5"}, "heading_6": {"content": "Elemento 6"}, "heading_7": {"content": "Elemento  7"}, "heading_8": {"content": "Elemento  8"}}}}, "settings": {"header_layout": {"content": "Diseño del encabezado"}, "header_additional_links": {"content": "Enlaces adicionales"}, "button_label": {"label": "Etiqueta de enlace 1"}, "button_link": {"label": "Enlace 1"}, "button_label_one": {"label": "Etiqueta de enlace 2"}, "button_link_one": {"label": "Enlace 2"}, "disable_additional_links": {"label": "Habilitar enlaces adicionales"}, "logo_help": {"content": "Edita tu logotipo en [configuraciones del tema](/editor?context=theme&category=logo)."}, "desktop_header_layout": {"label": "Diseño de encabezado de escritorio", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Logo-Menú-Iconos"}, "options__3": {"label": "Menú-Logo-Iconos"}, "options__4": {"label": "Logo-Centro-Menú-Iconos"}}, "show_search_icon": {"label": "Estilo alternativo", "info": "Esta configuración se aplica solo cuando se selecciona 'Dos filas'. Permite elegir un estilo alternativo para este diseño."}, "menu": {"label": "Menú"}, "featured_collection_1": {"label": "Colección 1"}, "featured_collection_2": {"label": "Colección 2"}, "menu_type_desktop": {"label": "Tipo de menú de escritorio", "info": "El tipo de menú se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Mega menú"}, "options__3": {"label": "Cajón"}}, "all_items_mega": {"label": "Aplicar mega menú a todos los elementos del menú", "info": "Si se selecciona el mega menú. <PERSON><PERSON> defecto, solo los elementos del menú con 3 niveles se convierten en mega menú"}, "submenu_animation_position": {"label": "Animación del mega menú", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "De abajo hacia arriba"}, "options__3": {"label": "De izquierda a derecha"}}, "sticky_header_type": {"label": "Encabezado fijo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Al hacer scroll hacia arriba"}, "options__3": {"label": "Siempre"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>, reducir tamaño del logotipo"}}, "country_selector_content": {"content": "Selector de país/región"}, "country_selector_info": {"info": "Para agregar un país/región, ve a tus [ajustes de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Habilitar selector de país/región"}, "enable_fixed_header_type": {"label": "Encabezado alternativo en la página de inicio"}, "enable_fixed_header_type_collection": {"label": "Encabezado alternativo en la página de colección"}, "enable_fixed_header_type_all": {"label": "Encabezado alternativo en todas las páginas"}, "enable_fixed_header_transparent": {"label": "Hacer transparente el encabezado alternativo"}, "enable_header_full_width": {"label": "Habilitar encabezado a toda la anchura"}, "fixed_header_type_margin": {"label": "Margen superior de escritorio"}, "fixed_header_type_margin_mobile": {"label": "Margen superior móvil"}, "header_transparent": {"content": "Encabezado alternativo"}, "transparent_menu": {"label": "Páginas personalizadas", "info": "Elige un menú vinculado a las páginas donde deseas aplicar la cabecera alternativa."}, "header_sticky": {"content": "Encabezado fijo"}, "header_highlight": {"content": "Resaltar elemento de menú"}, "enable_item_highlight": {"label": "Habilitar resaltado de elemento del menú"}, "item_highlight_text": {"label": "Texto de resaltado de elemento del menú"}, "item_highlight_position": {"label": "Posición de resaltado de elemento del menú", "options__1": {"label": "Primer elemento del menú"}, "options__2": {"label": "<PERSON><PERSON><PERSON> del <PERSON>"}, "options__3": {"label": "Tercer elemento del menú"}, "options__4": {"label": "Cuarto elemento del menú"}, "options__5": {"label": "<PERSON><PERSON>to elemento del menú"}, "options__6": {"label": "Sexto elemento del menú"}, "options__7": {"label": "Séptimo elemento del <PERSON>"}, "options__8": {"label": "Octavo elemento del menú"}, "options__9": {"label": "Noveno elemento del menú"}, "options__10": {"label": "Décimo elemento del menú"}}, "adjust_item_highlight_position": {"label": "Ajustar la posición del resaltado del elemento"}, "item_highlight_background_color": {"label": "Color de fondo de resaltado"}, "item_highlight_color": {"label": "Color de texto de resaltado"}, "margin_bottom": {"label": "Margen inferior"}, "header_icons_deco": {"content": "Decoración de iconos del encabezado"}, "header_icons_decoration": {"label": "Decoración de iconos", "options__1": {"label": "Ninguna"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Lín<PERSON>"}}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "mobile_desktop_header_layout": {"label": "Diseño del encabezado móvil", "options__1": {"label": "Centrado"}, "options__2": {"label": "Iz<PERSON>erda"}}}}, "image-banner": {"name": "<PERSON> de imagen", "settings": {"image": {"label": "Imagen"}, "image_overlay_opacity": {"label": "Opacidad de la superposición de la imagen", "info": "Puedes cambiar el color de opacidad globalmente desde las [configuraciones del tema](/editor?context=theme&category=colors)."}, "image_height": {"label": "Altura del banner", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba al centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centro a la izquierda"}, "options__5": {"label": "Centro al centro"}, "options__6": {"label": "Centro a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo al centro"}, "options__9": {"label": "Abajo a la derecha"}, "label": "Posición del contenido en escritorio"}, "show_text_box": {"label": "Mostrar cuadro de texto en escritorio"}, "box_padding_top": {"label": "Relleno superior del cuadro de texto"}, "box_padding_bottom": {"label": "Relleno inferior del cuadro de texto"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en escritorio"}, "show_image_circle": {"label": "Ocultar círculo de imagen"}, "ignore_image_circle_animation": {"label": "Desactivar animación del círculo de imagen"}, "color_scheme": {"info": "Visible cuando se muestra el contenedor."}, "header": {"content": "Diseño mó<PERSON>"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido móvil"}, "stack_images_on_mobile": {"label": "A<PERSON><PERSON> imágenes en móvil"}, "show_text_below": {"label": "Mostrar contenedor en móvil"}, "adapt_height_first_image": {"label": "Adaptar altura de la sección al tamaño de la primera imagen", "info": "Sobrescribe la configuración de altura del banner de imagen cuando esté marcado."}}, "blocks": {"image": {"name": "Imagen", "settings": {"image": {"label": "Imagen"}}}, "heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado", "info": "Pon en cursiva una palabra importante en tu encabezado para destacarla y luego elige una opción de resaltado de las siguientes."}, "word_animation_color": {"label": "Color de resaltado"}, "highlight_option": {"options__1": {"label": "Cursiva"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Opción de resaltado"}}}, "caption": {"name": "Leyenda", "settings": {"heading": {"label": "Leyenda"}, "text": {"label": "Texto"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descripción"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Estilo de texto"}}}, "countdown-timer": {"name": "Temporizador de cuenta regresiva", "settings": {"countdown_small": {"label": "<PERSON>cer el temporizador pequeño"}}}, "buttons": {"name": "Botones", "settings": {"button_label_1": {"label": "Etiqueta del primer botón", "info": "Deja en blanco la etiqueta para ocultar el botón."}, "button_link_1": {"label": "Enlace del primer botón"}, "button_style_secondary_1": {"label": "Usar estilo de botón de contorno"}, "button_label_2": {"label": "Etiqueta del segundo botón", "info": "Deja en blanco la etiqueta para ocultar el botón."}, "button_link_2": {"label": "Enlace del segundo botón"}, "button_style_secondary_2": {"label": "Usar estilo de botón de contorno"}}}}, "presets": {"name": "<PERSON> de imagen"}}, "image-banner-with-featured-collection": {"name": "Banner de imagen con colección destacada", "settings": {"image": {"label": "Imagen"}, "hide_image": {"label": "Usar fondo de color sólido"}, "image_overlay_opacity": {"label": "Opacidad de superposición de imagen", "info": "Puede cambiar el color de opacidad globalmente desde la [configuración del tema](/editor?context=theme&category=colors)."}, "content_text_color": {"label": "Color del texto del contenido"}, "content_button_background_color": {"label": "Color de fondo del contenido"}, "content_button_text_color": {"label": "Color del texto del contenido"}, "image_height": {"label": "Altura del banner"}, "image_height_mobile": {"label": "Altura del banner"}, "full_width_banner": {"label": "<PERSON>cer el diseño a ancho completo"}, "desktop_content_position": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Posición del contenido en el escritorio"}, "show_text_box": {"label": "Mostrar cuadro de texto en el escritorio"}, "box_padding_top": {"label": "Relleno superior del cuadro de texto"}, "box_padding_bottom": {"label": "Relleno inferior del cuadro de texto"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en el escritorio"}, "header_mobile_image_banner": {"content": "Diseño móvil - Banner de imagen"}, "image_mobile": {"label": "Imagen"}, "image_overlay_opacity_mobile": {"label": "Opacidad de superposición de imagen", "info": "Puedes cambiar la opacidad del color globalmente desde la [configuración del tema](/editor?context=theme&category=colors)."}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido móvil"}, "stack_images_on_mobile": {"label": "Apilar imágenes en móviles"}, "show_text_below": {"label": "Mostrar contenedor en móviles"}, "adapt_height_first_image": {"label": "Adaptar la altura de la sección al tamaño de la primera imagen", "info": "Sobrescribe la configuración de altura del banner de imagen cuando está marcada."}, "header_featured_collection": {"content": "Colección destacada"}, "enable_collection": {"label": "Habilitar colección destacada"}, "collection": {"label": "Colección"}, "products_to_show": {"label": "Número máximo de productos a mostrar"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "show_view_all": {"label": "Habilitar \"Ver todo\" si la colección tiene más productos de los mostrados"}, "view_all_style": {"label": "<PERSON><PERSON><PERSON> de \"Ver todo\"", "options__1": {"label": "Enlace"}, "options__2": {"label": "Botón"}}, "enable_desktop_slider": {"label": "Habilitar carrusel en el escritorio"}, "full_width": {"label": "<PERSON><PERSON> productos de ancho completo"}, "header": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el ratón"}, "show_vendor": {"label": "<PERSON><PERSON> vendedor"}, "show_rating": {"label": "Mostrar calificación del producto", "info": "Para mostrar una calificación, agregue una aplicación de calificación de producto."}, "enable_quick_buy": {"label": "Habilitar vista rápida", "info": "Óptimo con tipo de carrito emergente o de cajón."}, "quick_add_position": {"label": "Posición de agregar rápido", "options__1": {"label": "Superposición"}, "options__2": {"label": "Predeterminado"}}, "header_overlap": {"content": "Superposición"}, "desktop_margin_top": {"label": "Superposición en el escritorio"}, "mobile_margin_top": {"label": "Superposición en móviles"}, "header_mobile_featured_collection": {"content": "Diseño móvil - Colección destacada"}, "columns_mobile": {"label": "Número de columnas en móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}, "disable_quick_add": {"label": "Deshabilitar agregar rápido en móviles cuando está habilitado en el escritorio"}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móviles"}}, "blocks": {"heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "caption": {"name": "Subencabezado", "settings": {"heading": {"label": "Subencabezado"}, "text": {"label": "Texto"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descripción"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Estilo de texto"}}}, "buttons": {"name": "Botones", "settings": {"button_label_1": {"label": "Etiqueta del primer botón", "info": "Deje en blanco la etiqueta para ocultar el botón."}, "button_link_1": {"label": "Enlace del primer botón"}}}}, "presets": {"name": "Banner de imagen con colección destacada"}}, "image-banner-with-collections": {"name": "Banner de imagen con lista de colecciones", "settings": {"image": {"label": "Imagen"}, "hide_image": {"label": "Usar fondo de color sólido"}, "image_overlay_opacity": {"label": "Opacidad de la superposición de imagen", "info": "Puedes cambiar el color de opacidad globalmente desde los [ajustes del tema](/editor?context=theme&category=colors)."}, "content_text_color": {"label": "Color del texto del contenido"}, "content_button_background_color": {"label": "Color de fondo del contenido"}, "content_button_text_color": {"label": "Color del texto del contenido"}, "image_height": {"label": "Altura del banner"}, "image_height_mobile": {"label": "Altura del banner"}, "desktop_content_position": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Posición del contenido en escritorio"}, "show_text_box": {"label": "Mostrar cuadro de texto en escritorio"}, "box_padding_top": {"label": "Espaciado superior del cuadro de texto"}, "box_padding_bottom": {"label": "Espaciado inferior del cuadro de texto"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en escritorio"}, "header_mobile_image_banner": {"content": "Diseño mó<PERSON>"}, "image_mobile": {"label": "Imagen"}, "image_overlay_opacity_mobile": {"label": "Opacidad de la superposición de imagen", "info": "Puedes cambiar el color de opacidad globalmente desde los [ajustes del tema](/editor?context=theme&category=colors)."}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en móvil"}, "stack_images_on_mobile": {"label": "A<PERSON><PERSON> imágenes en móvil"}, "show_text_below": {"label": "Mostrar contenedor en móvil"}, "adapt_height_first_image": {"label": "Adaptar altura de la sección al tamaño de la primera imagen", "info": "Sobrescribe la configuración de altura del banner cuando está activado."}, "header_featured_collection": {"content": "Colección destacada"}, "enable_collection": {"label": "Activar colección destacada"}, "collection": {"label": "Colección"}, "products_to_show": {"label": "Máximo de productos a mostrar"}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "show_view_all": {"label": "Activar \"Ver todo\" si la colección tiene más productos de los mostrados"}, "view_all_style": {"label": "<PERSON><PERSON><PERSON> de \"Ver todo\"", "options__1": {"label": "Enlace"}, "options__2": {"label": "Botón"}}, "enable_desktop_slider": {"label": "Activar carrusel en escritorio"}, "full_width": {"label": "<PERSON>cer productos a ancho completo"}, "header": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "show_rating": {"label": "Mostrar calificación del producto", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos."}, "enable_quick_buy": {"label": "Activar vista rápida", "info": "Óptimo con tipo de carrito emergente o en barra lateral."}, "quick_add_position": {"label": "Posición de la vista rápida", "options__1": {"label": "Superposición"}, "options__2": {"label": "Predeterminado"}}, "header_overlap": {"content": "Superposición"}, "desktop_margin_top": {"label": "Superposición en escritorio"}, "mobile_margin_top": {"label": "Superposición en móvil"}, "header_mobile_featured_collection": {"content": "Diseño móvil - Colección destacada"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}, "disable_quick_add": {"label": "Desactivar compra rápida en móvil cuando está activada en escritorio"}, "swipe_on_mobile": {"label": "Activar deslizamiento en móvil"}, "heading": {"label": "Encabezado", "info": "Pon en cursiva una palabra importante en tu encabezado para destacarla y luego elige una opción de resaltado de las siguientes."}, "caption": {"label": "Subtítulo"}, "text": {"label": "Descripción"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Estilo del texto"}, "button_label_1": {"label": "Etiqueta del primer botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link_1": {"label": "Enlace del primer botón"}, "highlight_option": {"options__1": {"label": "Cursiva"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Opción de resaltado"}, "word_animation_color": {"label": "Color de resaltado"}, "header_collections": {"content": "Colecciones"}, "disable_arrow_mobile": {"label": "Ocultar flechas del slider en móvil"}}, "blocks": {"featured_collection": {"name": "Colección", "settings": {"collection": {"label": "Colección"}}}}, "presets": {"name": "Banner de imagen con lista de colecciones"}}, "banner-two-columns": {"name": "Banners de columnas", "settings": {"banner_layout": {"label": "Diseño", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Adicional"}}, "swipe_on_mobile": {"label": "Activar deslizamiento en dispositivos móviles"}, "full_width": {"label": "<PERSON><PERSON> dise<PERSON> de ancho completo"}, "accessibility": {"content": "Accesibilidad", "label": "Descripción de la presentación de diapositivas", "info": "Describe la presentación de diapositivas para los clientes que utilizan lectores de pantalla."}}, "blocks": {"slide": {"name": "Banner", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Leyenda"}, "heading": {"label": "Encabezado"}, "subheading": {"label": "Texto"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}, "box_align": {"label": "Posición del contenido en escritorio", "info": "La posición se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba al centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centro a la izquierda"}, "options__5": {"label": "Centro al centro"}, "options__6": {"label": "Centro a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo al centro"}, "options__9": {"label": "Abajo a la derecha"}}, "show_text_box": {"label": "Mostrar cuadro de texto en escritorio"}, "text_alignment": {"label": "Alineación del contenido en escritorio", "option_1": {"label": "Iz<PERSON>erda"}, "option_2": {"label": "Centro"}, "option_3": {"label": "Derecha"}}, "image_overlay_opacity": {"label": "Opacidad de la superposición de la imagen"}, "color_scheme": {"info": "Visible cuando se muestra el contenedor."}, "text_alignment_mobile": {"label": "Alineación del contenido en móvil", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}}}}, "presets": {"name": "Banners de columnas"}}, "sticky-banners": {"name": "Funky banners", "settings": {"banner_height": {"label": "Altura de la imagen", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Grande"}}, "swipe_on_mobile": {"label": "Activar deslizamiento en dispositivos móviles"}, "full_width": {"label": "<PERSON><PERSON> dise<PERSON> de ancho completo"}, "accessibility": {"content": "Accesibilidad", "label": "Descripción de la presentación de diapositivas", "info": "Describe la presentación de diapositivas para los clientes que utilizan lectores de pantalla."}}, "blocks": {"slide": {"name": "Banner", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Leyenda"}, "heading": {"label": "Encabezado"}, "subheading": {"label": "Texto"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}, "box_align": {"label": "Posición del contenido en escritorio", "info": "La posición se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba al centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centro a la izquierda"}, "options__5": {"label": "Centro al centro"}, "options__6": {"label": "Centro a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo al centro"}, "options__9": {"label": "Abajo a la derecha"}}, "show_text_box": {"label": "Mostrar cuadro de texto en escritorio"}, "text_alignment": {"label": "Alineación del contenido en escritorio", "option_1": {"label": "Iz<PERSON>erda"}, "option_2": {"label": "Centro"}, "option_3": {"label": "Derecha"}}, "image_overlay_opacity": {"label": "Opacidad de la superposición de la imagen"}, "color_scheme": {"info": "Visible cuando se muestra el contenedor."}, "text_alignment_mobile": {"label": "Alineación del contenido en móvil", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}}}}, "presets": {"name": "Sticky banners"}}, "image-with-text": {"name": "Imagen con texto", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Leyenda vertical"}, "height": {"options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Grande"}, "label": "Altura de la imagen"}, "desktop_image_width": {"options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON> de <PERSON>n en escritorio", "info": "La imagen se optimiza automáticamente para dispositivos móviles."}, "layout": {"options__1": {"label": "Imagen primero"}, "options__2": {"label": "<PERSON><PERSON> segundo"}, "label": "Ubicación de la imagen en escritorio", "info": "La ubicación de la imagen primero es la disposición móvil predeterminada."}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en escritorio"}, "desktop_content_position": {"options__1": {"label": "Arriba"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Abajo"}, "label": "Posición del contenido en escritorio"}, "content_layout": {"options__1": {"label": "Sin superposición"}, "options__2": {"label": "Superposición"}, "label": "Diseño de contenido"}, "desktop_enable_gradient": {"label": "Habilitar fondo de decoración"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en móvil"}, "mobile_enable_gradient": {"label": "Habilitar fondo de decoración"}, "full_width": {"label": "Hacer el diseño en caja"}}, "blocks": {"heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "caption": {"name": "Leyenda", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo del texto", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Tamaño del texto", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Contenido"}, "text_style": {"label": "Estilo del texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}}}}, "image": {"name": "Imagen", "settings": {"image": {"label": "Imagen", "info": "Para una apariencia óptima, asegúrese de que las dimensiones de la imagen sean de 300x300 píxeles."}, "mobile_disable_image": {"label": "Desactivar imagen en dispositivos móviles"}}}, "button": {"name": "Botón", "settings": {"button_label": {"label": "Etiqueta del botón", "info": "Deja en blanco la etiqueta para ocultar el botón."}, "button_link": {"label": "Enlace del botón"}}}}, "presets": {"name": "Imagen con texto"}}, "image-hotspots": {"name": "Puntos calientes de imagen", "settings": {"image": {"label": "Imagen"}, "image_size": {"options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "label": "Tam<PERSON><PERSON> de la imagen"}, "heading": {"label": "Encabezado"}, "caption": {"label": "Subtítulo"}, "text_style": {"label": "Estilo de texto del subtítulo", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "text_size": {"label": "Tamaño de texto del subtítulo", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "product": {"label": "Producto"}, "layout": {"options__1": {"label": "Imagen primero"}, "options__2": {"label": "Texto primero"}, "label": "Ubicación de la imagen"}, "layout_mobile": {"options__1": {"label": "Imagen primero"}, "options__2": {"label": "Texto primero"}, "label": "Ubicación de la imagen"}, "header_colors": {"content": "Colores"}, "tooltip_background_color": {"label": "Color de fondo del tooltip"}, "full_width": {"label": "<PERSON><PERSON> diseño a toda la anchura"}, "hide_content": {"label": "Ocultar contenido"}}, "blocks": {"tooltip": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "link": {"label": "Enlace del producto"}, "product": {"label": "Producto"}, "content": {"label": "Contenido"}, "top": {"label": "Posición superior"}, "left": {"label": "Posición izquierda"}}}}, "presets": {"name": "Puntos calientes de imagen"}}, "quick-info-bar": {"name": "Barra de información rápida", "settings": {"first_column_content": {"content": "Contenido de la primera columna"}, "image_1": {"label": "Imagen"}, "heading_1": {"label": "Encabezado"}, "caption_1": {"label": "Leyenda"}, "second_column_content": {"content": "Contenido de la segunda columna"}, "image_2": {"label": "Imagen"}, "heading_2": {"label": "Encabezado"}, "caption_2": {"label": "Leyenda"}, "third_column_content": {"content": "Contenido de la tercera columna"}, "image_3": {"label": "Imagen"}, "heading_3": {"label": "Encabezado"}, "caption_3": {"label": "Leyenda"}, "fourth_column_content": {"content": "Contenido de la cuarta columna"}, "image_4": {"label": "Imagen"}, "heading_4": {"label": "Encabezado"}, "caption_4": {"label": "Leyenda"}, "info_bar_options": {"content": "Opciones de la barra de información rápida"}, "image_size": {"options_1": {"label": "Pequeño"}, "options_2": {"label": "Mediano"}, "options_3": {"label": "Grande"}, "label": "Tam<PERSON><PERSON> de la imagen"}, "desktop_bar_position": {"options_1": {"label": "Derecha"}, "options_2": {"label": "Iz<PERSON>erda"}, "options_3": {"label": "Centro"}, "options_4": {"label": "Centro sin superposición"}, "label": "Posición de la barra en escritorio"}, "bar_full_width": {"label": "Mostrar la barra en ancho completo"}, "add_border": {"label": "Agregar borde a la sección"}, "full_width_background": {"label": "Hacer el fondo de la sección de ancho completo"}}, "presets": {"name": "Barra de información rápida"}}, "multirow": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagen"}, "image_height": {"options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}, "label": "Altura de la imagen"}, "desktop_image_width": {"options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON> de <PERSON>n en escritorio", "info": "La imagen se optimiza automáticamente para móvil."}, "button_style": {"options__1": {"label": "Botón sólido"}, "options__2": {"label": "Botón de contorno"}, "label": "Estilo del botón"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en escritorio"}, "desktop_content_position": {"options__1": {"label": "Arriba"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Abajo"}, "label": "Posición del contenido en escritorio", "info": "La posición se optimiza automáticamente para móvil."}, "image_layout": {"options__1": {"label": "Alternar desde la izquierda"}, "options__2": {"label": "Alternar desde la derecha"}, "options__3": {"label": "Alineado a la izquierda"}, "options__4": {"label": "Alineado a la derecha"}, "label": "Ubicación de la imagen en escritorio", "info": "La ubicación se optimiza automáticamente para móvil."}, "ignore_row_spacing": {"label": "Ignorar espacio entre filas"}, "full_width": {"label": "<PERSON>cer el diseño de ancho completo"}, "content_gradient_position": {"options__1": {"label": "Arriba izquierda"}, "options__2": {"label": "A<PERSON><PERSON> derecha"}, "options__3": {"label": "Abajo izquierda"}, "options__4": {"label": "A<PERSON><PERSON> derecha"}, "label": "Posición de la decoración del contenido"}, "ignore_content_gradient": {"label": "Ignorar decoración del contenido"}, "ignore_content_overlap": {"label": "Ignorar superposición del contenido"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en móvil"}, "header_mobile": {"content": "Diseño mó<PERSON>"}}, "blocks": {"row": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Leyenda"}, "heading": {"label": "Encabezado"}, "heading_size": {"options__1": {"label": "Extra grande"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Mediano"}, "label": "Tamaño del encabezado"}, "text": {"label": "Texto"}, "button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "Enlace del botón"}}}}, "presets": {"name": "<PERSON><PERSON>"}}, "main-account": {"name": "C<PERSON><PERSON>"}, "main-activate-account": {"name": "Activación de la cuenta"}, "main-addresses": {"name": "Direcciones"}, "main-article": {"name": "Entrada de blog", "blocks": {"featured_image": {"name": "Imagen destacada", "settings": {"image_height": {"label": "Altura de la imagen destacada", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}, "info": "Para obtener mejores resultados, utiliza una imagen con una relación de aspecto de 16:9. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "<PERSON>rar fecha"}, "blog_show_author": {"label": "Mostrar autor"}}}, "content": {"name": "Contenido"}, "tags": {"name": "Etiquetas"}, "buttons": {"name": "Botones de entrada anterior/siguiente"}, "share": {"name": "Compartir", "settings": {"text": {"label": "Texto"}, "featured_image_info": {"content": "Si incluyes un enlace en las publicaciones de redes sociales, la imagen destacada de la página se mostrará como la imagen previa. [Más información](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un título y descripción de la tienda se incluyen con la imagen previa. [Más información](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}}}, "main-blog": {"name": "Entradas de blog", "settings": {"header": {"content": "Tarjeta de entrada de blog"}, "make_first_post_featured": {"label": "Destaca la primera entrada del blog en la parte superior"}, "show_image": {"label": "Mostrar imagen destacada"}, "tags": {"label": "Mostrar etiquetas"}, "tag_label": {"label": "Filtrar por etiqueta"}, "tag_default": {"label": "Todos los artículos"}, "show_page_title": {"label": "Mostrar título de la página"}, "show_date": {"label": "<PERSON>rar fecha"}, "show_author": {"label": "Mostrar autor"}, "paragraph": {"content": "Cambia los extractos editando tus entradas de blog. [Más información](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "layout": {"label": "Diseño en escritorio", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}, "options__3": {"label": "3 Columnas"}, "info": "Las entradas se apilan en dispositivos móviles."}, "blog_style": {"label": "Estilo <PERSON>", "options__1": {"label": "Moderno"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Elegante"}}, "image_height": {"label": "Altura de la imagen destacada", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}, "info": "Para obtener mejores resultados, utiliza una imagen con una relación de aspecto de 3:2. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Precio subtotal"}, "buttons": {"name": "Botón de pago"}, "text-with-image": {"name": "Texto con imagen", "settings": {"image": {"label": "Imagen"}, "image_width": {"label": "<PERSON><PERSON>"}, "hide_image": {"label": "Ocultar imagen"}, "text": {"label": "Texto"}, "text_style": {"label": "Texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "centered_content": {"label": "Centrar contenido"}}}}}, "main-cart-items": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"desktop_enable_gradient": {"label": "Activar fondo de decoración"}, "header_mobile": {"content": "Diseño para dispositivos móviles"}, "mobile_enable_gradient": {"label": "Activar fondo de decoración"}}}, "main-404": {"name": "Página 404", "settings": {"heading": {"label": "Encabezado"}, "text": {"label": "Texto"}, "image": {"label": "Imagen"}}}, "main-collection-banner": {"name": "Banner de colección principal", "settings": {"paragraph": {"content": "Añade una descripción o imagen editando tu colección. [Más información](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Mostrar descripción de la colección"}, "show_breadcrumbs": {"label": "Mostrar migas de pan"}, "collection_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON> columnas"}, "options__2": {"label": "Fondo de color"}, "options__3": {"label": "Portada"}}, "desktop_content_alignment": {"label": "Alineación", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}, "header_style_three": {"content": "Opacidad del estilo de cubierta"}, "image_height": {"label": "Altura del banner", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "show_text_box": {"label": "Mostrar cuadro de texto"}, "text_box_color": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Oscuro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Esquema de color del cuadro de texto"}, "header_style_one_two": {"content": "Dos columnas/Fondo de color"}, "banner_background_color": {"label": "Color de fondo"}, "padding_heading": {"content": "<PERSON><PERSON><PERSON>"}, "all_products_collection_header": {"content": "Colección de todos los productos", "info": "Aquí puedes configurar la imagen y el encabezado para la colección predeterminada de todos los productos"}, "image": {"label": "Imagen"}, "fallback_heading": {"label": "Encabezado"}}, "presets": {"name": "Banner de colección principal"}}, "main-collection-product-grid": {"name": "Cuadrícula de productos", "settings": {"header__layout": {"content": "Diseño"}, "collection_layout": {"label": "Diseño de cuadrícula de productos", "options__1": {"label": "Moderno"}, "options__2": {"label": "Elegante"}}, "product_card_style": {"label": "Estilo de la tarjeta de producto", "options__1": {"label": "Predeterminado"}, "options__3": {"label": "Elegante"}}, "add_border": {"label": "<PERSON><PERSON><PERSON> borde a la imagen del producto"}, "products_per_page": {"label": "Productos por página"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "pagination": {"label": "Estilo de paginación", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Botón de cargar más"}}, "load_button": {"label": "Botón de carga"}, "enable_filtering": {"label": "Habilitar filtrado", "info": "Personaliza los filtros con la aplicación de búsqueda y descubrimiento. [Más información](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_switcher": {"label": "Habilitar conmutador"}, "filter_type": {"label": "Diseño de filtros en el escritorio", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cajón"}, "info": "El cajón es el diseño predeterminado en dispositivos móviles."}, "open_filter": {"label": "Estilo de filtro vertical", "options__1": {"label": "Primero abierto"}, "options__2": {"label": "Todos abiertos"}}, "enable_sorting": {"label": "Habilitar ordenamiento"}, "filter_layout": {"label": "Disposición del filtro", "options__1": {"label": "Cuadrado"}, "options__2": {"label": "<PERSON><PERSON> completo"}}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "show_rating": {"label": "Mostrar calificación del producto", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "header__1": {"content": "Filtrado y ordenamiento"}, "header__3": {"content": "Tarjeta de producto"}, "enable_tags": {"label": "Habilitar filtrado", "info": "Personaliza los filtros con la aplicación de búsqueda y descubrimiento. [Más información](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_quick_buy": {"label": "Habilitar vista rápida", "info": "Óptimo con tipo de carrito emergente o cajón."}, "enable_quick_add": {"label": "Habilitar aña<PERSON> al <PERSON>rito"}, "list_color_variants_in_collection": {"label": "Mostrar variantes de color del producto en la colección", "info": "Asegúrese de establecer imágenes destacadas para todas las variantes de su producto"}, "list_size_variants_in_collection": {"label": "Mostrar variantes de tamaño del producto en la colección"}, "hide_unavailable": {"label": "Ocultar productos no disponibles y agotados"}, "quick_add_position": {"label": "Posición de compra rápida", "options__1": {"label": "Superpuesta"}, "options__2": {"label": "Predeterminada"}}, "header_mobile": {"content": "Diseño para dispositivos móviles"}, "columns_mobile": {"label": "Número de columnas en dispositivos móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}, "disable_quick_add": {"label": "Deshabilitar compra rápida en dispositivos móviles cuando está habilitada en el escritorio"}, "product_count": {"label": "Habilitar el conteo de productos", "info": "El número no incluye las variantes de productos"}}, "blocks": {"promo_row": {"name": "Fila de promoción", "settings": {"text": {"label": "Coloque la promoción después del producto:", "info": "Ingresa un número entero"}, "image": {"label": "Imagen"}, "caption": {"label": "Subtítulo"}, "heading": {"label": "Encabezado"}, "subheading": {"label": "Texto"}, "link_label": {"label": "Etiqueta de enlace"}, "link": {"label": "Enlace"}, "banner_height": {"label": "Altura de la imagen", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Grande"}}, "banner_layout": {"label": "Diseño del banner", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Cuadrícula"}}, "show_text_box": {"label": "Mostrar cuadro de texto"}, "image_overlay_opacity": {"label": "Opacidad de la superposición de la imagen"}}}}}, "main-list-collections": {"name": "Página de lista de colecciones", "settings": {"title": {"label": "Encabezado"}, "sort": {"label": "Ordenar colecciones por:", "options__1": {"label": "Alfabéticamente, A-Z"}, "options__2": {"label": "Alfabéticamente, Z-A"}, "options__3": {"label": "<PERSON><PERSON>, de nuevo a antiguo"}, "options__4": {"label": "<PERSON><PERSON>, de antiguo a nuevo"}, "options__5": {"label": "<PERSON><PERSON><PERSON> de productos, de mayor a menor"}, "options__6": {"label": "<PERSON><PERSON><PERSON> de productos, de menor a mayor"}}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}, "info": "Agrega imágenes editando tus colecciones. [Más información](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "header_mobile": {"content": "Diseño para dispositivos móviles"}, "columns_mobile": {"label": "Número de columnas en dispositivos móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}}}, "main-login": {"name": "In<PERSON><PERSON>"}, "main-order": {"name": "Pedido"}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "Pie de página de contraseña"}, "main-password-header": {"name": "Encabezado de contraseña", "settings": {"logo_header": {"content": "Logotipo"}, "logo_help": {"content": "Edita tu logotipo en la configuración del tema."}}}, "main-product": {"name": "Product information", "blocks": {"spacer": {"name": "Separador", "settings": {"margin_top": {"label": "Margen superior"}}}, "tabs": {"name": "Pestañas", "settings": {"centered_tabs": {"label": "Centrar la navegación de pestañas"}, "remove_border_tabs": {"label": "Ocultar el borde inferior en la navegación de pestañas"}, "header_item_1": {"content": "Elemento 1"}, "header_item_2": {"content": "Elemento 2"}, "header_item_3": {"content": "Elemento 3"}, "heading_1": {"label": "Encabezado"}, "heading_2": {"label": "Encabezado"}, "heading_3": {"label": "Encabezado"}, "row_content_1": {"label": "Contenido"}, "row_content_2": {"label": "Contenido"}, "row_content_3": {"label": "Contenido"}}}, "payment_enable": {"name": "Íconos de pago", "settings": {"header": {"content": "Ocultar en móvil / escritorio", "info": "Caso de uso: Posicionamiento diferente para escritorio y móvil. Ejemplo: Posiciona este bloque en escritorio y ocúltalo en móvil. Luego, usa otra instancia del mismo bloque para posicionarlo en móvil y ocultarlo en escritorio."}, "hide_mobile": {"label": "Ocultar bloque en móvil"}, "hide_desktop": {"label": "Ocultar bloque en escritorio"}, "header_quick_view": {"content": "Modal de vista rápida", "info": "Elige ocultar o mostrar el bloque en la vista rápida"}, "hide_quick_view": {"label": "Ocultar bloque en la vista rápida"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "show_text_background": {"label": "<PERSON><PERSON><PERSON> de texto"}}}, "image": {"name": "Imagen", "settings": {"image": {"label": "Imagen"}, "image_link": {"label": "Enlace"}, "text_1": {"label": "Texto"}, "text_1_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "vendor": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"name": "Title", "settings": {"column": {"label": "Posición", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}, "options__3": {"label": "Debajo de la galería"}}, "margin_top": {"label": "Margen superior"}}}, "price": {"name": "Precio", "settings": {"text": {"label": "Texto"}}}, "waiting_list": {"name": "Lista de Espera", "settings": {"paragraph": {"content": "Registro en lista de espera para productos agotados"}, "waiting_list_title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "waiting_list_tagline": {"label": "<PERSON><PERSON>"}, "waiting_list_notice": {"label": "Aviso"}, "waiting_list_button": {"label": "Botón"}}}, "product-meta": {"name": "Inventario y calificación", "settings": {"header_inventory": {"content": "Inventario"}, "show_product_inventory": {"label": "Mostrar inventario"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Umbral de inventario bajo", "info": "Elige 0 para mostrar siempre en stock si está disponible."}, "show_inventory_quantity": {"label": "Mostrar cantidad de inventario"}, "header_sku": {"content": "SKU"}, "show_product_sku": {"label": "Mostrar SKU"}, "header_rating": {"content": "Calificación"}, "info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/product-ratings-and-reviews) para aprender más"}, "show_product_rating": {"label": "Mostrar calificación"}}}, "dynamic_card_icons": {"name": "Iconos dinámicos", "settings": {"info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/dynamic-icons-with-text) para aprender más"}, "card_metafield_text": {"label": "Texto"}, "card_metafield_key": {"label": "Clave del metacampo de la tarjeta"}, "card_metafield_image_size": {"label": "Tam<PERSON><PERSON> de la imagen", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "options__4": {"label": "<PERSON><PERSON> grande"}}, "card_metafield_layout": {"label": "Diseño", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Estrecho"}, "options__3": {"label": "En línea"}}, "card_metafield_icon_title_font_weight": {"label": "Peso de la fuente del título del icono", "options__1": {"label": "Negrita"}, "options__2": {"label": "Normal"}}, "card_metafield_border": {"label": "<PERSON><PERSON> de la imagen", "options__1": {"label": "<PERSON>rde"}, "options__2": {"label": "<PERSON> borde"}}, "card_metafield_enable_border_radius": {"label": "Habilitar radio del borde - círculo"}, "icons_tooltip": {"label": "Mostrar el título del icono como un tooltip"}}}, "inventory": {"name": "Estado del inventario", "settings": {"text_style": {"label": "Estilo del texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Umbral de inventario bajo", "info": "Elige 0 para siempre mostrar en stock si está disponible."}, "show_inventory_quantity": {"label": "Mostrar cantidad de inventario"}}}, "quantity_selector": {"name": "Selector de cantidad"}, "variant_picker": {"name": "Selector de variantes", "settings": {"picker_type": {"label": "Tipo", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Pastillas"}, "options__3": {"label": "Simple"}}}}, "countdown-timer": {"name": "Temporizador de cuenta regresiva"}, "buy_buttons": {"name": "Botones de compra", "settings": {"show_dynamic_checkout": {"label": "Mostrar botones de pago rápido", "info": "Usando los métodos de pago disponibles en tu tienda, los clientes verán su opción preferida, como PayPal. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Mostrar formulario de información del destinatario para tarjetas de regalo", "info": "Permite a los compradores enviar tarjetas de regalo en una fecha programada junto con un mensaje personal. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}, "hide_unavailable": {"label": "Ocultar variantes no disponibles y agotadas"}, "variant": {"content": "Selector de variantes"}, "swatch_shape": {"label": "Estilo <PERSON>", "info": "Consulte la documentación [Más información](https://manathemes.com/docs/flux-theme/how-to-guides/color-swatches)", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cuadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "pickup_availability": {"name": "Disponibilidad para recoger"}, "description": {"name": "Descripción"}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo del texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "delivery_estimator": {"name": "Calculadora de Entrega", "settings": {"info": {"content": "Visita [documentación del tema](https://manathemes.com/docs/flux-theme/how-to-guides/delivery-estimator) para aprender más"}, "delivery_estimator_text": {"label": "Texto de la calculadora de entrega"}, "earliest_delivery": {"label": "Entrega más temprana", "info": "Número mínimo de días para la entrega, por ejemplo: 2"}, "latest_delivery": {"label": "Entrega más tardía", "info": "Número máximo de días para la entrega, por ejemplo: 5"}, "text_style": {"label": "Estilo del texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "share": {"name": "Compartir", "settings": {"text": {"label": "Texto"}, "featured_image_info": {"content": "Si incluyes un enlace en las publicaciones de redes sociales, la imagen destacada de la página se mostrará como la imagen de vista previa. [Más información](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un título de la tienda y una descripción se incluyen con la imagen de vista previa. [Más información](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "share_top_right_corner": {"label": "Posicionar compartir en la esquina superior derecha"}}}, "custom_liquid": {"name": "Líquido personalizado", "settings": {"custom_liquid": {"label": "Líquido personalizado", "info": "Agrega fragmentos de aplicación u otro código líquido para crear personalizaciones avanzadas."}}}, "collapsible_tab": {"name": "<PERSON>la plegable", "settings": {"heading": {"info": "Incluye un encabezado que explique el contenido.", "label": "Encabezado"}, "no_padding": {"label": "<PERSON> relleno"}, "show_spacer": {"label": "Mostrar espaciador"}, "image": {"label": "Imagen"}, "content": {"label": "Contenido de la fila"}, "page": {"label": "Contenido de la fila desde la página"}, "icon": {"label": "Icono", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cesta"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Caja"}, "options__5": {"label": "Caja Corazón"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Calendario"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON> de chat"}, "options__9": {"label": "Marca de verificación"}, "options__10": {"label": "Portapapeles"}, "options__11": {"label": "<PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON>"}, "options__13": {"label": "Sobre"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Cuentag<PERSON><PERSON>"}, "options__16": {"label": "Signo de exclamación"}, "options__17": {"label": "Regalo"}, "options__18": {"label": "<PERSON>ema"}, "options__19": {"label": "Globo"}, "options__20": {"label": "Corazón"}, "options__21": {"label": "Auricular"}, "options__22": {"label": "Lista"}, "options__23": {"label": "Batido"}, "options__24": {"label": "Candado"}, "options__25": {"label": "V<PERSON><PERSON>"}, "options__26": {"label": "Chincheta"}, "options__27": {"label": "<PERSON><PERSON>"}, "options__28": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__29": {"label": "Shop"}, "options__30": {"label": "<PERSON>a"}, "options__31": {"label": "Avión"}, "options__32": {"label": "Planta"}, "options__33": {"label": "Etiqueta de precio"}, "options__34": {"label": " Fuego"}, "options__35": {"label": "Reciclar"}, "options__36": {"label": "Devolver"}, "options__37": {"label": "<PERSON><PERSON> de <PERSON>eve"}, "options__38": {"label": "Estrella"}, "options__39": {"label": "Cronómetro"}, "options__40": {"label": "Etiqueta"}, "options__41": {"label": "Árbol"}, "options__42": {"label": "Pulgar hacia arriba"}, "options__43": {"label": "Camión"}, "options__44": {"label": "Signo de interrogación"}}}}, "popup": {"name": "Ventana emergente", "settings": {"link_label": {"label": "Etiqueta del enlace"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "rating": {"name": "Valoración del producto", "settings": {"paragraph": {"content": "Para mostrar una valoración, añade una aplicación de valoración de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Productos complementarios", "settings": {"paragraph": {"content": "Para seleccionar productos complementarios, añade la aplicación de Búsqueda y Descubrimiento. [Más información](https://manathemes.com/docs/flux-theme/how-to-guides/complementary-and-related-products)"}, "heading": {"label": "Encabezado"}, "make_collapsible_row": {"label": "Mostrar como fila plegable"}, "icon": {"info": "Visible cuando se muestra como fila plegable."}, "product_list_limit": {"label": "Máximo de productos a mostrar"}, "products_per_page": {"label": "Número de productos por página"}, "pagination_style": {"label": "Estilo de paginación", "options": {"option_1": "Punt<PERSON>", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Números"}}, "product_card": {"heading": "Tarjeta de producto"}, "image_ratio": {"label": "Relación de aspecto de la imagen", "options": {"option_1": "Retrato", "option_2": "Cuadrada"}}, "enable_quick_add": {"label": "Habilitar botón de añadir r<PERSON>"}, "columns": {"label": "Columnas", "options": {"option_1": "Uno", "option_2": "<PERSON><PERSON>"}}}}, "ingredient_details": {"name": "Detalles del ingrediente", "settings": {"header_block_content": {"content": "Contenido del ingrediente"}, "left_column_label": {"label": "Etiqueta de la columna izquierda"}, "right_column_label": {"label": "Etiqueta de la columna derecha"}, "content": {"label": "Contenido", "info": "Separe la etiqueta y el valor con una coma. Use SHIFT + ENTER para agregar una nueva fila. Use un guion para sangrar las filas. IMPORTANTE: No use encabezados ni estilos de lista."}}}, "icon_with_text": {"name": "Ícono con texto", "settings": {"layout": {"label": "Diseño", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "content": {"label": "Contenido", "info": "Elige un ícono o añade una imagen para cada columna o fila."}, "heading": {"info": "Deja en blanco la etiqueta del encabezado para ocultar la columna de íconos."}, "icon_1": {"label": "Primer <PERSON><PERSON><PERSON>"}, "image_1": {"label": "Primera imagen"}, "heading_1": {"label": "Primer <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "icon_2": {"label": "<PERSON><PERSON><PERSON>"}, "image_2": {"label": "Segunda imagen"}, "heading_2": {"label": "<PERSON><PERSON><PERSON>"}, "icon_3": {"label": "<PERSON><PERSON><PERSON>"}, "image_3": {"label": "Tercera imagen"}, "heading_3": {"label": "<PERSON><PERSON><PERSON> <PERSON>"}}}}, "settings": {"info": {"content": "Visita la [documentación del tema](https://manathemes.com/docs/flux-theme/product-page/) para saber más"}, "header_layout": {"content": "Diseño"}, "header": {"content": "Medios", "info": "Obtén más información sobre [tipos de medios](https://help.shopify.com/manual/products/product-media)."}, "enable_full_width": {"label": "<PERSON>cer el diseño de ancho completo"}, "enable_sticky_info": {"label": "Habilitar contenido pegajoso en escritorio"}, "color_scheme": {"label": "Esquema de color de la información del producto"}, "enable_info_padding": {"label": "Habilitar el relleno de la caja de información del producto"}, "product_gallery_width": {"label": "<PERSON><PERSON> de la galería de productos"}, "product_layout": {"label": "Diseño de escritorio", "options__1": {"label": "Diseño 1"}, "options__2": {"label": "Diseño 2"}, "options__3": {"label": "Diseño 3"}, "options__4": {"label": "Diseño 4"}, "options__5": {"label": "Diseño 5"}}, "product_background_color": {"label": "Color de fondo"}, "title": {"label": "Encabezado"}, "gallery_layout": {"label": "Diseño de escritorio", "options__1": {"label": "Simple"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Miniaturas a la izquierda"}, "options__4": {"label": "Miniaturas abajo"}, "options__5": {"label": "Miniaturas a la derecha"}, "options__6": {"label": "Diapositiva"}}, "constrain_to_viewport": {"label": "Restringir medios a la altura de la pantalla"}, "media_size": {"label": "Ancho de medios en escritorio", "info": "Los medios se optimizan automáticamente para móvil.", "options__1": {"label": "Mediano"}, "options__2": {"label": "Grande"}}, "image_zoom": {"label": "Zoom de imagen", "info": "Clic y desplazamiento abren la ventana emergente de forma predeterminada en móvil.", "options__1": {"label": "<PERSON><PERSON>r ventana emergente"}, "options__2": {"label": "Clic y desplazamiento"}, "options__3": {"label": "Sin zoom"}}, "media_style": {"label": "Estilo de medios e información", "options__1": {"label": "Estilo uno"}, "options__2": {"label": "<PERSON><PERSON><PERSON> dos"}}, "media_position": {"label": "Posición de medios en escritorio", "info": "La posición se optimiza automáticamente para móvil.", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}}, "media_fit": {"label": "Ajuste de medios", "options__1": {"label": "Original"}, "options__2": {"label": "Llenar"}}, "thumbnail_size": {"label": "Tamaño de miniatura", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "under_gallery": {"label": "Posición del contenido debajo de la galería", "options__1": {"label": "Primero"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "mobile_thumbnails": {"label": "Diseño de galería", "options__1": {"label": "<PERSON><PERSON> columnas"}, "options__2": {"label": "Mostrar miniaturas"}, "options__3": {"label": "Ocultar miniaturas"}}, "hide_variants": {"label": "Ocultar medios de otras variantes después de seleccionar una variante"}, "enable_video_looping": {"label": "Habilitar reproducción en bucle de videos"}, "header_in_stock_colors": {"content": "Colores en stock"}, "in_stock_background_color": {"label": "Color de fondo en stock"}, "in_stock_color": {"label": "Color de texto en stock"}, "desktop_enable_gradient": {"label": "Habilitar fondo decorativo"}, "header_quantity_selector": {"content": "Selector de cantidad"}, "quantity_selector": {"label": "Radio de borde del selector de cantidad"}, "header_mobile": {"content": "Diseño en móvil"}, "mobile_enable_gradient": {"label": "Habilitar fondo decorativo"}}}, "main-register": {"name": "Registro"}, "main-reset-password": {"name": "Restablecimiento de contraseña"}, "main-search": {"name": "Resultados de búsqueda", "settings": {"columns_desktop": {"label": "Número de columnas en escritorio"}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> vendedor"}, "show_rating": {"label": "Mostrar calificación del producto", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "header__1": {"content": "Tarjeta de producto"}, "header__2": {"content": "Tarjeta de blog", "info": "Los estilos de las tarjetas de blog también se aplican a las tarjetas de página en los resultados de búsqueda. Para cambiar los estilos de las tarjetas, actualiza la configuración de tu tema."}, "header__3": {"content": "Tarjeta de página"}, "show_article_posts": {"label": "Mostrar tarjetas de artículos"}, "show_page_posts": {"label": "Mostrar tarjetas de páginas"}, "article_show_date": {"label": "<PERSON>rar fecha"}, "article_show_author": {"label": "Mostrar autor"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}}}}, "quick-order-list": {"name": "Lista de pedidos rápidos", "settings": {"enable_card_background": {"label": "Activar fondo de tarjeta"}, "show_image": {"label": "Mostrar imagen"}, "show_sku": {"label": "Mostrar SKU"}}, "presets": {"name": "Lista de pedidos rápidos"}}, "multicolumn": {"name": "Multicolumna", "settings": {"multicolumn_style": {"label": "Diseño", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "Elegante"}}, "enable_card_background": {"label": "Habilitar fondo de tarjeta"}, "caption": {"label": "Leyenda"}, "title": {"label": "Encabezado"}, "image_width": {"label": "<PERSON><PERSON>", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Completo"}}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}, "options__4": {"label": "Circular"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "column_alignment": {"label": "Alineación de columnas", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}}, "button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "Enlace del botón"}, "desktop_enable_gradient": {"label": "Habilitar fondo de decoración"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móvil"}, "mobile_enable_gradient": {"label": "Habilitar fondo de decoración"}}, "blocks": {"column": {"name": "Columna", "settings": {"image": {"label": "Imagen"}, "hide_image": {"label": "Ocultar imagen"}, "title": {"label": "Encabezado"}, "text": {"label": "Descripción"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}}}}, "presets": {"name": "Multicolumna"}}, "events-calendar": {"name": "Calendario de eventos", "settings": {"enable_card_background": {"label": "Habilitar fondo de tarjeta"}, "caption": {"label": "Subtítulo"}, "title": {"label": "Encabezado"}, "image_width": {"label": "<PERSON><PERSON>", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Completo"}}, "image_ratio": {"label": "Relación de imagen", "options__1": {"label": "Adaptarse a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrado"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "event_layout": {"label": "Diseño", "options__1": {"label": "Cuadrícula"}, "options__2": {"label": "Lista"}}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "column_alignment": {"label": "Alineación de columnas", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}}, "button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "Enlace del botón"}, "desktop_enable_gradient": {"label": "Habilitar fondo decorativo"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}, "swipe_on_mobile": {"label": "Habilitar deslizar en móvil"}, "mobile_enable_gradient": {"label": "Habilitar fondo decorativo"}}, "blocks": {"event": {"name": "Evento", "settings": {"image": {"label": "Imagen"}, "hide_image": {"label": "Ocultar imagen"}, "event_date": {"label": "<PERSON><PERSON>"}, "event_month": {"label": "<PERSON><PERSON>", "options__1": {"label": "Ene"}, "options__2": {"label": "Feb"}, "options__3": {"label": "Mar"}, "options__4": {"label": "Abr"}, "options__5": {"label": "May"}, "options__6": {"label": "Jun"}, "options__7": {"label": "Jul"}, "options__8": {"label": "Ago"}, "options__9": {"label": "Sep"}, "options__10": {"label": "Oct"}, "options__11": {"label": "Nov"}, "options__12": {"label": "Dic"}}, "hide_date": {"label": "<PERSON><PERSON><PERSON><PERSON> fecha"}, "event_time": {"label": "Día y hora"}, "event_price": {"label": "Precio"}, "event_heading": {"label": "Encabezado"}, "event_description": {"label": "Texto"}, "event_location": {"label": "Ubicación"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}}}}, "presets": {"name": "Calendario de eventos"}}, "multicolumn-cover": {"name": "Portada multicolumna", "settings": {"caption": {"label": "Leyenda"}, "title": {"label": "Encabezado"}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}}, "min_overlay_height": {"label": "Ajustar altura de superposición"}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "column_alignment": {"label": "Alineación de columnas", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}}, "background_style": {"label": "Fondo secundario", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Mostrar como fondo de columna"}}, "button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "Enlace del botón"}, "show_text_box": {"label": "Mostrar caja de texto"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móvil"}, "full_width": {"label": "<PERSON><PERSON> secci<PERSON> de ancho completo"}, "image_overlay_opacity": {"label": "Opacidad", "info": "Puedes cambiar el color de opacidad globalmente desde la [configuración del tema](/editor?context=theme&category=colors)."}}, "blocks": {"column": {"name": "Columna", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Leyenda"}, "title": {"label": "Encabezado"}, "text": {"label": "Descripción"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}}}}, "presets": {"name": "Portada multicolumna"}}, "icons-with-text": {"name": "Íconos con texto", "settings": {"image_width": {"label": "<PERSON><PERSON>", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Completo"}}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "column_alignment": {"label": "Alineación de columnas", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}}, "enable_desktop_slider": {"label": "Habilitar carrusel en escritorio"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móvil"}}, "blocks": {"column": {"name": "Columna", "settings": {"image": {"label": "Imagen"}, "title": {"label": "Encabezado"}, "text": {"label": "Descripción"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}}}}, "presets": {"name": "Íconos con texto"}}, "infocards": {"name": "Tarjetas informativas", "settings": {"cards_style": {"label": "Diseño", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}, "options__3": {"label": "3 Columnas"}}}, "blocks": {"infocard": {"name": "Tarjeta", "settings": {"image": {"label": "Imagen"}, "hide_image": {"label": "Ocultar imagen"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"label": "Subtítulo"}, "text": {"label": "Descripción"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}, "infocards_background_color": {"label": "Color de fondo de la tarjeta"}, "infocards_color": {"label": "Color del texto de la tarjeta"}}}}, "presets": {"name": "Tarjetas informativas"}}, "testimonials": {"name": "Testimonios", "settings": {"title": {"label": "Encabezado"}, "caption": {"label": "Subtítulo"}, "desktop_title_caption_position": {"label": "Posición del encabezado y subtítulo en escritorio", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}, "options__3": {"label": "Arriba"}}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "button_label": {"label": "Etiqueta del botón"}, "button_link": {"label": "Enlace del botón"}, "desktop_enable_gradient": {"label": "Habilitar fondo de decoración"}, "testimonials_style": {"label": "Estilo de testimonios", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "Moderno"}}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móvil"}, "mobile_enable_gradient": {"label": "Habilitar fondo de decoración"}}, "blocks": {"column": {"name": "Columna", "settings": {"image": {"label": "Imagen"}, "title": {"label": "Encabezado"}, "text": {"label": "Descripción"}, "rating": {"label": "Mostrar estrellas"}, "rating_stars": {"label": "Estrellas de valoración", "options__1": {"label": "5 Estrellas"}, "options__2": {"label": "4 Estrellas"}, "options__3": {"label": "3 Estrellas"}, "options__4": {"label": "2 Estrellas"}, "options__5": {"label": "1 Estrella"}}, "product": {"label": "Producto"}}}}, "presets": {"name": "Testimonios"}}, "promo-popup": {"name": "Ventana emergente promocional", "settings": {"enable_popup": {"label": "Habilitar ventana emergente"}, "popup_test": {"label": "Mostrar ventana emergente de prueba"}, "layout": {"label": "Diseño", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "Cuadrícula"}, "options__3": {"label": "Cuadrícula-Inferior-Derecha"}}, "popup_title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header_font_size": {"label": "Tamaño de fuente del título"}, "popup_message": {"label": "Men<PERSON><PERSON>"}, "message_font_size": {"label": "Tamaño de fuente del mensaje"}, "submit_button_text": {"label": "Texto del botón de envío"}, "button_color": {"label": "Color del botón"}, "button_text_color": {"label": "Color del texto del botón"}, "button_hover_color": {"label": "Color al pasar el mouse sobre el botón"}, "success_message": {"label": "Mensaje de éxito"}, "fadein": {"label": "Tiempo antes de que aparezca la ventana emergente"}, "popup_count": {"label": "Cantidad de ventanas emergentes"}, "popup_image": {"label": "Seleccionar una imagen para la ventana emergente"}, "image_width": {"label": "<PERSON><PERSON>"}, "image_alt": {"label": "Texto alternativo de la imagen"}}, "presets": {"name": "Ventana emergente promocional"}}, "promotion-cards": {"name": "Tarjetas de Promoción", "settings": {"title": {"label": "Encabezado"}, "caption": {"label": "Subtítulo"}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Número de columnas en móvil", "options__1": {"label": "1 Columna"}, "options__2": {"label": "2 Columnas"}}, "promotion_cards_style": {"label": "Estilo de la tarjeta de promoción", "options__1": {"label": "Moderno"}, "options__2": {"label": "Elegante"}, "options__3": {"label": "Portada"}}, "swipe_on_mobile": {"label": "Habilitar deslizamiento en móvil"}}, "blocks": {"column": {"name": "Columna", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Pie de foto"}, "title": {"label": "Encabezado"}, "link_label": {"label": "Etiqueta del enlace"}, "link": {"label": "Enlace"}, "product": {"label": "Producto"}}}}, "presets": {"name": "Tarjetas de Promoción"}}, "newsletter": {"name": "Registro de Correo Electrónico", "settings": {"full_width": {"label": "Hacer que el fondo de la sección sea de ancho completo", "info": "Visible solo con fondo de color."}, "paragraph": {"content": "Cada suscripción por correo electrónico crea una cuenta de cliente. [Más información](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "paragraph": {"name": "Subencabezado", "settings": {"paragraph": {"label": "Descripción"}}}, "email_form": {"name": "Formulario de Correo Electrónico"}}, "presets": {"name": "Registro de Correo Electrónico"}}, "newsletter-banner": {"name": "Banner de Registro de Correo Electrónico", "settings": {"paragraph": {"content": "Cada suscripción por correo electrónico crea una cuenta de cliente. [Más información](https://help.shopify.com/manual/customers)"}, "image": {"label": "Imagen"}, "image_2": {"label": "Imagen", "info": "Esta imagen se mostrará cuando seleccione 'Estilo dos' para el estilo de su boletín."}, "newsletter_style": {"label": "<PERSON>stilo <PERSON>", "options__1": {"label": "<PERSON> imagen"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON> imagen"}}, "layout": {"label": "Diseño de escritorio", "options__1": {"label": "Imagen primero"}, "options__2": {"label": "Texto primero"}}, "full_width": {"label": "Hacer que la sección sea de ancho completo"}}, "blocks": {"caption": {"name": "Subtítulo", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Tamaño de texto", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}}}, "heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "paragraph": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"label": "Descripción"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "label": "Estilo de texto"}}}, "email_form": {"name": "Formulario de Correo Electrónico"}}, "presets": {"name": "Banner de Registro de Correo Electrónico"}}, "recently-viewed-products": {"name": "Productos vistos recientemente", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"label": "Subtítulo"}}, "presets": {"name": "Productos vistos recientemente"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "related-products": {"name": "Productos relacionados", "settings": {"heading": {"label": "Encabezado"}, "caption": {"label": "Leyenda"}, "text_style": {"label": "Estilo de texto de la leyenda", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "text_size": {"label": "Tamaño de texto de la leyenda", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "products_to_show": {"label": "Máximo de productos a mostrar"}, "columns_desktop": {"label": "Número de columnas en escritorio"}, "paragraph__1": {"content": "Las recomendaciones dinámicas utilizan la información de los pedidos y los productos para cambiar y mejorar con el tiempo. [Más información](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Proporción de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> vendedor"}, "show_rating": {"label": "Mostrar la calificación del producto", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Diseño en dispositivos móviles"}, "columns_mobile": {"label": "Número de columnas en dispositivos móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}}}, "rich-text": {"name": "Texto enriquecido", "settings": {"desktop_content_position": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Posición del contenido en el escritorio", "info": "La posición se optimiza automáticamente para dispositivos móviles."}, "content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido"}, "header_mobile": {"content": "Diseño en dispositivos móviles"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido"}, "full_width": {"label": "Hacer que el fondo de la sección ocupe todo el ancho", "info": "Visible solo con fondo de color."}}, "blocks": {"image": {"name": "Imagen", "settings": {"image": {"label": "Imagen", "info": "Para una apariencia óptima, asegúrese de que las dimensiones de la imagen sean de 120x120 píxeles."}}}, "heading": {"name": "Encabezado", "settings": {"heading": {"label": "Encabezado"}}}, "caption": {"name": "Leyenda", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Tamaño de texto", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descripción"}}}, "buttons": {"name": "Botones", "settings": {"button_label_1": {"label": "Etiqueta del primer botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link_1": {"label": "Enlace del primer botón"}, "button_style_secondary_1": {"label": "Usar estilo de botón de contorno"}, "button_label_2": {"label": "Etiqueta del segundo botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link_2": {"label": "Enlace del segundo botón"}, "button_style_secondary_2": {"label": "Usar estilo de botón de contorno"}}}}, "presets": {"name": "Texto enriquecido"}}, "scrolling-text": {"name": "Texto/Imagen desplazable", "settings": {"scroll_direction": {"options__1": {"label": "Derecha"}, "options__2": {"label": "Iz<PERSON>erda"}, "label": "Dirección del desplazamiento"}, "enable_scroll_decoration": {"label": "Habilitar decoración"}, "scroll_decoration": {"label": "Decoración de desplazamiento", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Diamante"}, "options__3": {"label": "Hexágono"}, "options__4": {"label": "Estrella"}}, "enable_stencil_text": {"label": "Estilo de texto en plantilla"}, "scroll_speed": {"label": "Velocidad"}, "scroll_height": {"label": "Altura del desplazamiento"}, "scroll_text_size": {"label": "Tamaño del texto"}, "hover_stop": {"label": "Detener al pasar el ratón"}, "keep_small_mobile": {"label": "Mantener en móviles pequeños"}, "border": {"label": "<PERSON><PERSON><PERSON> b<PERSON>e"}, "enable_announcement_bar_desktop_sticky": {"label": "Habilitar diseño fijo en el escritorio"}, "enable_announcement_bar_mobile_sticky": {"label": "Habilitar diseño fijo en el móvil"}}, "blocks": {"text": {"name": "Texto/Imagen", "settings": {"text": {"label": "Descripción"}, "image": {"label": "Imagen", "info": "¡Si añades una imagen, el texto se ocultará!"}, "image_link": {"label": "<PERSON><PERSON> de imagen"}}}}, "presets": {"name": "Texto/Imagen desplazable"}}, "video": {"name": "Video", "settings": {"caption": {"label": "Subtítulo"}, "heading": {"label": "Encabezado"}, "text": {"label": "Descripción"}, "button_label_1": {"label": "Etiqueta del botón"}, "button_link_1": {"label": "Enlace del botón"}, "video_layout": {"label": "Diseño", "options__1": {"label": "Diseño uno"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "cover_image": {"label": "Imagen de portada"}, "video_url": {"label": "URL", "placeholder": "Usa una URL de YouTube o Vimeo", "info": "Acepta URL de YouTube o Vimeo"}, "description": {"label": "Texto alternativo del video", "info": "Describe el video para los clientes que usan lectores de pantalla. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Agregar relleno a la imagen", "info": "Selecciona relleno de imagen si no deseas que tu imagen de portada se recorte."}, "full_width": {"label": "Hacer que la sección ocupe todo el ancho"}}, "presets": {"name": "Video"}}, "video-background": {"name": "Fondo de Video", "settings": {"video_url": {"label": "Seleccionar video", "info": "Sube tu video a Contenido - Archivos, luego copia el enlace y pégalo aquí."}, "poster": {"label": "Agregar imagen de fondo de respaldo en caso de que el video no se cargue"}, "caption": {"label": "Subtítulo"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Descripción"}, "button_label": {"label": "Etiqueta del botón", "info": "Deje la etiqueta en blanco para ocultar el botón."}, "link": {"label": "Enlace del botón"}, "secondary_style": {"label": "Usar estilo de botón de contorno"}, "full_width_background": {"label": "Hacer que la sección ocupe todo el ancho"}, "background_height": {"label": "Altura del fondo de escritorio", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "background_height_mobile": {"label": "Altura del fondo en móvil", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "text_align": {"label": "Alineación del texto", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}, "box_align": {"label": "Posición del contenido", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}, "ignore_box": {"label": "Habilitar cuadro s<PERSON>o"}, "blur": {"label": "Agregar desenfoque para videos de baja calidad"}, "opacity": {"label": "Cambiar la opacidad del video"}, "header_video": {"content": "Video"}, "video_style": {"label": "Estilo de video", "info": "Nativo muestra el video en su tamaño original sin superposición de contenido. Para incluir contenido, seleccione la opción de fondo.", "options__1": {"label": "Nativo"}, "options__2": {"label": "Fondo"}}}, "presets": {"name": "Fondo de Video"}}, "slideshow": {"name": "Presentación de diapositivas", "settings": {"layout": {"label": "Diseño", "options__1": {"label": "<PERSON><PERSON> completo"}, "options__2": {"label": "En caja"}}, "slide_height": {"label": "Altura de la diapositiva", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Estilo de paginación", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Líneas"}}, "auto_rotate": {"label": "Rotar automáticamente las diapositivas"}, "change_slides_speed": {"label": "Cambiar diapositivas cada"}, "slideshow_controls_background": {"label": "Agregar fondo a los controles del carrusel"}, "slider_animations": {"label": "Animaciones del carrusel", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "Zoom vertical"}, "options__3": {"label": "Zoom horizontal"}, "options__4": {"label": "Revelar vertical"}, "options__5": {"label": "<PERSON><PERSON>r horizontal"}}, "mobile": {"content": "Diseño mó<PERSON>"}, "show_text_below": {"label": "Mostrar contenido debajo de las imágenes en dispositivos móviles"}, "accessibility": {"content": "Accesibilidad", "label": "Descripción de la presentación de diapositivas", "info": "Describe la presentación de diapositivas para los clientes que usan lectores de pantalla."}}, "blocks": {"slide": {"name": "Diapositiva", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Subtítulo"}, "heading": {"label": "Encabezado"}, "subheading": {"label": "Texto"}, "button_label": {"label": "Etiqueta del botón", "info": "Deja el campo en blanco para ocultar el botón."}, "link": {"label": "Enlace del botón"}, "secondary_style": {"label": "Usar estilo de botón de contorno"}, "box_align": {"label": "Posición del contenido en escritorio", "info": "La posición se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba al centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centro a la izquierda"}, "options__5": {"label": "Centro al centro"}, "options__6": {"label": "Centro a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo al centro"}, "options__9": {"label": "Abajo a la derecha"}}, "show_text_box": {"label": "Mostrar cuadro de texto en escritorio"}, "text_alignment": {"label": "Alineación del contenido en escritorio", "option_1": {"label": "Iz<PERSON>erda"}, "option_2": {"label": "Centro"}, "option_3": {"label": "Derecha"}}, "image_overlay_opacity": {"label": "Opacidad de la superposición de imagen"}, "color_scheme": {"info": "Visible cuando se muestra el contenedor."}, "text_alignment_mobile": {"label": "Alineación del contenido en móviles", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}}}}, "presets": {"name": "Presentación de diapositivas"}}, "slideshow-two-columns": {"name": "Presentación de diapositivas dos columnas", "settings": {"layout": {"label": "Diseño", "options__1": {"label": "<PERSON><PERSON> completo"}, "options__2": {"label": "En caja"}}, "slide_height": {"label": "Altura de la diapositiva", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Estilo de paginación", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Líneas"}}, "auto_rotate": {"label": "Rotar automáticamente las diapositivas"}, "change_slides_speed": {"label": "Cambiar diapositivas cada"}, "slideshow_controls_background": {"label": "Agregar fondo a los controles del carrusel"}, "slider_animations": {"label": "Animaciones del carrusel", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "Zoom vertical"}, "options__3": {"label": "Zoom horizontal"}, "options__4": {"label": "Revelar vertical"}, "options__5": {"label": "<PERSON><PERSON>r horizontal"}}, "mobile": {"content": "Diseño mó<PERSON>"}, "hide_slider_controls": {"label": "Ocultar controles del deslizador"}, "show_text_below": {"label": "Mostrar contenido debajo de las imágenes en dispositivos móviles"}, "accessibility": {"content": "Accesibilidad", "label": "Descripción de la presentación de diapositivas", "info": "Describe la presentación de diapositivas para los clientes que usan lectores de pantalla."}}, "blocks": {"slide": {"name": "Diapositiva", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Subtítulo"}, "heading": {"label": "Encabezado"}, "subheading": {"label": "Texto"}, "button_label": {"label": "Etiqueta del botón", "info": "Deja el campo en blanco para ocultar el botón."}, "link": {"label": "Enlace del botón"}, "secondary_style": {"label": "Usar estilo de botón de contorno"}, "text_alignment": {"label": "Alineación del contenido en escritorio", "option_1": {"label": "Iz<PERSON>erda"}, "option_2": {"label": "Centro"}, "option_3": {"label": "Derecha"}}, "color_scheme": {"info": "Visible cuando se muestra el contenedor."}, "text_alignment_mobile": {"label": "Alineación del contenido en móviles", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}}}}, "presets": {"name": "Presentación de diapositivas dos columnas"}}, "advanced-slideshow": {"name": "Parallax Slider", "settings": {"auto_rotate": {"label": "Rotar diapositivas automáticamente"}, "slider_direction": {"label": "Dirección del deslizador", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "slider_loop": {"label": "<PERSON><PERSON><PERSON> infinito"}, "slider_interval": {"label": "Intervalo del deslizador"}, "slider_height": {"label": "Altura del deslizador"}, "full_width": {"label": "<PERSON><PERSON> el deslizador de ancho completo"}, "box": {"content": "Configuración del cuadro de contenido"}, "content_height": {"label": "Posición vertical del contenido"}, "content_position": {"label": "Posición horizontal del contenido"}, "content_size": {"label": "Tamaño del cuadro de contenido"}, "content_align": {"label": "Alineación del contenido", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}, "content_box": {"label": "Activar fondo del cuadro de contenido"}, "content_opacity": {"label": "Opacidad del cuadro de contenido"}, "text": {"content": "Configuración del texto"}, "heading_size": {"label": "Tamaño del encabezado"}, "heading_style": {"label": "Estilo del texto del encabezado", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Capitalizar"}}, "caption_size": {"label": "Tamaño del subtítulo"}, "link_size": {"label": "<PERSON><PERSON><PERSON>lace"}, "caption_style": {"label": "Estilo del texto del subtítulo", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Capitalizar"}}, "mobile": {"content": "Configuración móvil"}, "mobile_heading_size": {"label": "Tamaño del encabezado"}, "mobile_caption_size": {"label": "Tamaño del subtítulo"}, "mobile_height": {"label": "Altura del deslizador"}, "mobile_content_height": {"label": "Posición vertical del contenido"}, "mobile_content_size": {"label": "Tamaño del cuadro de contenido"}, "other": {"content": "Otras configuraciones"}}, "blocks": {"slide": {"name": "Diapositiva", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Subtítulo"}, "heading": {"label": "Encabezado"}, "button_label": {"label": "Etiqueta del botón", "info": "Deje la etiqueta en blanco para ocultar el botón."}, "link": {"label": "Enlace del botón"}, "show_link_button": {"label": "Habilitar botón"}, "secondary_style": {"label": "Usar estilo de botón de contorno"}}}}, "presets": {"name": "Parallax Slider"}}, "slick-slider": {"name": "<PERSON><PERSON>lider", "settings": {"header": {"content": "Ajustes del Slider"}, "image_opacity": {"label": "Opacidad de la imagen"}, "opacity_mobile": {"label": "Opacidad solo en móviles"}}, "presets": {"name": "<PERSON><PERSON>lider"}}, "comparison-slider": {"name": "Control deslizante de comparación", "settings": {"title": {"label": "Encabezado"}, "caption": {"label": "Subtítulo"}, "image": {"label": "Imagen uno"}, "image_2": {"label": "<PERSON><PERSON> dos"}, "comparison_slider_layout": {"label": "Diseño", "options__1": {"label": "Vertical"}, "options__2": {"label": "<PERSON><PERSON>na derecha"}, "options__3": {"label": "Columna i<PERSON>erda"}}, "height": {"label": "Altura en escritorio"}, "mouse_percent": {"label": "Posición predeterminada del deslizador"}, "disable_before_after": {"label": "Deshabilitar antes/después"}, "before_text": {"label": "Texto antes"}, "after_text": {"label": "Texto después"}, "header_mobile": {"content": "Móvil"}, "mobile_height": {"label": "Altura en móvil"}, "full_width": {"label": "<PERSON>cer el diseño de ancho completo"}, "accessibility": {"content": "Accesibilidad", "label": "Descripción de la presentación de diapositivas", "info": "Describe la presentación de diapositivas para los clientes que usan lectores de pantalla."}}, "presets": {"name": "Control deslizante de comparación"}}, "anchor_link": {"name": "<PERSON><PERSON> an<PERSON>", "settings": {"anchor": {"label": "Ancla HTML", "info": "Agregar texto de anclaje para enlazar"}}, "presets": {"name": "<PERSON><PERSON> an<PERSON>"}}, "separator": {"name": "Separador", "settings": {"separator_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Lín<PERSON>"}, "options__2": {"label": "Ola 1"}, "options__3": {"label": "Ola 2"}, "options__4": {"label": "Ola 3"}, "options__5": {"label": "Ola 4"}, "options__6": {"label": "Nube 1"}, "options__7": {"label": "Nube 2"}, "options__8": {"label": "Colinas 1"}, "options__9": {"label": "Colinas 2"}, "options__10": {"label": "Borde festoneado 1"}, "options__11": {"label": "Borde festoneado 2"}}, "spacer_color": {"label": "Color del separador"}, "margin_top_desktop": {"label": "Margen superior en escritorio"}, "margin_bottom_desktop": {"label": "Margen inferior en escritorio"}}, "presets": {"name": "Separador"}}, "collapsible_content": {"name": "Contenido plegable", "settings": {"caption": {"label": "Subtítulo"}, "heading": {"label": "Encabezado"}, "heading_alignment": {"label": "Alineación del encabezado", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}}, "content_style": {"label": "Diseño", "options__1": {"label": "Caja"}, "options__2": {"label": "Sin caja"}}, "container_color_scheme": {"label": "Esquema de color del contenedor", "info": "Visible cuando el diseño está configurado en contenedor de fila o contenedor de sección."}, "open_first_collapsible_row": {"label": "Abrir la primera fila plegable"}, "open_all_collapsible_row": {"label": "<PERSON><PERSON><PERSON> todo"}, "header_mobile": {"content": "Diseño mó<PERSON>"}}, "blocks": {"collapsible_row": {"name": "<PERSON>la plegable", "settings": {"heading": {"info": "Incluye un encabezado que explique el contenido.", "label": "Encabezado"}, "row_content": {"label": "Contenido de la fila"}, "page": {"label": "Contenido de fila desde la página"}, "icon": {"label": "Icono", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cesta"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Caja"}, "options__5": {"label": "Caja Corazón"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Calendario"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON> de Chat"}, "options__9": {"label": "Marca de Verificación"}, "options__10": {"label": "Portapapeles"}, "options__11": {"label": "<PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON>"}, "options__13": {"label": "Sobre"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Cuentag<PERSON><PERSON>"}, "options__16": {"label": "Signo de Exclamación"}, "options__17": {"label": "Regalo"}, "options__18": {"label": "<PERSON>ema"}, "options__19": {"label": "Globo"}, "options__20": {"label": "Corazón"}, "options__21": {"label": "Auricular"}, "options__22": {"label": "Lista"}, "options__23": {"label": "Batido"}, "options__24": {"label": "Candado"}, "options__25": {"label": "Magia"}, "options__26": {"label": "Chincheta de Mapa"}, "options__27": {"label": "<PERSON><PERSON>"}, "options__28": {"label": "<PERSON><PERSON>"}, "options__29": {"label": "Shop"}, "options__30": {"label": "<PERSON>a"}, "options__31": {"label": "Avión"}, "options__32": {"label": "Planta"}, "options__33": {"label": "Etiqueta de Precio"}, "options__34": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__35": {"label": "Reciclar"}, "options__36": {"label": "Retorno"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Estrella"}, "options__39": {"label": "Cronómetro"}, "options__40": {"label": "Etiqueta"}, "options__41": {"label": "Árbol"}, "options__42": {"label": "Pulgar Arriba"}, "options__43": {"label": "Camión"}, "options__44": {"label": "Signo de Interrogación"}}}}}, "presets": {"name": "Contenido plegable"}}, "tabs": {"name": "Pestañas", "settings": {"tabs_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Predeterminado"}, "options__2": {"label": "Simple"}, "options__3": {"label": "Botón"}}, "full_width": {"label": "Hacer que las pestañas ocupen todo el ancho"}}, "blocks": {"tab": {"name": "Pestaña", "settings": {"image": {"label": "Imagen"}, "hide_image": {"label": "Ocultar imagen"}, "tab_image_width": {"label": "Tam<PERSON><PERSON> de la imagen"}, "heading": {"info": "Incluye un encabezado que explique el contenido.", "label": "Encabezado"}, "row_content": {"label": "Contenido de la pestaña"}, "page": {"label": "Contenido de la pestaña desde la página"}, "button_label": {"label": "Etiqueta del primer botón", "info": "Deja en blanco la etiqueta para ocultar el botón."}, "button_link": {"label": "Enlace del primer botón"}, "button_style_secondary": {"label": "Usar estilo de botón de contorno"}}}}, "presets": {"name": "Pestañas"}}, "about": {"name": "Acerca de", "settings": {"caption": {"label": "Pie de foto"}, "title": {"label": "Encabezado"}, "text": {"label": "Texto"}, "image": {"label": "Imagen"}, "open_first_collapsible_row": {"label": "Abrir primera fila plegable"}, "header_mobile": {"content": "Diseño mó<PERSON>"}}, "blocks": {"collapsible_row": {"name": "<PERSON>la plegable", "settings": {"heading": {"info": "Incluir un encabezado que explique el contenido.", "label": "Encabezado"}, "row_content": {"label": "Contenido de la fila"}, "page": {"label": "Contenido de la fila desde la página"}, "icon": {"label": "Ícono", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Canasta"}, "options__3": {"label": "Corazón de bolsa"}, "options__4": {"label": "Caja"}, "options__5": {"label": "Corazón de caja"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Calendario"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON> de chat"}, "options__9": {"label": "Marca de verificación"}, "options__10": {"label": "Portapapeles"}, "options__11": {"label": "<PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON> mitad"}, "options__13": {"label": "Sobre"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Cuentag<PERSON><PERSON>"}, "options__16": {"label": "Signo de exclamación"}, "options__17": {"label": "Regalo"}, "options__18": {"label": "<PERSON>ema"}, "options__19": {"label": "Mundo"}, "options__20": {"label": "Corazón"}, "options__21": {"label": "Audífono"}, "options__22": {"label": "Lista"}, "options__23": {"label": "Batido"}, "options__24": {"label": "Candado"}, "options__25": {"label": "V<PERSON><PERSON>"}, "options__26": {"label": "Chincheta de mapa"}, "options__27": {"label": "<PERSON><PERSON>"}, "options__28": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__29": {"label": "Shop"}, "options__30": {"label": "<PERSON>a"}, "options__31": {"label": "Avión"}, "options__32": {"label": "Planta"}, "options__33": {"label": "Etiqueta de precio"}, "options__34": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__35": {"label": "Reciclaje"}, "options__36": {"label": "Devolver"}, "options__37": {"label": "<PERSON><PERSON> de <PERSON>eve"}, "options__38": {"label": "Estrella"}, "options__39": {"label": "Cronómetro"}, "options__40": {"label": "Etiqueta"}, "options__41": {"label": "Árbol"}, "options__42": {"label": "Pulgar arriba"}, "options__43": {"label": "Camión"}, "options__44": {"label": "Signo de interrogación"}}}}}, "presets": {"name": "Acerca de"}}}}
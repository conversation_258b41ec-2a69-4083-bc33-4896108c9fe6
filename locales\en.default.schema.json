{
  "settings_schema": {
    "global": {
      "settings": {
        "header__border": {
          "content": "Border"
        },
        "header__shadow": {
          "content": "Shadow"
        },
        "header__exclusions": {
          "content": "Exclusions"
        },
        "global_shadow_opacity": {
          "label": "Opacity"
        },
        "global_shadow_blur": {
          "label": "Blur"
        },
        "global_border_radius": {
          "label": "Corner radius"
        },
        "show_button_arrow": {
          "label": "Show button arrow"
        },
        "button_style": {
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Modern"
          },
          "options__3": {
            "label": "Elegant"
          },
          "label": "Button style"
        },
        "global_shadow_horizontal_offset": {
          "label": "Horizontal offset"
        },
        "global_shadow_vertical_offset": {
          "label": "Vertical offset"
        },
        "global_border_thickness": {
          "label": "Thickness"
        },
        "global_border_opacity": {
          "label": "Opacity"
        },
        "exclude_drawer": {
          "label": "Exclude drawers (mobile)"
        },
        "exclude_popup": {
          "label": "Exclude popups"
        },
        "exclude_inputs": {
          "label": "Exclude inputs"
        },
        "image_padding": {
          "label": "Image padding"
        },
        "text_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Text alignment"
        },
        "card_icons_size": {
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "label": "Icons size"
        }
      }
    },
    "global_design": {
      "name": "Global design"
    },
    "breadcrumbs": {
      "name": "Breadcrumbs",
      "settings": {
        "show_breadcrumb_nav": {
          "label": "Show breadcrumbs navigation"
        },
        "breadcrumbs_style": {
          "options__1": {
            "label": "Border"
          },
          "options__2": {
            "label": "No border"
          },
          "options__3": {
            "label": "Background"
          },
          "label": "Style"
        }
      }
    },
    "animations": {
      "name": "Animations",
      "settings": {
        "deactivate_animation": {
          "label": "Deactivate on scroll animation"
        },
        "deactivate_menu_animation": {
          "label": "Deactivate nav menu animation"
        },
        "page_scroll_indicator": {
          "label": "Enable page scroll indicator"
        },
        "scroll_indicator_color": {
          "label": "Scroll indicator color"
        },
        "heading__1": {
          "content": "Page Loader"
        },
        "page_loader_enable": {
          "label": "Enable loader"
        },
        "loader_background_color": {
          "label": "Loader background color"
        },
        "loader_text_color": {
          "label": "Loader text color"
        },
        "page_loader_text": {
          "label": "Page loader text"
        },
        "page_loader_style": {
          "options__1": {
            "label": "Always"
          },
          "options__2": {
            "label": "First time"
          },
          "label": "Loader text appears"
        }
      }
    },
    "color_swatches": {
      "name": "Color Swatches",
      "settings": {
        "info": {
          "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/color-swatches/) to learn more"
        },
        "swatch_enable": {
          "label": "Enable Color Swatches"
        },
        "color_map": {
          "label": "Colors:",
          "info": "Add variant color names and color values in this format: 'Black: #000000'. One color per line. You can also add an image by using the image handle in this format: 'Natural: natural.png'."
        }
      }
    },
    "cards": {
      "name": "Product cards",
      "settings": {
        "card_metafield_key": {
          "label": "Card metafield key"
        },
        "enable_tooltip": {
          "label": "Enable tooltip"
        }
      }
    },
    "quick_view": {
      "name": "Quick view",
      "settings": {
        "quick_view_product_gallery_width": {
          "label": "Product gallery width"
        },
        "quick_view_height": {
          "label": "Quick view height"
        }
      }
    },
    "collection_cards": {
      "name": "Collection cards"
    },
    "blog_cards": {
      "name": "Blog cards"
    },
    "badges": {
      "name": "Badges",
      "settings": {
        "position": {
          "options__1": {
            "label": "Bottom left"
          },
          "options__2": {
            "label": "Bottom right"
          },
          "options__3": {
            "label": "Top left"
          },
          "options__4": {
            "label": "Top right"
          },
          "label": "Position on cards"
        },
        "badge_discount": {
          "label": "Show discount percentage"
        },
        "header": {
          "content": "Custom Badge"
        },
        "info": {
          "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/product-card-badges) to learn more"
        },
        "custom_badge_text": {
          "label": "Custom badge text"
        },
        "custom_badge_tag": {
          "label": "Custom badge tag",
          "info": "Make sure to use the same tag you added in product tags."
        },
        "custom_badge_color": {
          "label": "Text color"
        },
        "custom_badge_background": {
          "label": "Background color"
        }
      }
    },
    "colors": {
      "name": "Color schemes",
      "settings": {
        "background": {
          "label": "Background"
        },
        "background_gradient": {
          "label": "Background gradient",
          "info": "Background gradient replaces background where possible."
        },
        "text": {
          "label": "Text"
        },
        "button_background": {
          "label": "Solid button background"
        },
        "button_label": {
          "label": "Solid button label"
        },
        "secondary_button_label": {
          "label": "Outline button"
        },
        "color_link": {
          "label": "Link color"
        },
        "shadow": {
          "label": "Shadow"
        }
      }
    },
    "colors_add": {
      "name": "Additional colors",
      "settings": {
        "heading__1": {
          "content": "Special backgrounds"
        },
        "background_color_3": {
          "label": "Content box background",
          "info": "Used as a background color for content boxes within sections."
        },
        "background_color_2": {
          "label": "Decoration background",
          "info": "Used as a decoration background color within various sections."
        },
        "heading__3": {
          "content": "Accent colors"
        },
        "accent_background_color_1": {
          "label": "Accent background color 1",
          "info": "Used as a primary accent background color."
        },
        "accent_color_1": {
          "label": "Accent text color 1",
          "info": "Used as a primary accent text color."
        },
        "accent_color_2": {
          "label": "Accent background color 2",
          "info": "Used as a secondary accent background color."
        },
        "accent_text_color_2": {
          "label": "Accent text color 2",
          "info": "Used as a secondary accent text color."
        },
        "heading__4": {
          "content": "Border colors"
        },
        "border_color_1": {
          "label": "Border color",
          "info": "Used as a border color."
        },
        "border_color_2": {
          "label": "Border color 2",
          "info": "Used as a secondary border color."
        },
        "heading__5": {
          "content": "Special Buttons"
        },
        "button_quick_add_background_color": {
          "label": "Quick add button background",
          "info": "Used for the default position (elegant collection style)"
        },
        "button_quick_add_text_color": {
          "label": "Quick add button text",
          "info": "Used for the default position (elegant collection style)"
        },
        "button_quick_add_background_color_hover": {
          "label": "Quick add button background hover",
          "info": "Used for the default position (elegant collection style)"
        },
        "button_quick_add_text_color_hover": {
          "label": "Quick add button text hover",
          "info": "Used for the default position (elegant collection style)"
        },
        "heading__6": {
          "content": "Other colors"
        },
        "countdown_background_top": {
          "label": "Countdown background first"
        },
        "countdown_text_top": {
          "label": "Countdown text first"
        },
        "countdown_background_bottom": {
          "label": "Countdown background second"
        },
        "countdown_text_bottom": {
          "label": "Countdown text second"
        },
        "opacity_color": {
          "label": "Global opacity color"
        },
        "color_link": {
          "label": "Link color"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_h1": {
          "label": "Assign h1 to logo and title"
        },
        "logo_width": {
          "label": "Desktop logo width"
        },
        "logo_width_mobile": {
          "label": "Mobile logo width"
        },
        "favicon": {
          "label": "Favicon image",
          "info": "Will be scaled down to 32 x 32px"
        }
      }
    },
    "brand_information": {
      "name": "Brand information",
      "settings": {
        "paragraph": {
          "content": "Add a brand description to your store's footer."
        },
        "brand_headline": {
          "label": "Headline"
        },
        "brand_description": {
          "label": "Description"
        },
        "brand_image": {
          "label": "Image"
        },
        "brand_image_width": {
          "label": "Image width"
        }
      }
    },
    "typography": {
      "name": "Typography",
      "settings": {
        "type_header_font": {
          "label": "Font",
          "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https:\/\/help.shopify.com\/manual\/online-store\/os\/store-speed\/improving-speed#fonts)"
        },
        "type_header_weight": {
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Bold"
          },
          "label": "Font weight"
        },
        "heading_scale": {
          "label": "Font size scale"
        },
        "header__1": {
          "content": "Headings"
        },
        "header__2": {
          "content": "Body"
        },
        "type_body_font": {
          "label": "Font",
          "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https:\/\/help.shopify.com\/manual\/online-store\/os\/store-speed\/improving-speed#fonts)"
        },
        "body_scale": {
          "label": "Font size scale"
        },
        "header__3": {
          "content": "Navigation menu"
        },
        "navigation_font": {
          "options__1": {
            "label": "Headings"
          },
          "options__2": {
            "label": "Body"
          },
          "label": "Font"
        },
        "text_style": {
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Uppercase"
          },
          "label": "Text style"
        },
        "navigation_scale": {
          "label": "Main menu font scale"
        },
        "subnavigation_scale": {
          "label": "Submenu font scale"
        }
      }
    },
    "buttons": {
      "name": "Buttons"
    },
    "variant_pills": {
      "name": "Variant pills",
      "paragraph": "Variant pills are one way of displaying your product variants. [Learn more](https:\/\/help.shopify.com\/en\/manual\/online-store\/themes\/theme-structure\/page-types#variant-picker-block)"
    },
    "inputs": {
      "name": "Inputs"
    },
    "content_containers": {
      "name": "Content containers"
    },
    "popups": {
      "name": "Dropdowns and pop-ups",
      "paragraph": "Affects areas like navigation dropdowns, pop-up modals, and cart pop-ups."
    },
    "media": {
      "name": "Media"
    },
    "drawers": {
      "name": "Drawers"
    },
    "styles": {
      "name": "Icons",
      "settings": {
        "accent_icons": {
          "options__3": {
            "label": "Outline button"
          },
          "options__4": {
            "label": "Text"
          },
          "label": "Color"
        }
      }
    },
    "social-media": {
      "name": "Social media",
      "settings": {
        "header": {
          "content": "Social accounts"
        },
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https:\/\/twitter.com\/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https:\/\/facebook.com\/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https:\/\/pinterest.com\/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http:\/\/instagram.com\/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https:\/\/tiktok.com\/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "https:\/\/shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https:\/\/www.snapchat.com\/add\/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https:\/\/www.youtube.com\/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https:\/\/vimeo.com\/shopify"
        }
      }
    },
    "search_input": {
      "name": "Search behavior",
      "settings": {
        "header": {
          "content": "Search suggestions"
        },
        "predictive_search_enabled": {
          "label": "Enable search suggestions"
        },
        "predictive_search_show_vendor": {
          "label": "Show product vendor",
          "info": "Visible when search suggestions enabled."
        },
        "predictive_search_show_price": {
          "label": "Show product price",
          "info": "Visible when search suggestions enabled."
        }
      }
    },
    "currency_format": {
      "name": "Currency format",
      "settings": {
        "content": "Currency codes",
        "paragraph": "Cart and checkout prices always show currency codes. Example: $1.00 USD.",
        "currency_code_enabled": {
          "label": "Show currency codes"
        }
      }
    },
    "cart": {
      "name": "Cart",
      "settings": {
        "cart_type": {
          "label": "Cart type",
          "drawer": {
            "label": "Drawer"
          },
          "page": {
            "label": "Page"
          },
          "notification": {
            "label": "Popup notification"
          }
        },
        "cart_icon": {
          "label": "Cart icon",
          "bag": {
            "label": "Bag"
          },
          "cart": {
            "label": "Cart"
          }
        },
        "show_vendor": {
          "label": "Show vendor"
        },
        "show_cart_note": {
          "label": "Enable cart note"
        },
        "cart_note_open": {
          "label": "Open cart note at load"
        },
        "header_shipping": {
          "content": "Free shipping message"
        },
        "enable_free_shipping_message": {
          "label": "Enable free shipping message"
        },
        "free_shipping_message": {
          "label": "Message",
          "info": "Use *amount* placeholder where the calculated number should appear"
        },
        "free_shipping_success": {
          "label": "Success message"
        },
        "header_promo": {
          "content": "Promo message"
        },
        "enable_promo_message": {
          "label": "Enable promo message"
        },
        "promo_message": {
          "label": "Message"
        },
        "header_cross_sell": {
          "content": "Cross-sell"
        },
        "enable_cross_sell": {
          "label": "Enable cross-sell"
        },
        "info": {
          "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/flux-theme-settings\/cart) to learn more"
        },
        "cross_sell_product": {
          "label": "Cross-sell collection"
        },
        "cross_sell_label": {
          "label": "Cross-sell title"
        },
        "header_terms": {
          "content": "Terms and conditions"
        },
        "enable_terms": {
          "label": "Enable T&C requirenment"
        },
        "terms_label": {
          "label": "Label"
        },
        "header_empty_cart": {
          "content": "Empty cart"
        },
        "cart_drawer": {
          "header": "Cart drawer",
          "collection": {
            "label": "Collection",
            "info": "Visible when cart drawer is empty."
          }
        },
        "enable_empty_cart_message": {
          "label": "Enable empty cart message"
        },
        "empty_cart_message": {
          "label": "Empty cart message"
        },
        "button_link": {
          "label": "Button link"
        },
        "header_buttons": {
          "content": "Buttons"
        },
        "disable_cart_button": {
          "label": "Disable view cart button"
        },
        "disable_checkout_button": {
          "label": "Disable checkout button"
        }
      }
    },
    "layout": {
      "name": "Layout",
      "settings": {
        "page_width": {
          "label": "Page width"
        },
        "spacing_sections": {
          "label": "Space between template sections"
        },
        "header__grid": {
          "content": "Grid"
        },
        "paragraph__grid": {
          "content": "Affects areas with multiple columns or rows."
        },
        "spacing_grid_horizontal": {
          "label": "Horizontal space"
        },
        "spacing_grid_vertical": {
          "label": "Vertical space"
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Desktop layout",
        "padding_top": "Top padding",
        "padding_bottom": "Bottom padding",
        "padding_left": "Left padding",
        "padding_right": "Right padding"
      },
      "section_margin_heading": "Mobile layout",
      "margin_spacing": {
        "options__1": {
          "label": "-"
        },
        "options__2": {
          "label": "+"
        },
        "label": "Top margin"
      },
      "margin_top": "Margin value",
      "spacing": "Spacing",
      "header_color_box": {
        "content": "Colors"
      },
      "colors_box": {
        "label": "Content box color scheme"
      },
      "colors": {
        "option_1": {
          "label": "Background 1 - Text 1"
        },
        "option_2": {
          "label": "Background 1 - Text 2"
        },
        "option_3": {
          "label": "Background 2 - Text 1"
        },
        "option_4": {
          "label": "Background 2 - Text 2"
        },
        "option_5": {
          "label": "Accent background 1 - Acccent text 1"
        },
        "option_6": {
          "label": "Accent background 2 - Acccent text 2"
        },
        "label": "Section color scheme",
        "info": "To change the color scheme, update your [theme settings](\/editor?context=theme&category=colors).",
        "has_cards_info": "To change the color scheme, update your theme settings."
      },
      "text_color": {
        "option_none": {
          "label": "None (as set in color scheme)"
        },
        "option_1": {
          "label": "Text color 1"
        },
        "option_2": {
          "label": "Text color 2"
        },
        "option_3": {
          "label": "Accent color 1"
        },
        "option_4": {
          "label": "Accent color 2"
        },
        "label": "Text color",
        "info": "Here you can change text color independently of the color scheme."
      },
      "heading_style": {
        "label": "Heading style",
        "options__1": {
          "label": "Default"
        },
        "options__2": {
          "label": "Uppercase"
        }
      },
      "heading_size": {
        "label": "Heading size",
        "options__1": {
          "label": "Extra large"
        },
        "options__2": {
          "label": "Large"
        },
        "options__3": {
          "label": "Medium"
        },
        "options__4": {
          "label": "Small"
        }
      },
      "text_block_size": {
        "label": "Text size",
        "options__1": {
          "label": "Extra large"
        },
        "options__2": {
          "label": "Large"
        },
        "options__3": {
          "label": "Medium"
        },
        "options__4": {
          "label": "Small"
        }
      },
      "price_size": {
        "label": "Price size",
        "options__1": {
          "label": "Extra large"
        },
        "options__2": {
          "label": "Large"
        },
        "options__3": {
          "label": "Medium"
        },
        "options__4": {
          "label": "Small"
        }
      },
      "heading_tag": {
        "label": "Heading tag",
        "info": "Specify heading code types for SEO and search engines for crawling purposes.",
        "options__1": {
          "label": "H1"
        },
        "options__2": {
          "label": "H2"
        },
        "options__3": {
          "label": "H3"
        },
        "options__4": {
          "label": "H4"
        },
        "options__5": {
          "label": "H5"
        },
        "options__6": {
          "label": "H6"
        }
      },
      "text_size": {
        "label": "Subheading size",
        "options__1": {
          "label": "Small"
        },
        "options__2": {
          "label": "Medium"
        },
        "options__3": {
          "label": "Large"
        }
      },
      "text_style": {
        "label": "Subheading text style",
        "options__1": {
          "label": "Default"
        },
        "options__2": {
          "label": "Uppercase"
        }
      },
      "ignore_spacing": {
        "label": "Ignore space between template sections"
      },
      "disable_arrow_mobile": {
        "label": "Hide slider arrows"
      },
      "animate_slider": {
        "label": "Animate image"
      },
      "gradient_position": {
        "options__1": {
          "label": "Left"
        },
        "options__2": {
          "label": "Right"
        },
        "label": "Decoration background position"
      },
      "countdown-text": {
        "label": "Text"
      },
      "countdown-date": {
        "label": "Date",
        "info": "Format example: Sep 30, 2025"
      },
      "countdown-time": {
        "label": "Time",
        "info": "Format example: 9:00"
      },
      "countdown-date-time-style": {
        "label": "Countodown style",
        "options__1": {
          "label": "Option 1"
        },
        "options__2": {
          "label": "Option 2"
        }
      },
      "countdown-text-position": {
        "label": "Text position",
        "options__1": {
          "label": "Top"
        },
        "options__2": {
          "label": "Left"
        }
      },
      "large-countdown": {
        "label": "Large countdown"
      },
      "countdown_finished_message": {
        "label": "Countdown finished message",
        "info": "If empty, timer is hidden when it hits 0"
      },
      "countdown_timer_tag": {
        "label": "Show only on products with tag 'timer'"
      }
    },
    "announcement-bar": {
      "name": "Info\/Social Bar",
      "settings": {
        "header_layout": {
          "content": "Layout"
        },
        "announcement_position": {
          "label": "Bar layout",
          "options__1": {
            "label": "Horizontal"
          },
          "options__2": {
            "label": "Vertical Left"
          },
          "options__3": {
            "label": "Vertical Right"
          }
        },
        "sticky": {
          "content": "Sticky Bar",
          "info": "If you choose the sticky bar option, please adjust the top padding within the header section accordingly."
        },
        "enable_announcement_bar_desktop_sticky": {
          "label": "Make sticky on desktop"
        },
        "enable_announcement_bar_mobile_sticky": {
          "label": "Make sticky on mobile"
        },
        "header_vertical_bar": {
          "content": "Vertical bar options"
        },
        "vertical_position": {
          "label": "Position"
        },
        "header_top_bar": {
          "content": "Top bar options"
        },
        "text": {
          "label": "Text"
        },
        "link": {
          "label": "Link"
        },
        "text_animation": {
          "label": "Activate text animation"
        },
        "show_countdown": {
          "label": "Show countdown",
          "info": "Enabling countdown will replace text added in the announcement blocks"
        },
        "countdown": {
          "content": "Countdown Timer"
        },
        "show_social_content": {
          "content": "Socials"
        },
        "show_social_info": {
          "info": "To display your social media accounts, link them in your [theme settings](\/editor?context=theme&category=social%20media)."
        },
        "show_social": {
          "label": "Show social media icons"
        },
        "country_selector_content": {
          "content": "Country\/region selector"
        },
        "country_selector_info": {
          "info": "To add a country\/region, go to your [market settings.](\/admin\/settings\/markets)"
        },
        "enable_country_selector": {
          "label": "Enable country\/region selector"
        }
      },
      "presets": {
        "name": "Info\/Social Bar"
      }
    },
    "apps": {
      "name": "Apps",
      "settings": {
        "include_margins": {
          "label": "Make section margins the same as theme"
        }
      },
      "presets": {
        "name": "Apps"
      }
    },
    "featured-collections": {
      "name": "Featured collections",
      "settings": {
        "featured_collection_1": {
          "label": "Collection 1"
        },
        "featured_collection_2": {
          "label": "Collection 2"
        },
        "collection_style": {
          "label": "Style",
          "options__1": {
            "label": "Elegant"
          },
          "options__2": {
            "label": "Modern"
          },
          "options__3": {
            "label": "No image"
          }
        },
        "layout": {
          "label": "Desktop layout",
          "options__1": {
            "label": "Collection first"
          },
          "options__2": {
            "label": "Text first"
          }
        },
        "desktop_content_position": {
          "label": "Desktop content position",
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Middle"
          },
          "options__3": {
            "label": "Bottom"
          }
        },
        "show_text_box": {
          "label": "Show text box"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "info": "Add images by editing your collections. [Learn more](https:\/\/help.shopify.com\/manual\/products\/collections)"
        },
        "desktop_content_alignment": {
          "label": "Desktop content alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "desktop_content_overlap": {
          "label": "Add Overlap"
        },
        "desktop_collections_alignment": {
          "label": "Desktop collections alignment",
          "options__1": {
            "label": "First down"
          },
          "options__2": {
            "label": "Even"
          },
          "options__3": {
            "label": "Second down"
          }
        },
        "full_width": {
          "label": "Make layout full width"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "layout_mobile": {
          "label": "Mobile layout",
          "options__1": {
            "label": "Text first"
          },
          "options__2": {
            "label": "Collection first"
          }
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "caption": {
          "name": "Subheading",
          "settings": {
            "heading": {
              "label": "Subheading"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            }
          }
        },
        "button": {
          "name": "Button",
          "settings": {
            "button_label": {
              "label": "Button label"
            },
            "button_link": {
              "label": "Button URL"
            }
          }
        }
      },
      "presets": {
        "name": "Featured collections"
      }
    },
    "subcollections": {
      "name": "Subcollections",
      "settings": {
        "info": {
          "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/how-to-set-up-subcollections-on-collection-pages) to learn more"
        },
        "title": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "enable_desktop_slider": {
          "label": "Enable desktop slider"
        },
        "collections_to_show": {
          "label": "Maximum subcollections to show"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "subcollection_list_style": {
          "label": "Collection list style",
          "options__1": {
            "label": "Elegant"
          },
          "options__2": {
            "label": "Modern"
          },
          "options__3": {
            "label": "No image"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "info": "Add images by editing your collections. [Learn more](https:\/\/help.shopify.com\/manual\/products\/collections)"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        }
      },
      "presets": {
        "name": "Subcollections"
      }
    },
    "collection-list": {
      "name": "Collection list",
      "settings": {
        "title": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "enable_desktop_slider": {
          "label": "Enable desktop slider"
        },
        "collections_to_show": {
          "label": "Maximum subcollections to show"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "collection_list_style": {
          "label": "Collection list style",
          "options__1": {
            "label": "Elegant"
          },
          "options__2": {
            "label": "Modern"
          },
          "options__3": {
            "label": "No image"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "info": "Add images by editing your collections. [Learn more](https:\/\/help.shopify.com\/manual\/products\/collections)"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Collection",
          "settings": {
            "collection": {
              "label": "Collection"
            }
          }
        }
      },
      "presets": {
        "name": "Collection list"
      }
    },
    "collection-tabs": {
      "name": "Collection tabs",
      "settings": {
        "collection_style": {
          "label": "Collection style",
          "options__1": {
            "label": "Modern"
          },
          "options__2": {
            "label": "Elegant"
          }
        },
        "content_alignment": {
          "label": "Content alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        }
      },
      "blocks": {
        "collection": {
          "name": "Collection",
          "settings": {
            "collection": {
              "label": "Select Collection"
            },
            "tab_heading": {
              "label": "Tab heading"
            }
          }
        }
      },
      "presets": {
        "name": "Collection tabs"
      }
    },
    "two-images-text": {
      "name": "Images with text",
      "settings": {
        "image": {
          "label": "Image one"
        },
        "image_2": {
          "label": "Image two"
        },
        "layout": {
          "label": "Desktop layout",
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Text first"
          }
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "layout_mobile": {
          "label": "Mobile layout",
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Text first"
          }
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "caption": {
          "name": "Subheading",
          "settings": {
            "heading": {
              "label": "Subheading"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            }
          }
        },
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Image"
            }
          }
        },
        "image_1": {
          "name": "Image overlap",
          "settings": {
            "image_1": {
              "label": "Image"
            }
          }
        },
        "button": {
          "name": "Button",
          "settings": {
            "button_label": {
              "label": "Button label"
            },
            "button_link": {
              "label": "Button URL"
            }
          }
        }
      },
      "presets": {
        "name": "Images with text"
      }
    },
    "location-map": {
      "name": "Location map",
      "settings": {
        "info": {
          "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/locations-map) to learn more"
        },
        "title": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "content": {
          "label": "Text",
          "info": "Content for the section text area."
        },
        "header_contact": {
          "content": "Contact form"
        },
        "show_contact_form": {
          "label": "Show contact form"
        },
        "hide_phone_field": {
          "label": "Hide phone field"
        },
        "api_key": {
          "label": "Google Maps API Key",
          "info": "Activating Google Maps and Geocoding API's is required for the map to work."
        },
        "zoom_level": {
          "label": "Zoom Level",
          "info": "Zoom level for the map (1-18)."
        },
        "header": {
          "content": "Location"
        },
        "address": {
          "label": "Address",
          "info": "Enter the address you want to display on the map."
        },
        "marker_content": {
          "label": "Marker Content",
          "info": "Content for the marker popup."
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Full width"
          },
          "options__2": {
            "label": "Boxed"
          },
          "options__3": {
            "label": "Content left, map right"
          },
          "options__4": {
            "label": "Map left, content right"
          }
        }
      },
      "presets": {
        "name": "Location map"
      }
    },
    "countdown": {
      "name": "Promo with timer",
      "settings": {
        "image": {
          "label": "Image one"
        },
        "image_2": {
          "label": "Image two"
        },
        "layout": {
          "label": "Desktop layout",
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Text first"
          }
        },
        "desktop_content_position": {
          "label": "Desktop content position",
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Middle"
          },
          "options__3": {
            "label": "Bottom"
          }
        },
        "desktop_content_alignment": {
          "label": "Desktop content alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "layout_mobile": {
          "label": "Mobile layout",
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Text first"
          }
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "caption": {
          "name": "Subheading",
          "settings": {
            "heading": {
              "label": "Subheading"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            }
          }
        },
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Image"
            }
          }
        },
        "countdown-timer": {
          "name": "Countdown Timer"
        },
        "button": {
          "name": "Button",
          "settings": {
            "button_label": {
              "label": "Button label"
            },
            "button_link": {
              "label": "Button URL"
            }
          }
        }
      },
      "presets": {
        "name": "Promo with Timer"
      }
    },
    "image-gallery": {
      "name": "Image gallery",
      "settings": {
        "title": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "scroll_height_mobile": {
          "label": "Scroll height"
        }
      },
      "blocks": {
        "text": {
          "name": "Image"
        }
      },
      "presets": {
        "name": "Image gallery"
      }
    },
    "contact-form": {
      "name": "Contact form",
      "settings": {
        "contact_style": {
          "label": "Layout",
          "options__1": {
            "label": "Column"
          },
          "options__2": {
            "label": "Row"
          }
        },
        "image_height": {
          "label": "Banner height",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "image_height_mobile": {
          "label": "Banner height",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "caption": {
          "label": "Subheading"
        },
        "heading": {
          "label": "Heading"
        },
        "button_label_1": {
          "label": "Button label"
        },
        "button_link_1": {
          "label": "Button link"
        },
        "text": {
          "label": "Sidebar info"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      },
      "presets": {
        "name": "Contact form"
      }
    },
    "custom-liquid": {
      "name": "Custom liquid",
      "settings": {
        "custom_liquid": {
          "label": "Custom liquid",
          "info": "Add app snippets or other liquid code to create advanced customizations."
        }
      },
      "presets": {
        "name": "Custom liquid"
      }
    },
    "featured-blog": {
      "name": "Featured blog posts",
      "settings": {
        "caption": {
          "label": "Subheading"
        },
        "heading": {
          "label": "Heading"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Number of blog posts to show"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "show_view_all": {
          "label": "Enable \"View all\" button if blog includes more blog posts than shown"
        },
        "show_image": {
          "label": "Show featured image"
        },
        "show_date": {
          "label": "Show date"
        },
        "show_author": {
          "label": "Show author"
        },
        "show_excerpt": {
          "label": "Show excerpt"
        },
        "blog_style": {
          "label": "Blog style",
          "options__1": {
            "label": "Modern"
          },
          "options__2": {
            "label": "Simple"
          },
          "options__3": {
            "label": "Elegant"
          },
          "options__4": {
            "label": "Cover"
          }
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        }
      },
      "presets": {
        "name": "Featured blog posts"
      }
    },
    "featured-collection": {
      "name": "Featured collection",
      "settings": {
        "caption": {
          "label": "Subheading"
        },
        "title": {
          "label": "Heading"
        },
        "description": {
          "label": "Description"
        },
        "show_description": {
          "label": "Show collection description from the admin"
        },
        "description_style": {
          "label": "Description style",
          "options__1": {
            "label": "Body"
          },
          "options__2": {
            "label": "Subtitle"
          },
          "options__3": {
            "label": "Uppercase"
          }
        },
        "collection": {
          "label": "Collection"
        },
        "collection_style": {
          "label": "Collection style",
          "options__1": {
            "label": "Modern"
          },
          "options__3": {
            "label": "Elegant"
          }
        },
        "products_to_show": {
          "label": "Maximum products to show"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "show_view_all": {
          "label": "Enable \"View all\" if collection has more products than shown"
        },
        "view_all_style": {
          "label": "\"View all\" style",
          "options__1": {
            "label": "Link"
          },
          "options__2": {
            "label": "Button"
          }
        },
        "enable_desktop_slider": {
          "label": "Enable carousel on desktop"
        },
        "full_width": {
          "label": "Make products full width"
        },
        "header": {
          "content": "Product card"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Show vendor"
        },
        "show_rating": {
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app."
        },
        "enable_quick_buy": {
          "label": "Enable quick view",
          "info": "Optimal with popup or drawer cart type."
        },
        "quick_add_position": {
          "label": "Quick view position",
          "options__1": {
            "label": "Overlay"
          },
          "options__2": {
            "label": "Default"
          }
        },
        "header_overlap": {
          "content": "Overlap",
          "info": "You can place the featured collection above the previous section with an overlapping effect. This will hide the heading and description to achieve a cleaner appearance."
        },
        "enable_overlap": {
          "label": "Enable overlap"
        },
        "margin_top": {
          "label": "Overlap "
        },
        "mobile_margin_top": {
          "label": "Mobile overlap"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 column"
          },
          "options__2": {
            "label": "2 columns"
          }
        },
        "disable_quick_add": {
          "label": "Disable quick add on mobile when enabled on desktop"
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "header_banner_height": {
          "content": "Banner Height"
        },
        "banner_height_desktop": {
          "label": "Desktop height"
        },
        "banner_height_mobile": {
          "label": "Mobile height"
        }
      },
      "presets": {
        "name": "Featured collection"
      }
    },
    "featured-product": {
      "name": "Featured product",
      "blocks": {
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Default"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "vendor": {
          "name": "Vendor"
        },
        "title": {
          "name": "Title"
        },
        "product-meta": {
          "name": "Inventory"
        },
        "price": {
          "name": "Price"
        },
        "quantity_selector": {
          "name": "Quantity selector"
        },
        "countdown-timer": {
          "name": "Countdown Timer"
        },
        "variant_picker": {
          "name": "Variant picker",
          "settings": {
            "picker_type": {
              "label": "Type",
              "options__1": {
                "label": "Dropdown"
              },
              "options__2": {
                "label": "Pills"
              },
              "options__3": {
                "label": "Simple"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Buy buttons",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Show dynamic checkout buttons",
              "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal. [Learn more](https:\/\/help.shopify.com\/manual\/using-themes\/change-the-layout\/dynamic-checkout)"
            },
            "variant": {
              "content": "Variant picker"
            },
            "swatch_shape": {
              "label": "Swatch style",
              "info": "Check the documentation [Learn more](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/color-swatches)",
              "options__1": {
                "label": "Circle"
              },
              "options__2": {
                "label": "Square"
              },
              "options__3": {
                "label": "None"
              }
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "description": {
          "name": "Description"
        },
        "share": {
          "name": "Share",
          "settings": {
            "text": {
              "label": "Text"
            },
            "featured_image_info": {
              "content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/images\/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "A store title and description are included with the preview image. [Learn more](https:\/\/help.shopify.com\/manual\/promoting-marketing\/seo\/adding-keywords#set-a-title-and-description-for-your-online-store)"
            }
          }
        },
        "custom_liquid": {
          "name": "Custom liquid",
          "settings": {
            "custom_liquid": {
              "label": "Custom liquid"
            }
          }
        },
        "rating": {
          "name": "Product rating",
          "settings": {
            "paragraph": {
              "content": "To display a rating, add a product rating app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/theme-structure\/theme-features#featured-product-rating)"
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Product"
        },
        "product_background_color": {
          "label": "Background color"
        },
        "secondary_background": {
          "label": "Show secondary background"
        },
        "header": {
          "content": "Media",
          "info": "Learn more about [media types](https:\/\/help.shopify.com\/manual\/products\/product-media)"
        },
        "media_position": {
          "label": "Desktop media position",
          "info": "Position is automatically optimized for mobile.",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Right"
          }
        },
        "hide_variants": {
          "label": "Hide unselected variants media on desktop"
        },
        "enable_video_looping": {
          "label": "Enable video looping"
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      },
      "presets": {
        "name": "Featured product"
      }
    },
    "footer": {
      "name": "Footer",
      "blocks": {
        "link_list": {
          "name": "Menu",
          "settings": {
            "heading": {
              "label": "Heading"
            },
            "menu": {
              "label": "Menu",
              "info": "Displays only top-level menu items."
            }
          }
        },
        "brand_information": {
          "name": "Brand information",
          "settings": {
            "brand_headline": {
              "label": "Headline"
            },
            "brand_description": {
              "label": "Description"
            },
            "brand_image": {
              "label": "Image"
            },
            "brand_image_width": {
              "label": "Image width"
            },
            "header__2": {
              "content": "Newsletter"
            },
            "newsletter_enable": {
              "label": "Show email signup"
            },
            "header__1": {
              "content": "Social media icons"
            },
            "show_social": {
              "label": "Show social media icons",
              "info": "To display your social media accounts, link them in your [theme settings](\/editor?context=theme&category=social%20media)."
            },
            "social_icons_size": {
              "options__1": {
                "label": "Extra small"
              },
              "options__2": {
                "label": "Small"
              },
              "options__3": {
                "label": "Medium"
              },
              "options__4": {
                "label": "Large"
              },
              "label": "Social icons size"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "heading": {
              "label": "Heading"
            },
            "subtext": {
              "label": "Subtext"
            }
          }
        },
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Image"
            },
            "image_width": {
              "label": "Image width"
            },
            "image_headline": {
              "label": "Heading"
            },
            "image_description": {
              "label": "Description"
            },
            "button_label": {
              "label": "Button label"
            },
            "button_link": {
              "label": "Button link"
            },
            "button_style_secondary_1": {
              "label": "Use outline button style"
            },
            "show_text_under": {
              "label": "Show content under"
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Show email signup"
        },
        "newsletter_heading": {
          "label": "Heading"
        },
        "header__1": {
          "content": "Email signup",
          "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https:\/\/help.shopify.com\/manual\/customers\/manage-customers)"
        },
        "header__2": {
          "content": "Social media icons",
          "info": "To display your social media accounts, link them in your [theme settings](\/editor?context=theme&category=social%20media)."
        },
        "socials_heading": {
          "label": "Heading"
        },
        "show_social": {
          "label": "Show social media icons"
        },
        "header__3": {
          "content": "Country\/Region selector"
        },
        "header__4": {
          "info": "To add a country\/region, go to your [market settings.](\/admin\/settings\/markets)"
        },
        "enable_country_selector": {
          "label": "Enable country\/region selector"
        },
        "header__5": {
          "content": "Language selector"
        },
        "header__6": {
          "info": "To add a language, go to your [language settings.](\/admin\/settings\/languages)"
        },
        "enable_language_selector": {
          "label": "Enable language selector"
        },
        "header__10": {
          "content": "Powered by Shopify"
        },
        "show_powered_by_link": {
          "label": "Show powered by Shopify"
        },
        "header__7": {
          "content": "Payment methods"
        },
        "payment_enable": {
          "label": "Show payment icons"
        },
        "header__8": {
          "content": "Policy links",
          "info": "To add store policies, go to your [policy settings](\/admin\/settings\/legal)."
        },
        "show_policy": {
          "label": "Show policy links"
        },
        "margin_top": {
          "label": "Desktop\/mobile top margin"
        },
        "margin_bottom": {
          "label": "Mobile bottom margin"
        },
        "centered_content": {
          "label": "Center content on mobile"
        },
        "header__9": {
          "content": "Follow on shop",
          "info": "Display follow button for your storefront on the shop app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/follow-on-shop)"
        },
        "enable_follow_on_shop": {
          "label": "Enable follow on shop"
        },
        "header_back_to_top_desktop": {
          "content": "Back to top button on desktop"
        },
        "back_to_top_desktop": {
          "label": "Enable back to top button"
        },
        "back_to_top_bottom": {
          "label": "Position vertical"
        },
        "back_to_top_right": {
          "label": "Position horizontal"
        },
        "header_back_to_top_mobile": {
          "content": "Back to top button on mobile"
        },
        "back_to_top_mobile": {
          "label": "Enable back to top button"
        },
        "footer_border": {
          "label": "Enable footer top border"
        },
        "make_columns_even": {
          "label": "Make columns even"
        }
      }
    },
    "header": {
      "name": "Header",
      "blocks": {
        "mega_promotion": {
          "name": "Mega promotion",
          "settings": {
            "info": {
              "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/mega-menu-promotions) to learn more"
            },
            "mega_promotion_item": {
              "label": "Menu item",
              "info": "Enter the name of the mega menu item you want to add promotion card to."
            },
            "mega_promotion_image": {
              "label": "Image"
            },
            "mega_promotion_caption": {
              "label": "Subheading"
            },
            "mega_promotion_title": {
              "label": "Title"
            },
            "mega_promotion_link_label": {
              "label": "Link label"
            },
            "mega_promotion_link": {
              "label": "Link url"
            }
          }
        },
        "mega_image_menu": {
          "name": "Mega image menu",
          "settings": {
            "heading_1": {
              "content": "Item 1"
            },
            "heading_2": {
              "content": "Item 2"
            },
            "heading_3": {
              "content": "Item 3"
            },
            "heading_4": {
              "content": "Item 4"
            },
            "heading_5": {
              "content": "Item 5"
            },
            "heading_6": {
              "content": "Item 6"
            },
            "heading_7": {
              "content": "Item 7"
            },
            "heading_8": {
              "content": "Item 8"
            }
          }
        }
      },
      "settings": {
        "header_layout": {
          "content": "Header layout"
        },
        "header_additional_links": {
          "content": "Additional links"
        },
        "button_label": {
          "label": "Link label 1"
        },
        "button_link": {
          "label": "Link 1"
        },
        "button_label_one": {
          "label": "Link label 2"
        },
        "button_link_one": {
          "label": "Link 2"
        },
        "disable_additional_links": {
          "label": "Enable additional links"
        },
        "logo_help": {
          "content": "Edit your logo in [theme settings](\/editor?context=theme&category=logo)."
        },
        "desktop_header_layout": {
          "label": "Desktop header layout",
          "options__1": {
            "label": "Two-Row"
          },
          "options__2": {
            "label": "Logo-Menu-Icons"
          },
          "options__3": {
            "label": "Menu-Logo-Icons"
          },
          "options__4": {
            "label": "Logo-Menu-Icons"
          }
        },
        "show_search_icon": {
          "label": "Alternative style",
          "info": "This setting applies only when 'Two Row' is selected. It allows you to choose an alternative style for this layout."
        },
        "menu": {
          "label": "Menu"
        },
        "featured_collection_1": {
          "label": "Collection 1"
        },
        "featured_collection_2": {
          "label": "Collection 2"
        },
        "menu_type_desktop": {
          "label": "Desktop menu type",
          "info": "Menu type is automatically optimized for mobile.",
          "options__1": {
            "label": "Dropdown"
          },
          "options__2": {
            "label": "Mega menu"
          },
          "options__3": {
            "label": "Drawer"
          }
        },
        "all_items_mega": {
          "label": "Apply mega menu to all menu items",
          "info": "If mega menu is selected. By default only menu items with 3 levels are turned into mega menu"
        },
        "submenu_animation_position": {
          "label": "Mega menu animation",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Bottom up"
          },
          "options__3": {
            "label": "Left to right"
          }
        },
        "sticky_header_type": {
          "label": "Sticky header",
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "On scroll up"
          },
          "options__3": {
            "label": "Always"
          },
          "options__4": {
            "label": "Always, reduce logo size"
          }
        },
        "country_selector_content": {
          "content": "Country\/region selector"
        },
        "country_selector_info": {
          "info": "To add a country\/region, go to your [market settings.](\/admin\/settings\/markets)"
        },
        "enable_country_selector": {
          "label": "Enable country\/region selector on desktop"
        },
        "enable_fixed_header_type": {
          "label": "Alternative header on home page"
        },
        "enable_fixed_header_type_collection": {
          "label": "Alternative header on collection page"
        },
        "enable_fixed_header_type_all": {
          "label": "Alternative header on all pages"
        },
        "enable_fixed_header_transparent": {
          "label": "Make alternative header transparent"
        },
        "enable_header_full_width": {
          "label": "Enable header full width"
        },
        "fixed_header_type_margin": {
          "label": "Desktop margin top"
        },
        "fixed_header_type_margin_mobile": {
          "label": "Mobile margin top"
        },
        "header_transparent": {
          "content": "Alternative header"
        },
        "transparent_menu": {
          "label": "Custom Pages",
          "info": "Pick a menu linked to the pages where you want to apply the alternative header."
        },
        "header_sticky": {
          "content": "Sticky header"
        },
        "header_highlight": {
          "content": "Menu item highlight"
        },
        "enable_item_highlight": {
          "label": "Enable menu item highlight"
        },
        "item_highlight_text": {
          "label": "Menu item highlight text"
        },
        "item_highlight_position": {
          "label": "Menu item highlight position",
          "options__1": {
            "label": "First menu item"
          },
          "options__2": {
            "label": "Second menu item"
          },
          "options__3": {
            "label": "Third menu item"
          },
          "options__4": {
            "label": "Fourth menu item"
          },
          "options__5": {
            "label": "Fifth menu item"
          },
          "options__6": {
            "label": "Sixth menu item"
          },
          "options__7": {
            "label": "Seventh menu item"
          },
          "options__8": {
            "label": "Eight menu item"
          },
          "options__9": {
            "label": "Ninth menu item"
          },
          "options__10": {
            "label": "Tenth menu item"
          }
        },
        "adjust_item_highlight_position": {
          "label": "Adjust item highlight position"
        },
        "item_highlight_background_color": {
          "label": "Highlight background color"
        },
        "item_highlight_color": {
          "label": "Highlight text color"
        },
        "margin_bottom": {
          "label": "Bottom margin"
        },
        "header_icons_deco": {
          "content": "Header icons decoration"
        },
        "header_icons_decoration": {
          "label": "Icons decoration",
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "Circle"
          },
          "options__3": {
            "label": "Line"
          }
        },
        "header_mobile": {
          "content": "Mobile Layout"
        },
        "mobile_desktop_header_layout": {
          "label": "Mobile header layout",
          "options__1": {
            "label": "Center"
          },
          "options__2": {
            "label": "Left"
          }
        }
      }
    },
    "image-banner": {
      "name": "Image banner",
      "settings": {
        "image": {
          "label": "Image"
        },
        "image_overlay_opacity": {
          "label": "Image overlay opacity",
          "info": "You can change opacity color globaly from the [theme settings](\/editor?context=theme&category=colors)."
        },
        "image_height": {
          "label": "Banner height",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top left"
          },
          "options__2": {
            "label": "Top center"
          },
          "options__3": {
            "label": "Top right"
          },
          "options__4": {
            "label": "Middle left"
          },
          "options__5": {
            "label": "Middle center"
          },
          "options__6": {
            "label": "Middle right"
          },
          "options__7": {
            "label": "Bottom left"
          },
          "options__8": {
            "label": "Bottom center"
          },
          "options__9": {
            "label": "Bottom right"
          },
          "label": "Desktop content position"
        },
        "show_text_box": {
          "label": "Show text box on desktop"
        },
        "box_padding_top": {
          "label": "Text box top padding"
        },
        "box_padding_bottom": {
          "label": "Text box bottom padding"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content alignment"
        },
        "show_image_circle": {
          "label": "Hide Image Circle"
        },
        "ignore_image_circle_animation": {
          "label": "Deactivate image circle animation"
        },
        "color_scheme": {
          "info": "Visible when container displayed."
        },
        "header": {
          "content": "Mobile layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile content alignment"
        },
        "stack_images_on_mobile": {
          "label": "Stack images on mobile"
        },
        "show_text_below": {
          "label": "Show container on mobile"
        },
        "adapt_height_first_image": {
          "label": "Adapt section height to first image size",
          "info": "Overwrites image banner height setting when checked."
        }
      },
      "blocks": {
        "image": {
          "name": "Circle image",
          "settings": {
            "image": {
              "label": "Image"
            }
          }
        },
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading",
              "info": "Italicize an important word in your heading to make it stand out, then choose a highlight option from the choices below."
            },
            "word_animation_color" : {
              "label": "Highlight color"
            },
            "highlight_option": {
              "options__1": {
                "label": "Italic"
              },
              "options__2": {
                "label": "Underline One"
              },
              "options__3": {
                "label": "Underline Two"
              },
              "options__4": {
                "label": "Circle One"
              },
              "options__5": {
                "label": "Circle Two"
              },
              "label": "Highlight option"
            }
          }
        },
        "caption": {
          "name": "Subheading",
          "settings": {
            "heading": {
              "label": "Subheading"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Description"
            },
            "text_style": {
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              },
              "label": "Text style"
            }
          }
        },
        "countdown-timer": {
          "name": "Countdown timer",
          "settings": {
            "countdown_small": {
              "label": "Make countdown small"
            }
          }
        },
        "buttons": {
          "name": "Buttons",
          "settings": {
            "button_label_1": {
              "label": "First button label",
              "info": "Leave the label blank to hide the button."
            },
            "button_link_1": {
              "label": "First button link"
            },
            "button_style_secondary_1": {
              "label": "Use outline button style"
            },
            "button_label_2": {
              "label": "Second button label",
              "info": "Leave the label blank to hide the button."
            },
            "button_link_2": {
              "label": "Second button link"
            },
            "button_style_secondary_2": {
              "label": "Use outline button style"
            }
          }
        }
      },
      "presets": {
        "name": "Image banner"
      }
    },
    "image-banner-with-featured-collection": {
      "name": "Image banner collection",
      "settings": {
        "image": {
          "label": "Image"
        },
        "hide_image": {
          "label": "Use solid color background"
        },
        "image_overlay_opacity": {
          "label": "Image overlay opacity",
          "info": "You can change opacity color globaly from the [theme settings](\/editor?context=theme&category=colors)."
        },
        "content_text_color": {
          "label": "Content text color"
        },
        "content_button_background_color": {
          "label": "Content background color"
        },
        "content_button_text_color": {
          "label": "Content text color"
        },
        "image_height": {
          "label": "Banner height"
        },
        "image_height_mobile": {
          "label": "Banner height"
        },
        "full_width_banner": {
          "label": "Make layout full width"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content position"
        },
        "show_text_box": {
          "label": "Show text box on desktop"
        },
        "box_padding_top": {
          "label": "Text box top padding"
        },
        "box_padding_bottom": {
          "label": "Text box bottom padding"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content alignment"
        },
        "header_mobile_image_banner": {
          "content": "Mobile layout - Image banner"
        },
        "image_mobile": {
          "label": "Image"
        },
        "image_overlay_opacity_mobile": {
          "label": "Image overlay opacity",
          "info": "You can change opacity color globaly from the [theme settings](\/editor?context=theme&category=colors)."
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile content alignment"
        },
        "stack_images_on_mobile": {
          "label": "Stack images on mobile"
        },
        "show_text_below": {
          "label": "Show container on mobile"
        },
        "adapt_height_first_image": {
          "label": "Adapt section height to first image size",
          "info": "Overwrites image banner height setting when checked."
        },
        "header_featured_collection": {
          "content": "Featured collection"
        },
        "enable_collection": {
          "label": "Enable featured collection"
        },
        "collection": {
          "label": "Collection"
        },
        "products_to_show": {
          "label": "Maximum products to show"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "show_view_all": {
          "label": "Enable \"View all\" if collection has more products than shown"
        },
        "view_all_style": {
          "label": "\"View all\" style",
          "options__1": {
            "label": "Link"
          },
          "options__2": {
            "label": "Button"
          }
        },
        "enable_desktop_slider": {
          "label": "Enable carousel on desktop"
        },
        "full_width": {
          "label": "Make products full width"
        },
        "header": {
          "content": "Product card"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Show vendor"
        },
        "show_rating": {
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app."
        },
        "enable_quick_buy": {
          "label": "Enable quick view",
          "info": "Optimal with popup or drawer cart type."
        },
        "quick_add_position": {
          "label": "Quick view position",
          "options__1": {
            "label": "Overlay"
          },
          "options__2": {
            "label": "Default"
          }
        },
        "header_overlap": {
          "content": "Overlap"
        },
        "desktop_margin_top": {
          "label": "Desktop Overlap"
        },
        "mobile_margin_top": {
          "label": "Mobile overlap"
        },
        "header_mobile_featured_collection": {
          "content": "Mobile layout - Featured collection"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 column"
          },
          "options__2": {
            "label": "2 columns"
          }
        },
        "disable_quick_add": {
          "label": "Disable quick add on mobile when enabled on desktop"
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "caption": {
          "name": "Subheading",
          "settings": {
            "heading": {
              "label": "Subheading"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Description"
            },
            "text_style": {
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              },
              "label": "Text style"
            }
          }
        },
        "buttons": {
          "name": "Buttons",
          "settings": {
            "button_label_1": {
              "label": "First button label",
              "info": "Leave the label blank to hide the button."
            },
            "button_link_1": {
              "label": "First button link"
            }
          }
        }
      },
      "presets": {
        "name": "Image banner collection"
      }
    },
    "image-banner-with-collections": {
      "name": "Image banner with collection list",
      "settings": {
        "image": {
          "label": "Image"
        },
        "hide_image": {
          "label": "Use solid color background"
        },
        "image_overlay_opacity": {
          "label": "Image overlay opacity",
          "info": "You can change opacity color globaly from the [theme settings](\/editor?context=theme&category=colors)."
        },
        "content_text_color": {
          "label": "Content text color"
        },
        "content_button_background_color": {
          "label": "Content background color"
        },
        "content_button_text_color": {
          "label": "Content text color"
        },
        "image_height": {
          "label": "Banner height"
        },
        "image_height_mobile": {
          "label": "Banner height"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content position"
        },
        "show_text_box": {
          "label": "Show text box on desktop"
        },
        "box_padding_top": {
          "label": "Text box top padding"
        },
        "box_padding_bottom": {
          "label": "Text box bottom padding"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content alignment"
        },
        "header_mobile_image_banner": {
          "content": "Mobile layout"
        },
        "image_mobile": {
          "label": "Image"
        },
        "image_overlay_opacity_mobile": {
          "label": "Image overlay opacity",
          "info": "You can change opacity color globaly from the [theme settings](\/editor?context=theme&category=colors)."
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile content alignment"
        },
        "stack_images_on_mobile": {
          "label": "Stack images on mobile"
        },
        "show_text_below": {
          "label": "Show container on mobile"
        },
        "adapt_height_first_image": {
          "label": "Adapt section height to first image size",
          "info": "Overwrites image banner height setting when checked."
        },
        "header_featured_collection": {
          "content": "Featured collection"
        },
        "enable_collection": {
          "label": "Enable featured collection"
        },
        "collection": {
          "label": "Collection"
        },
        "products_to_show": {
          "label": "Maximum products to show"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "show_view_all": {
          "label": "Enable \"View all\" if collection has more products than shown"
        },
        "view_all_style": {
          "label": "\"View all\" style",
          "options__1": {
            "label": "Link"
          },
          "options__2": {
            "label": "Button"
          }
        },
        "enable_desktop_slider": {
          "label": "Enable carousel on desktop"
        },
        "full_width": {
          "label": "Make products full width"
        },
        "header": {
          "content": "Product card"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Show vendor"
        },
        "show_rating": {
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app."
        },
        "enable_quick_buy": {
          "label": "Enable quick view",
          "info": "Optimal with popup or drawer cart type."
        },
        "quick_add_position": {
          "label": "Quick view position",
          "options__1": {
            "label": "Overlay"
          },
          "options__2": {
            "label": "Default"
          }
        },
        "header_overlap": {
          "content": "Overlap"
        },
        "desktop_margin_top": {
          "label": "Desktop Overlap"
        },
        "mobile_margin_top": {
          "label": "Mobile overlap"
        },
        "header_mobile_featured_collection": {
          "content": "Mobile layout - Featured collection"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 column"
          },
          "options__2": {
            "label": "2 columns"
          }
        },
        "disable_quick_add": {
          "label": "Disable quick add on mobile when enabled on desktop"
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "heading": {
          "label": "Heading",
          "info": "Italicize an important word in your heading to make it stand out, then choose a highlight option from the choices below."
        },
        "caption": {
          "label": "Caption"
        },
        "text": {
          "label": "Description"
        },
        "text_style": {
          "options__1": {
            "label": "Body"
          },
          "options__2": {
            "label": "Subtitle"
          },
          "options__3": {
            "label": "Uppercase"
          },
          "label": "Text style"
        },
        "button_label_1": {
          "label": "First button label",
          "info": "Leave the label blank to hide the button."
        },
        "button_link_1": {
          "label": "First button link"
        },
        "highlight_option": {
          "options__1": {
            "label": "Italic"
          },
          "options__2": {
            "label": "Underline One"
          },
          "options__3": {
            "label": "Underline Two"
          },
          "options__4": {
            "label": "Circle One"
          },
          "options__5": {
            "label": "Circle Two"
          },
          "label": "Highlight option"
         },
         "word_animation_color" : {
            "label": "Highlight color"
         },
         "header_collections": {
            "content": "Collections"
         },
         "disable_arrow_mobile" : {
            "label": "Hide slider arrows on mobile"
         }
      },
      "blocks": {
        "featured_collection": {
          "name": "Collection",
          "settings": {
            "collection": {
              "label": "Collection"
            }
          }
        }
      },
      "presets": {
        "name": "Image banner with collection list"
      }
    },
    "banner-two-columns": {
      "name": "Banner columns",
      "settings": {
        "banner_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Simple"
          },
          "options__3": {
            "label": "Additional"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "full_width": {
          "label": "Make layout full width"
        },
        "accessibility": {
          "content": "Accessibility",
          "label": "Slideshow description",
          "info": "Describe the slideshow for customers using screen readers."
        }
      },
      "blocks": {
        "slide": {
          "name": "Banner",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "heading": {
              "label": "Heading"
            },
            "subheading": {
              "label": "Text"
            },
            "link_label": {
              "label": "Link label"
            },
            "link": {
              "label": "Link"
            },
            "box_align": {
              "label": "Desktop content position",
              "info": "Position is automatically optimized for mobile.",
              "options__1": {
                "label": "Top left"
              },
              "options__2": {
                "label": "Top center"
              },
              "options__3": {
                "label": "Top right"
              },
              "options__4": {
                "label": "Middle left"
              },
              "options__5": {
                "label": "Middle center"
              },
              "options__6": {
                "label": "Middle right"
              },
              "options__7": {
                "label": "Bottom left"
              },
              "options__8": {
                "label": "Bottom center"
              },
              "options__9": {
                "label": "Bottom right"
              }
            },
            "show_text_box": {
              "label": "Show text box on desktop"
            },
            "text_alignment": {
              "label": "Desktop content alignment",
              "option_1": {
                "label": "Left"
              },
              "option_2": {
                "label": "Center"
              },
              "option_3": {
                "label": "Right"
              }
            },
            "image_overlay_opacity": {
              "label": "Image overlay opacity"
            },
            "color_scheme": {
              "info": "Visible when container displayed."
            },
            "text_alignment_mobile": {
              "label": "Mobile content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              },
              "options__3": {
                "label": "Right"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Banner columns"
      }
    },
    "sticky-banners": {
      "name": "Funky Banners",
      "settings": {
        "banner_height": {
          "label": "Image height",
          "options__1": {
            "label": "Adapt to first image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "full_width": {
          "label": "Make layout full width"
        },
        "accessibility": {
          "content": "Accessibility",
          "label": "Slideshow description",
          "info": "Describe the slideshow for customers using screen readers."
        }
      },
      "blocks": {
        "slide": {
          "name": "Banner",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "heading": {
              "label": "Heading"
            },
            "subheading": {
              "label": "Text"
            },
            "link_label": {
              "label": "Link label"
            },
            "link": {
              "label": "Link"
            },
            "box_align": {
              "label": "Desktop content position",
              "info": "Position is automatically optimized for mobile.",
              "options__1": {
                "label": "Top left"
              },
              "options__2": {
                "label": "Top center"
              },
              "options__3": {
                "label": "Top right"
              },
              "options__4": {
                "label": "Middle left"
              },
              "options__5": {
                "label": "Middle center"
              },
              "options__6": {
                "label": "Middle right"
              },
              "options__7": {
                "label": "Bottom left"
              },
              "options__8": {
                "label": "Bottom center"
              },
              "options__9": {
                "label": "Bottom right"
              }
            },
            "show_text_box": {
              "label": "Show text box on desktop"
            },
            "text_alignment": {
              "label": "Desktop content alignment",
              "option_1": {
                "label": "Left"
              },
              "option_2": {
                "label": "Center"
              },
              "option_3": {
                "label": "Right"
              }
            },
            "image_overlay_opacity": {
              "label": "Image overlay opacity"
            },
            "color_scheme": {
              "info": "Visible when container displayed."
            },
            "text_alignment_mobile": {
              "label": "Mobile content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              },
              "options__3": {
                "label": "Right"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Sticky banners"
      }
    },
    "image-with-text": {
      "name": "Image with text",
      "settings": {
        "image": {
          "label": "Image"
        },
        "caption": {
          "label": "Vertical caption"
        },
        "height": {
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          },
          "label": "Image height"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "label": "Desktop image width",
          "info": "Image is automatically optimized for mobile."
        },
        "layout": {
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Image second"
          },
          "label": "Desktop image placement",
          "info": "Image first is the default mobile layout."
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content alignment"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Middle"
          },
          "options__3": {
            "label": "Bottom"
          },
          "label": "Desktop content position"
        },
        "content_layout": {
          "options__1": {
            "label": "No overlap"
          },
          "options__2": {
            "label": "Overlap"
          },
          "label": "Content layout"
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile content alignment"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        },
        "full_width": {
          "label": "Make layout boxed"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "caption": {
          "name": "Subheading",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Default"
              },
              "options__2": {
                "label": "Uppercase"
              }
            },
            "caption_size": {
              "label": "Text size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              }
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Content"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              }
            }
          }
        },
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Image",
              "info": "For the optimal appearance, please ensure the image dimensions are 300x300 pixels."
            },
            "mobile_disable_image": {
              "label": "Disable image on mobile"
            }
          }
        },
        "button": {
          "name": "Button",
          "settings": {
            "button_label": {
              "label": "Button label",
              "info": "Leave the label blank to hide the button."
            },
            "button_link": {
              "label": "Button link"
            }
          }
        }
      },
      "presets": {
        "name": "Image with text"
      }
    },
    "image-hotspots": {
      "name": "Image hotspots",
      "settings": {
        "image": {
          "label": "Image"
        },
        "image_size": {
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "label": "Image size"
        },
        "heading": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "text_style": {
          "label": "Subheading text style",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Uppercase"
          }
        },
        "text_size": {
          "label": "Subheading text size",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "product": {
          "label": "Product"
        },
        "layout": {
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Text first"
          },
          "label": "Image placement"
        },
        "layout_mobile": {
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Text first"
          },
          "label": "Image placement"
        },
        "header_colors": {
          "content": "Colors"
        },
        "tooltip_background_color": {
          "label": "Tooltip background color"
        },
        "full_width": {
          "label": "Make layout full width"
        },
        "hide_content": {
          "label": "Hide content"
        }
      },
      "blocks": {
        "tooltip": {
          "name": "Tooltip",
          "settings": {
            "title": {
              "label": "Title"
            },
            "link": {
              "label": "Product link"
            },
            "product": {
              "label": "Product"
            },
            "content": {
              "label": "Content"
            },
            "top": {
              "label": "Top position"
            },
            "left": {
              "label": "Left position"
            }
          }
        }
      },
      "presets": {
        "name": "Image hotspots"
      }
    },
    "quick-info-bar": {
      "name": "Quick info bar",
      "settings": {
        "first_column_content": {
          "content": "First column content"
        },
        "image_1": {
          "label": "Image"
        },
        "heading_1": {
          "label": "Heading"
        },
        "caption_1": {
          "label": "Subheading"
        },
        "second_column_content": {
          "content": "Second column content"
        },
        "image_2": {
          "label": "Image"
        },
        "heading_2": {
          "label": "Heading"
        },
        "caption_2": {
          "label": "Subheading"
        },
        "third_column_content": {
          "content": "Third column content"
        },
        "image_3": {
          "label": "Image"
        },
        "heading_3": {
          "label": "Heading"
        },
        "caption_3": {
          "label": "Subheading"
        },
        "fourth_column_content": {
          "content": "Fourth column content"
        },
        "image_4": {
          "label": "Image"
        },
        "heading_4": {
          "label": "Heading"
        },
        "caption_4": {
          "label": "Subheading"
        },
        "info_bar_options": {
          "content": "Quick info bar options"
        },
        "image_size": {
          "options_1": {
            "label": "Small"
          },
          "options_2": {
            "label": "Medium"
          },
          "options_3": {
            "label": "Large"
          },
          "label": "Image size"
        },
        "desktop_bar_position": {
          "options_1": {
            "label": "Right"
          },
          "options_2": {
            "label": "Left"
          },
          "options_3": {
            "label": "Center"
          },
          "options_4": {
            "label": "Center without overlap"
          },
          "label": "Desktop bar position"
        },
        "bar_full_width": {
          "label": "Show bar in full width"
        },
        "add_border": {
          "label": "Add border to section"
        },
        "full_width_background": {
          "label": "Make section background full width"
        }
      },
      "presets": {
        "name": "Quick info bar"
      }
    },
    "multirow": {
      "name": "Multirow",
      "settings": {
        "image": {
          "label": "Image"
        },
        "image_height": {
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          },
          "label": "Image height"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "label": "Desktop image width",
          "info": "Image is automatically optimized for mobile."
        },
        "button_style": {
          "options__1": {
            "label": "Solid button"
          },
          "options__2": {
            "label": "Outline button"
          },
          "label": "Button style"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content alignment"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Middle"
          },
          "options__3": {
            "label": "Bottom"
          },
          "label": "Desktop content position",
          "info": "Position is automatically optimized for mobile."
        },
        "image_layout": {
          "options__1": {
            "label": "Alternate from left"
          },
          "options__2": {
            "label": "Alternate from right"
          },
          "options__3": {
            "label": "Aligned left"
          },
          "options__4": {
            "label": "Aligned right"
          },
          "label": "Desktop image placement",
          "info": "Placement is automatically optimized for mobile."
        },
        "ignore_row_spacing": {
          "label": "Ignore space between rows"
        },
        "full_width": {
          "label": "Make layout full width"
        },
        "content_gradient_position": {
          "options__1": {
            "label": "Top left"
          },
          "options__2": {
            "label": "Top right"
          },
          "options__3": {
            "label": "Bottom left"
          },
          "options__4": {
            "label": "Bottom right"
          },
          "label": "Content decoration position"
        },
        "ignore_content_gradient": {
          "label": "Ignore content decoration"
        },
        "ignore_content_overlap": {
          "label": "Ignore content overlap"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile content alignment"
        },
        "header_mobile": {
          "content": "Mobile layout"
        }
      },
      "blocks": {
        "row": {
          "name": "Row",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "heading": {
              "label": "Heading"
            },
            "heading_size": {
              "options__1": {
                "label": "Extra large"
              },
              "options__2": {
                "label": "Large"
              },
              "options__3": {
                "label": "Medium"
              },
              "label": "Heading size"
            },
            "text": {
              "label": "Text"
            },
            "button_label": {
              "label": "Button label"
            },
            "button_link": {
              "label": "Button link"
            }
          }
        }
      },
      "presets": {
        "name": "Multirow"
      }
    },
    "main-account": {
      "name": "Account"
    },
    "main-activate-account": {
      "name": "Account activation"
    },
    "main-addresses": {
      "name": "Addresses"
    },
    "main-article": {
      "name": "Blog post",
      "blocks": {
        "featured_image": {
          "name": "Featured image",
          "settings": {
            "image_height": {
              "label": "Featured image height",
              "options__1": {
                "label": "Adapt to image"
              },
              "options__2": {
                "label": "Small"
              },
              "options__3": {
                "label": "Medium"
              },
              "options__4": {
                "label": "Large"
              },
              "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https:\/\/help.shopify.com\/manual\/shopify-admin\/productivity-tools\/image-editor#understanding-image-aspect-ratio)"
            }
          }
        },
        "title": {
          "name": "Title",
          "settings": {
            "blog_show_date": {
              "label": "Show aate"
            },
            "blog_show_author": {
              "label": "Show author"
            }
          }
        },
        "content": {
          "name": "Content"
        },
        "tags": {
          "name": "Tags"
        },
        "buttons": {
          "name": "Prev\/Next post buttons"
        },
        "share": {
          "name": "Share",
          "settings": {
            "text": {
              "label": "Text"
            },
            "featured_image_info": {
              "content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/images\/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "A store title and description are included with the preview image. [Learn more](https:\/\/help.shopify.com\/manual\/promoting-marketing\/seo\/adding-keywords#set-a-title-and-description-for-your-online-store)."
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blog posts",
      "settings": {
        "header": {
          "content": "Blog post card"
        },
        "make_first_post_featured": {
          "label": "Feature the first blog post at the top"
        },
        "show_image": {
          "label": "Show featured image"
        },
        "tags": {
          "label": "Show tags"
        },
        "tag_label": {
          "label": "Filter by tag"
        },
        "tag_default": {
          "label": "All posts"
        },
        "show_page_title": {
          "label": "Show page title"
        },
        "show_date": {
          "label": "Show date"
        },
        "show_author": {
          "label": "Show author"
        },
        "paragraph": {
          "content": "Change excerpts by editing your blog posts. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/blogs\/writing-blogs#display-an-excerpt-from-a-blog-post)"
        },
        "layout": {
          "label": "Desktop layout",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Column"
          },
          "options__3": {
            "label": "3 Column"
          },
          "info": "Posts are stacked on mobile."
        },
        "blog_style": {
          "label": "Blog style",
          "options__1": {
            "label": "Modern"
          },
          "options__2": {
            "label": "Simple"
          },
          "options__3": {
            "label": "Elegant"
          }
        },
        "image_height": {
          "label": "Featured image height",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          },
          "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https:\/\/help.shopify.com\/manual\/shopify-admin\/productivity-tools\/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-cart-footer": {
      "name": "Subtotal",
      "blocks": {
        "subtotal": {
          "name": "Subtotal price"
        },
        "buttons": {
          "name": "Checkout button"
        },
        "text-with-image": {
          "name": "Text with image",
          "settings": {
            "image": {
              "label": "Image"
            },
            "image_width": {
              "label": "Image width"
            },
            "hide_image": {
              "label": "Hide image"
            },
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Text",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Uppercase"
              }
            },
            "centered_content": {
              "label": "Center content"
            }
          }
        }
      }
    },
    "main-cart-items": {
      "name": "Items",
      "settings": {
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      }
    },
    "main-404": {
      "name": "404 Page",
      "settings": {
        "heading": {
          "label": "Heading"
        },
        "text": {
          "label": "Text"
        },
        "image": {
          "label": "Image"
        }
      }
    },
    "main-collection-banner": {
      "name": "Main collection banner",
      "settings": {
        "paragraph": {
          "content": "Add a description or image by editing your collection. [Learn more](https:\/\/help.shopify.com\/manual\/products\/collections\/collection-layout)"
        },
        "show_collection_description": {
          "label": "Show collection description"
        },
        "show_breadcrumbs": {
          "label": "Show breadcrumbs"
        },
        "collection_style": {
          "label": "Style",
          "options__1": {
            "label": "Two columns"
          },
          "options__2": {
            "label": "Color background"
          },
          "options__3": {
            "label": "Cover"
          }
        },
        "desktop_content_alignment": {
          "label": "Alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "header_style_three": {
          "content": "Cover style opacity"
        },
        "image_height": {
          "label": "Banner height",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "show_text_box": {
          "label": "Show text box"
        },
        "text_box_color": {
          "options__1": {
            "label": "Light"
          },
          "options__2": {
            "label": "Dark"
          },
          "options__3": {
            "label": "Light"
          },
          "label": "Text box color scheme"
        },
        "header_style_one_two": {
          "content": "Two Columns\/Color background"
        },
        "banner_background_color": {
          "label": "Background color"
        },
        "padding_heading": {
          "content": "Padding"
        },
        "all_products_collection_header": {
          "content": "All products collection",
          "info": "Here you can set image and heading for the default all products collection"
        },
        "image": {
          "label": "Image"
        },
        "fallback_heading": {
          "label": "Heading"
        }
      },
      "presets": {
        "name": "Main collection banner"
      }
    },
    "main-collection-product-grid": {
      "name": "Product grid",
      "settings": {
        "header__layout": {
          "content": "Layout"
        },
        "collection_layout": {
          "label": "Product grid layout",
          "options__1": {
            "label": "Boxed"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "product_card_style": {
          "label": "Product card style",
          "options__1": {
            "label": "Modern"
          },
          "options__3": {
            "label": "Elegant"
          }
        },
        "add_border": {
          "label": "Add border to product image"
        },
        "products_per_page": {
          "label": "Products per page"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "pagination": {
          "label": "Pagination style",
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Load more button"
          }
        },
        "load_button": {
          "label": "Load button"
        },
        "enable_filtering": {
          "label": "Enable filtering",
          "info": "Customize filters with the search and discovery app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/search-and-discovery\/filters)"
        },
        "enable_switcher": {
          "label": "Enable switcher"
        },
        "filter_type": {
          "label": "Desktop filter layout",
          "options__1": {
            "label": "Horizontal"
          },
          "options__2": {
            "label": "Sticky Sidebar"
          },
          "options__3": {
            "label": "Drawer"
          },
          "info": "Drawer is the default mobile layout."
        },
        "open_filter": {
          "label": "Vertical filter style",
          "options__1": {
            "label": "First open"
          },
          "options__2": {
            "label": "All open"
          }
        },
        "enable_sorting": {
          "label": "Enable sorting"
        },
        "filter_layout": {
          "label": "Filter layout",
          "options__1": {
            "label": "Boxed"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Show vendor"
        },
        "show_rating": {
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/theme-structure\/page-types#product-grid-section-settings)"
        },
        "header__1": {
          "content": "Filtering and sorting"
        },
        "header__3": {
          "content": "Product card"
        },
        "enable_tags": {
          "label": "Enable filtering",
          "info": "Customize filters with the search and discovery app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/search-and-discovery\/filters)"
        },
        "enable_quick_buy": {
          "label": "Enable quick view",
          "info": "Optimal with popup or drawer cart type."
        },
        "enable_quick_add": {
          "label": "Enable quick add to cart",
        },
        "list_color_variants_in_collection": {
          "label": "Show product color variants in collection",
          "info": "Make sure you set featured images for all your product variant"
        },
        "list_size_variants_in_collection": {
          "label": "Show product size variants in collection"
        },
        "hide_unavailable": {
          "label": "Hide unavailable and sold out products"
        },
        "quick_add_position": {
          "label": "Quick view position",
          "options__1": {
            "label": "Overlay"
          },
          "options__2": {
            "label": "Default"
          }
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 column"
          },
          "options__2": {
            "label": "2 columns"
          }
        },
        "disable_quick_add": {
          "label": "Disable quick add on mobile when enabled on desktop"
        },
        "product_count": {
          "label": "Enable Product Count",
          "info": "The number doesn't include product variants"
        }
      },
      "blocks": {
        "promo_row": {
          "name": "Promo Row",
          "settings": {
            "text": {
              "label": "Place promo after product:",
              "info": "Enter a whole number"
            },
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "heading": {
              "label": "Heading"
            },
            "subheading": {
              "label": "Text"
            },
            "link_label": {
              "label": "Link label"
            },
            "link": {
              "label": "Link"
            },
            "banner_height": {
              "label": "Image height",
              "options__1": {
                "label": "Adapt to first image"
              },
              "options__2": {
                "label": "Small"
              },
              "options__3": {
                "label": "Medium"
              },
              "options__4": {
                "label": "Large"
              }
            },
            "banner_layout": {
              "label": "Banner layout",
              "options__1": {
                "label": "Row"
              },
              "options__2": {
                "label": "Grid"
              }
            },
            "show_text_box": {
              "label": "Show text box"
            },
            "image_overlay_opacity": {
              "label": "Image overlay opacity"
            }
          }
        }
      }
    },
    "main-list-collections": {
      "name": "Collections list page",
      "settings": {
        "title": {
          "label": "Heading"
        },
        "sort": {
          "label": "Sort Collections By:",
          "options__1": {
            "label": "Alphabetically, A-Z"
          },
          "options__2": {
            "label": "Alphabetically, Z-A"
          },
          "options__3": {
            "label": "Date, New to Old"
          },
          "options__4": {
            "label": "Date, Old to New"
          },
          "options__5": {
            "label": "Product Count, High to Low"
          },
          "options__6": {
            "label": "Product Count, Low to High"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "info": "Add images by editing your collections. [Learn more](https:\/\/help.shopify.com\/manual\/products\/collections)"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 column"
          },
          "options__2": {
            "label": "2 columns"
          }
        }
      }
    },
    "main-login": {
      "name": "Login"
    },
    "main-order": {
      "name": "Order"
    },
    "main-page": {
      "name": "Page"
    },
    "main-password-footer": {
      "name": "Password footer"
    },
    "main-password-header": {
      "name": "Password header",
      "settings": {
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Edit your logo in theme settings."
        }
      }
    },
    "main-product": {
      "name": "Product information",
      "blocks": {
        "spacer": {
          "name": "Separator",
          "settings": {
            "margin_top": {
              "label": "Margin top"
            }
          }
        },
        "tabs": {
          "name": "Tabs",
          "settings": {
           "centered_tabs": {
             "label": "Center the tab navigation"
           },
            "remove_border_tabs": {
             "label": "Hide bottom border on tab navigation"
           },
           "header_item_1": {
             "content": "Item 1"
           },
           "header_item_2": {
             "content": "Item 2"
           },
           "header_item_3": {
             "content": "Item 3"
           },
           "heading_1": {
             "label": "Heading"
           },
           "heading_2": {
             "label": "Heading"
           },           
           "heading_3": {
             "label": "Heading"
           },
           "row_content_1": {
             "label": "Content"
           },
           "row_content_2": {
             "label": "Content"
           },           
           "row_content_3": {
             "label": "Content"
           }
         }
        },
        "payment_enable": {
          "name": "Payment icons",
          "settings": {
            "header": {
              "content": "Hide on mobile / desktop",
              "info": "Use case: different positioning for desktop and mobile. Example: position this block on desktop and hide it on mobile. Then use another instance of the same block to position on mobile and hide on desktop."
            },
           "hide_mobile": {
             "label": "Hide block on mobile"
           },
           "hide_desktop": {
             "label": "Hide block on desktop"
           },           
           "header_quick_view": {
             "content": "Quick view modal",
             "info": "Choose to hide or display block in the quick view modal"
           },
           "hide_quick_view": {
             "label": "Hide block in quick view"
           }
         }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            },
            "show_text_background": {
              "label": "Text padding"
            }
          }
        },
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Image"
            },
            "image_link": {
              "label": "Link"
            },
            "text_1": {
              "label": "Text"
            },
            "text_1_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "vendor": {
          "name": "Vendor"
        },
        "title": {
          "name": "Title",
          "settings": {
            "column": {
              "label": "Position",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Right"
              },
              "options__3": {
                "label": "Under gallery"
              }
            },
            "margin_top": {
              "label": "Margin top"
            }
          }
        },
        "price": {
          "name": "Price",
          "settings": {
            "text": {
              "label": "Text"
            }
          }
        },
        "waiting_list": {
          "name": "Waiting list",
          "settings": {
            "paragraph": {
              "content": "Waiting list signup for out of stock products"
            },
            "waiting_list_title": {
              "label": "Title"
            },
            "waiting_list_tagline": {
              "label": "Tagline"
            },
            "waiting_list_notice": {
              "label": "Notice"
            },
            "waiting_list_button": {
              "label": "Button"
            }
          }
        },
        "product-meta": {
          "name": "Inventory",
          "settings": {
            "header_inventory": {
              "content": "Inventory"
            },
            "show_product_inventory": {
              "label": "Show inventory"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__3": {
                "label": "Uppercase"
              }
            },
            "inventory_threshold": {
              "label": "Low inventory threshold",
              "info": "Choose 0 to always show in stock if available."
            },
            "show_inventory_quantity": {
              "label": "Show inventory count"
            },
            "header_sku": {
              "content": "SKU"
            },
            "show_product_sku": {
              "label": "Show SKU"
            },
            "header_rating": {
              "content": "Rating"
            },
            "info": {
              "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/product-ratings-and-reviews) to learn more"
            },
            "show_product_rating": {
              "label": "Show rating"
            }
          }
        },
        "dynamic_card_icons": {
          "name": "Dynamic icons",
          "settings": {
            "info": {
              "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/dynamic-icons-with-text) to learn more"
            },
            "card_metafield_text": {
              "label": "Text"
            },
            "card_metafield_key": {
              "label": "Card metafield key"
            },
            "card_metafield_image_size": {
              "label": "Image size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              },
              "options__4": {
                "label": "Extra large"
              }
            },
            "card_metafield_layout": {
              "label": "Layout",
              "options__1": {
                "label": "Wide"
              },
              "options__2": {
                "label": "Narrow"
              },
              "options__3": {
                "label": "Inline"
              }
            },
            "card_metafield_icon_title_font_weight": {
              "label": "Icon title font weight",
              "options__1": {
                "label": "Bold"
              },
              "options__2": {
                "label": "Normal"
              }
            },
            "card_metafield_border": {
              "label": "Image Border",
              "options__1": {
                "label": "Border"
              },
              "options__2": {
                "label": "No border"
              }
            },
            "card_metafield_enable_border_radius": {
              "label": "Enable border radius - circle"
            },
            "icons_tooltip": {
              "label": "Show icon title as a tooltip"
            }
          }
        },
        "inventory": {
          "name": "Inventory status",
          "settings": {
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            },
            "inventory_threshold": {
              "label": "Low inventory threshold",
              "info": "Choose 0 to always show in stock if available."
            },
            "show_inventory_quantity": {
              "label": "Show inventory count"
            }
          }
        },
        "quantity_selector": {
          "name": "Quantity selector"
        },
        "variant_picker": {
          "name": "Variant picker",
          "settings": {
            "picker_type": {
              "label": "Type",
              "options__1": {
                "label": "Dropdown"
              },
              "options__2": {
                "label": "Pills"
              },
              "options__3": {
                "label": "Simple"
              }
            }
          }
        },
        "countdown-timer": {
          "name": "Countdown Timer"
        },
        "buy_buttons": {
          "name": "Buy buttons",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Show dynamic checkout buttons",
              "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal. [Learn more](https:\/\/help.shopify.com\/manual\/using-themes\/change-the-layout\/dynamic-checkout)"
            },
            "show_gift_card_recipient": {
              "label": "Show recipient information form for gift cards",
              "info": "Allows buyers to send gift cards on a scheduled date along with a personal message. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/add-gift-card-recipient-fields)"
            },
            "hide_unavailable": {
              "label": "Hide unavailable and sold out variants"
            },
            "variant": {
              "content": "Variant picker"
            },
            "swatch_shape": {
              "label": "Swatch style",
              "info": "Check the documentation [Learn more](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/color-swatches)",
              "options__1": {
                "label": "Circle"
              },
              "options__2": {
                "label": "Square"
              },
              "options__3": {
                "label": "None"
              }
            }
          }
        },
        "pickup_availability": {
          "name": "Pickup availability"
        },
        "description": {
          "name": "Description"
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "delivery_estimator": {
          "name": "Delivery Estimator",
          "settings": {
            "info": {
              "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/delivery-estimator) to learn more"
            },
            "delivery_estimator_text": {
              "label": "Delivery estimator text"
            },
            "earliest_delivery": {
              "label": "Earliest delivery",
              "info": "Min number of days for delivery, e.g.: 2"
            },
            "latest_delivery": {
              "label": "Latest delivery",
              "info": "Max number of days for delivery, e.g.: 5"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "share": {
          "name": "Share",
          "settings": {
            "text": {
              "label": "Text"
            },
            "featured_image_info": {
              "content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/images\/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "A store title and description are included with the preview image. [Learn more](https:\/\/help.shopify.com\/manual\/promoting-marketing\/seo\/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "share_top_right_corner": {
              "label": "Position share to top right corner"
            }
          }
        },
        "custom_liquid": {
          "name": "Custom liquid",
          "settings": {
            "custom_liquid": {
              "label": "Custom liquid",
              "info": "Add app snippets or other liquid code to create advanced customizations."
            }
          }
        },
        "collapsible_tab": {
          "name": "Collapsible row",
          "settings": {
            "heading": {
              "info": "Include a heading that explains the content.",
              "label": "Heading"
            },
            "no_padding": {
              "label": "No padding"
            },
            "show_spacer": {
              "label": "Show separator"
            },
            "image": {
              "label": "Image"
            },
            "content": {
              "label": "Row content"
            },
            "page": {
              "label": "Row content from page"
            },
            "icon": {
              "label": "Icon",
              "options__1": {
                "label": "None"
              },
              "options__2": {
                "label": "Basket"
              },
              "options__3": {
                "label": "Bag Heart"
              },
              "options__4": {
                "label": "Box"
              },
              "options__5": {
                "label": "Box Heart"
              },
              "options__6": {
                "label": "Brush"
              },
              "options__7": {
                "label": "Calendar"
              },
              "options__8": {
                "label": "Chat Bubble"
              },
              "options__9": {
                "label": "Check Mark"
              },
              "options__10": {
                "label": "Clipboard"
              },
              "options__11": {
                "label": "Droplet"
              },
              "options__12": {
                "label": "Droplet Half"
              },
              "options__13": {
                "label": "Envelope"
              },
              "options__14": {
                "label": "Eye"
              },
              "options__15": {
                "label": "Eye Dropper"
              },
              "options__16": {
                "label": "Exclamation"
              },
              "options__17": {
                "label": "Gift"
              },
              "options__18": {
                "label": "Gem"
              },
              "options__19": {
                "label": "Globe"
              },
              "options__20": {
                "label": "Heart"
              },
              "options__21": {
                "label": "Headset"
              },
              "options__22": {
                "label": "List"
              },
              "options__23": {
                "label": "Smoothie"
              },
              "options__24": {
                "label": "Lock"
              },
              "options__25": {
                "label": "Magic"
              },
              "options__26": {
                "label": "Map Pin"
              },
              "options__27": {
                "label": "Cup"
              },
              "options__28": {
                "label": "Paw Print"
              },
              "options__29": {
                "label": "Shop"
              },
              "options__30": {
                "label": "Person"
              },
              "options__31": {
                "label": "Plane"
              },
              "options__32": {
                "label": "Plant"
              },
              "options__33": {
                "label": "Price Tag"
              },
              "options__34": {
                "label": "Fire"
              },
              "options__35": {
                "label": "Recycle"
              },
              "options__36": {
                "label": "Return"
              },
              "options__37": {
                "label": "Snowflake"
              },
              "options__38": {
                "label": "Star"
              },
              "options__39": {
                "label": "Stopwatch"
              },
              "options__40": {
                "label": "Tag"
              },
              "options__41": {
                "label": "Tree"
              },
              "options__42": {
                "label": "Thumb Up"
              },
              "options__43": {
                "label": "Truck"
              },
              "options__44": {
                "label": "Question Mark"
              }
            }
          }
        },
        "popup": {
          "name": "Pop-Up",
          "settings": {
            "link_label": {
              "label": "Link label"
            },
            "page": {
              "label": "Page"
            }
          }
        },
        "rating": {
          "name": "Product rating",
          "settings": {
            "paragraph": {
              "content": "To display a rating, add a product rating app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/theme-structure\/page-types#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Complementary products",
          "settings": {
            "paragraph": {
              "content": "To select complementary products, add the Search & Discovery app. [Learn more](https:\/\/manathemes.com\/docs\/flux-theme\/how-to-guides\/complementary-and-related-products)"
            },
            "heading": {
              "label": "Heading"
            },
            "make_collapsible_row": {
              "label": "Show as collapsible row"
            },
            "icon": {
              "info": "Visible when collapsible row is displayed."
            },
            "product_list_limit": {
              "label": "Maximum products to show"
            },
            "products_per_page": {
              "label": "Number of products per page"
            },
            "pagination_style": {
              "label": "Pagination style",
              "options": {
                "option_1": "Dots",
                "option_2": "Counter",
                "option_3": "Numbers"
              }
            },
            "product_card": {
              "heading": "Product card"
            },
            "image_ratio": {
              "label": "Image ratio",
              "options": {
                "option_1": "Portrait",
                "option_2": "Square"
              }
            },
            "enable_quick_add": {
              "label": "Enable add to cart button"
            },
            "columns": {
              "label": "Columns",
              "options": {
                "option_1": "One",
                "option_2": "Two"
              }
            }
          }
        },
        "ingredient_details": {
          "name": "Ingredient details",
          "settings": {
            "header_block_content": {
              "content": "Ingredient content"
            },
            "left_column_label": {
              "label": "Left column label"
            },
            "right_column_label": {
              "label": "Right column label"
            },
            "content": {
              "label": "Content",
              "info": "Separate label and value with a comma. Use SHIFT + ENTER to add a new row. Use a hyphen to indent rows. IMPORTANT: don't use headings and list styles"
            }
          }
        },
        "icon_with_text": {
          "name": "Icon with text",
          "settings": {
            "layout": {
              "label": "Layout",
              "options__1": {
                "label": "Horizontal"
              },
              "options__2": {
                "label": "Vertical"
              }
            },
            "content": {
              "label": "Content",
              "info": "Choose an icon or add an image for each column or row."
            },
            "heading": {
              "info": "Leave the heading label blank to hide the icon column."
            },
            "icon_1": {
              "label": "First icon"
            },
            "image_1": {
              "label": "First image"
            },
            "heading_1": {
              "label": "First heading"
            },
            "icon_2": {
              "label": "Second icon"
            },
            "image_2": {
              "label": "Second image"
            },
            "heading_2": {
              "label": "Second heading"
            },
            "icon_3": {
              "label": "Third icon"
            },
            "image_3": {
              "label": "Third image"
            },
            "heading_3": {
              "label": "Third heading"
            }
          }
        }
      },
      "settings": {
        "info": {
          "content": "Visit [theme documentation](https:\/\/manathemes.com\/docs\/flux-theme\/product-page\/) to learn more"
        },
        "header_layout": {
          "content": "Layout"
        },
        "header": {
          "content": "Media",
          "info": "Learn more about [media types.](https:\/\/help.shopify.com\/manual\/products\/product-media)"
        },
        "enable_full_width": {
          "label": "Make layout full width"
        },
        "enable_sticky_info": {
          "label": "Enable sticky content on desktop"
        },
        "color_scheme": {
          "label": "Product information color scheme"
        },
        "enable_info_padding": {
          "label": "Enable product information box padding"
        },
        "product_gallery_width": {
          "label": "Product gallery width"
        },
        "product_layout": {
          "label": "Desktop layout",
          "options__1": {
            "label": "Layout 1"
          },
          "options__2": {
            "label": "Layout 2"
          },
          "options__3": {
            "label": "Layout 3"
          },
          "options__4": {
            "label": "Layout 4"
          },
          "options__5": {
            "label": "Layout 5"
          }
        },
        "product_background_color": {
          "label": "Background color"
        },
        "title": {
          "label": "Heading"
        },
        "gallery_layout": {
          "label": "Desktop layout",
          "options__1": {
            "label": "Simple"
          },
          "options__2": {
            "label": "Grid"
          },
          "options__3": {
            "label": "Thumbnails left"
          },
          "options__4": {
            "label": "Thumbnails bottom"
          },
          "options__5": {
            "label": "Thumbnails right"
          },
          "options__6": {
            "label": "Slide"
          }
        },
        "constrain_to_viewport": {
          "label": "Constrain media to screen height"
        },
        "media_size": {
          "label": "Desktop media width",
          "info": "Media is automatically optimized for mobile.",
          "options__1": {
            "label": "Medium"
          },
          "options__2": {
            "label": "Large"
          }
        },
        "image_zoom": {
          "label": "Image zoom",
          "info": "Click and hover defaults to open lightbox on mobile.",
          "options__1": {
            "label": "Open lightbox"
          },
          "options__2": {
            "label": "Click and hover"
          },
          "options__3": {
            "label": "No zoom"
          }
        },
        "media_style": {
          "label": "Media and info style",
          "options__1": {
            "label": "Style one"
          },
          "options__2": {
            "label": "Style two"
          }
        },
        "media_position": {
          "label": "Desktop media position",
          "info": "Position is automatically optimized for mobile.",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Right"
          }
        },
        "media_fit": {
          "label": "Media fit",
          "options__1": {
            "label": "Original"
          },
          "options__2": {
            "label": "Fill"
          }
        },
        "thumbnail_size": {
          "label": "Thumbnail Size",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "under_gallery": {
          "label": "Under gallery content position",
          "options__1": {
            "label": "First"
          },
          "options__2": {
            "label": "Second"
          }
        },
        "mobile_thumbnails": {
          "label": "Gallery layout",
          "options__1": {
            "label": "2 Columns"
          },
          "options__2": {
            "label": "Show thumbnails"
          },
          "options__3": {
            "label": "Hide thumbnails"
          }
        },
        "hide_variants": {
          "label": "Hide other variants’ media after selecting a variant"
        },
        "enable_video_looping": {
          "label": "Enable video looping"
        },
        "header_in_stock_colors": {
          "content": "In stock colors"
        },
        "in_stock_background_color": {
          "label": "In stock background color"
        },
        "in_stock_color": {
          "label": "In stock text color"
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_quantity_selector": {
          "content": "Quantity selector"
        },
        "quantity_selector": {
          "label": "Quantity selector border radius"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      }
    },
    "main-register": {
      "name": "Registration"
    },
    "main-reset-password": {
      "name": "Password reset"
    },
    "main-search": {
      "name": "Search results",
      "settings": {
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Show vendor"
        },
        "show_rating": {
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/theme-structure\/page-types#search-results-section-settings)"
        },
        "header__1": {
          "content": "Product card"
        },
        "header__2": {
          "content": "Blog card",
          "info": "Blog card styles also apply to page cards in search results. To change card styles update your theme settings."
        },
        "header__3": {
          "content": "Page card"
        },
        "show_article_posts": {
          "label": "Show article cards"
        },
        "show_page_posts": {
          "label": "Show page cards"
        },
        "article_show_date": {
          "label": "Show date"
        },
        "article_show_author": {
          "label": "Show author"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        }
      }
    },
    "quick-order-list": {
      "name": "Quick order list",
      "settings": {
        "enable_card_background": {
          "label": "Enable card background"
        },
        "show_image": {
          "label": "Show image"
        },
        "show_sku": {
          "label": "Show sku"
        }
      },
      "presets": {
        "name": "Quick order list"
      }
    },
    "multicolumn": {
      "name": "Multicolumn",
      "settings": {
        "multicolumn_style": {
          "label": "Layout",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Elegant"
          }
        },
        "enable_card_background": {
          "label": "Enable card background"
        },
        "caption": {
          "label": "Subheading"
        },
        "title": {
          "label": "Heading"
        },
        "image_width": {
          "label": "Image width",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "options__4": {
            "label": "Full"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "options__4": {
            "label": "Circle"
          },
          "options__5": {
            "label": "Asymmetrical"
          }
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "column_alignment": {
          "label": "Column alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "button_label": {
          "label": "Button label"
        },
        "button_link": {
          "label": "Button link"
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      },
      "blocks": {
        "column": {
          "name": "Column",
          "settings": {
            "image": {
              "label": "Image"
            },
            "hide_image": {
              "label": "Hide image"
            },
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Description"
            },
            "link_label": {
              "label": "Link Label"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Multicolumn"
      }
    },
    "events-calendar": {
      "name": "Events calendar",
      "settings": {
        "enable_card_background": {
          "label": "Enable card background"
        },
        "caption": {
          "label": "Subheading"
        },
        "title": {
          "label": "Heading"
        },
        "image_width": {
          "label": "Image width",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "options__4": {
            "label": "Full"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "options__4": {
            "label": "Circle"
          },
          "options__5": {
            "label": "Asymmetrical"
          }
        },
        "event_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Grid"
          },
          "options__2": {
            "label": "List"
          }
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "column_alignment": {
          "label": "Column alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "button_label": {
          "label": "Button label"
        },
        "button_link": {
          "label": "Button link"
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      },
      "blocks": {
        "event": {
          "name": "Event",
          "settings": {
            "image": {
              "label": "Image"
            },
            "hide_image": {
              "label": "Hide image"
            },
            "event_date": {
              "label": "Date"
            },
            "event_month": {
              "label": "Month",
              "options__1": {
                "label": "Jan"
              },
              "options__2": {
                "label": "Feb"
              },
              "options__3": {
                "label": "Mar"
              },
              "options__4": {
                "label": "Apr"
              },
              "options__5": {
                "label": "May"
              },
              "options__6": {
                "label": "Jun"
              },
              "options__7": {
                "label": "Jul"
              },
              "options__8": {
                "label": "Aug"
              },
              "options__9": {
                "label": "Sep"
              },
              "options__10": {
                "label": "Oct"
              },
              "options__11": {
                "label": "Nov"
              },
              "options__12": {
                "label": "Dec"
              }
            },
            "hide_date": {
              "label": "Hide date"
            },
            "event_time": {
              "label": "Day and time"
            },
            "event_price": {
              "label": "Price"
            },
            "event_heading": {
              "label": "Heading"
            },
            "event_description": {
              "label": "Text"
            },
            "event_location": {
              "label": "Location"
            },
            "link_label": {
              "label": "Link Label"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Events calendar"
      }
    },
    "multicolumn-cover": {
      "name": "Multicolumn cover",
      "settings": {
        "caption": {
          "label": "Subheading"
        },
        "title": {
          "label": "Heading"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "min_overlay_height": {
          "label": "Adjust overlay height"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "column_alignment": {
          "label": "Column alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "background_style": {
          "label": "Secondary background",
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "Show as column background"
          }
        },
        "button_label": {
          "label": "Button label"
        },
        "button_link": {
          "label": "Button link"
        },
        "show_text_box": {
          "label": "Show text box"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "full_width": {
          "label": "Make section full width"
        },
        "image_overlay_opacity": {
          "label": "Opacity",
          "info": "You can change opacity color globaly from the [theme settings](\/editor?context=theme&category=colors)."
        }
      },
      "blocks": {
        "column": {
          "name": "Column",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Description"
            },
            "link_label": {
              "label": "Link label"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Multicolumn cover"
      }
    },
    "icons-with-text": {
      "name": "Icons with text",
      "settings": {
        "image_width": {
          "label": "Image width",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "options__4": {
            "label": "Full"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "options__4": {
            "label": "Circle"
          },
          "options__5": {
            "label": "Asymmetrical"
          }
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "column_alignment": {
          "label": "Column alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "enable_desktop_slider": {
          "label": "Enable carousel on desktop"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        }
      },
      "blocks": {
        "column": {
          "name": "Column",
          "settings": {
            "image": {
              "label": "Image"
            },
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Description"
            },
            "link_label": {
              "label": "Link Label"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Icons with text"
      }
    },
    "infocards": {
      "name": "Info cards",
      "settings": {
        "cards_style": {
          "label": "Layout",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          },
          "options__3": {
            "label": "3 Columns"
          }
        }
      },
      "blocks": {
        "infocard": {
          "name": "Card",
          "settings": {
            "image": {
              "label": "Image"
            },
            "hide_image": {
              "label": "Hide image"
            },
            "title": {
              "label": "Heading"
            },
            "caption": {
              "label": "Caption"
            },
            "text": {
              "label": "Description"
            },
            "link_label": {
              "label": "Link label"
            },
            "link": {
              "label": "Link"
            },
            "infocards_background_color": {
              "label": "Card background color"
            },
            "infocards_color": {
              "label": "Card text color"
            }
          }
        }
      },
      "presets": {
        "name": "Info cards"
      }
    },
    "testimonials": {
      "name": "Testimonials",
      "settings": {
        "title": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "desktop_title_caption_position": {
          "label": "Desktop title and caption position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Right"
          },
          "options__3": {
            "label": "Top"
          }
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "button_label": {
          "label": "Button label"
        },
        "button_link": {
          "label": "Button link"
        },
        "desktop_enable_gradient": {
          "label": "Enable decoration background"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "testimonials_style": {
          "label": "Testimonials style",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Modern"
          }
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        },
        "mobile_enable_gradient": {
          "label": "Enable decoration background"
        }
      },
      "blocks": {
        "column": {
          "name": "Column",
          "settings": {
            "image": {
              "label": "Image"
            },
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Description"
            },
            "rating": {
              "label": "Show stars"
            },
            "rating_stars": {
              "label": "Rating stars",
              "options__1": {
                "label": "5 Stars"
              },
              "options__2": {
                "label": "4 Stars"
              },
              "options__3": {
                "label": "3 Stars"
              },
              "options__4": {
                "label": "2 Stars"
              },
              "options__5": {
                "label": "1 Star"
              }
            },
            "product": {
              "label": "Product"
            }
          }
        }
      },
      "presets": {
        "name": "Testimonials"
      }
    },
    "promo-popup": {
      "name": "Promo Popup",
      "settings": {
        "enable_popup": {
          "label": "Enable popup"
        },
        "popup_test": {
          "label": "Show test popup"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Grid"
          },
          "options__3": {
            "label": "Grid-Bottom-Right"
          }
        },
        "popup_title": {
          "label": "Title"
        },
        "header_font_size": {
          "label": "Title font size"
        },
        "popup_message": {
          "label": "Message"
        },
        "message_font_size": {
          "label": "Message font size"
        },
        "submit_button_text": {
          "label": "Submit button text"
        },
        "button_color": {
          "label": "Button color"
        },
        "button_text_color": {
          "label": "Button text color"
        },
        "button_hover_color": {
          "label": "Button hover color"
        },
        "success_message": {
          "label": "Success message"
        },
        "fadein": {
          "label": "Time before popup to appear"
        },
        "popup_count": {
          "label": "Popup Count"
        },
        "popup_image": {
          "label": "Select an image for the popup"
        },
        "image_width": {
          "label": "Image width"
        },
        "image_alt": {
          "label": "Image alt text"
        }
      },
      "presets": {
        "name": "Promo Popup"
      }
    },
    "promotion-cards": {
      "name": "Promotion cards",
      "settings": {
        "title": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        },
        "promotion_cards_style": {
          "label": "Promotion card style",
          "options__1": {
            "label": "Modern"
          },
          "options__2": {
            "label": "Elegant"
          },
          "options__3": {
            "label": "Cover"
          }
        },
        "swipe_on_mobile": {
          "label": "Enable swipe on mobile"
        }
      },
      "blocks": {
        "column": {
          "name": "Column",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "title": {
              "label": "Heading"
            },
            "link_label": {
              "label": "Link label"
            },
            "link": {
              "label": "Link"
            },
            "product": {
              "label": "Product"
            }
          }
        }
      },
      "presets": {
        "name": "Promotion cards"
      }
    },
    "newsletter": {
      "name": "Email signup",
      "settings": {
        "full_width": {
          "label": "Make section background full width",
          "info": "Visible only with color background."
        },
        "paragraph": {
          "content": "Each email subscription creates a customer account. [Learn more](https:\/\/help.shopify.com\/manual\/customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "paragraph": {
          "name": "Subheading",
          "settings": {
            "paragraph": {
              "label": "Description"
            }
          }
        },
        "email_form": {
          "name": "Email form"
        }
      },
      "presets": {
        "name": "Email signup"
      }
    },
    "newsletter-banner": {
      "name": "Email signup banner",
      "settings": {
        "paragraph": {
          "content": "Each email subscription creates a customer account. [Learn more](https:\/\/help.shopify.com\/manual\/customers)"
        },
        "image": {
          "label": "Image"
        },
        "image_2": {
          "label": "Image",
          "info": "This image will be displayed when you select 'Style two' for your newsletter style."
        },
        "newsletter_style": {
          "label": "Newsletter style",
          "options__1": {
            "label": "One Image"
          },
          "options__2": {
            "label": "Two images"
          },
          "options__3": {
            "label": "No image"
          }
        },
        "layout": {
          "label": "Desktop layout",
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Text first"
          }
        },
        "full_width": {
          "label": "Make section full width"
        }
      },
      "blocks": {
        "caption": {
          "name": "Subheading",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Default"
              },
              "options__2": {
                "label": "Uppercase"
              }
            },
            "caption_size": {
              "label": "Text size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              }
            }
          }
        },
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "paragraph": {
          "name": "Paragraph",
          "settings": {
            "paragraph": {
              "label": "Description"
            },
            "text_style": {
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "label": "Text style"
            }
          }
        },
        "email_form": {
          "name": "Email form"
        }
      },
      "presets": {
        "name": "Email signup banner"
      }
    },
    "recently-viewed-products": {
      "name": "Recently viewed products",
      "settings": {
        "heading": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        }
      },
      "presets": {
        "name": "Recently viewed products"
      }
    },
    "page": {
      "name": "Page",
      "settings": {
        "page": {
          "label": "Page"
        }
      },
      "presets": {
        "name": "Page"
      }
    },
    "related-products": {
      "name": "Related products",
      "settings": {
        "heading": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "text_style": {
          "label": "Subheading text style",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Uppercase"
          }
        },
        "text_size": {
          "label": "Subheading text size",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "products_to_show": {
          "label": "Maximum products to show"
        },
        "columns_desktop": {
          "label": "Number of columns on desktop"
        },
        "paragraph__1": {
          "content": "Dynamic recommendations use order and product information to change and improve over time. [Learn more](https:\/\/help.shopify.com\/themes\/development\/recommended-products)"
        },
        "header__2": {
          "content": "Product card"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Show vendor"
        },
        "show_rating": {
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/theme-structure\/page-types#product-recommendations-section-settings)"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Number of columns on mobile",
          "options__1": {
            "label": "1 Column"
          },
          "options__2": {
            "label": "2 Columns"
          }
        }
      }
    },
    "rich-text": {
      "name": "Rich text",
      "settings": {
        "desktop_content_position": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Desktop content position",
          "info": "Position is automatically optimized for mobile."
        },
        "content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Content alignment"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile content alignment"
        },
        "full_width": {
          "label": "Make section background full width",
          "info": "Visible only with color background."
        }
      },
      "blocks": {
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Image",
              "info": "For the optimal appearance, please ensure the image dimensions are 120x120 pixels."
            }
          }
        },
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading"
            }
          }
        },
        "caption": {
          "name": "Subheading",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Default"
              },
              "options__2": {
                "label": "Uppercase"
              }
            },
            "caption_size": {
              "label": "Text size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              }
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Description"
            }
          }
        },
        "buttons": {
          "name": "Buttons",
          "settings": {
            "button_label_1": {
              "label": "First button label",
              "info": "Leave the label blank to hide the button."
            },
            "button_link_1": {
              "label": "First button link"
            },
            "button_style_secondary_1": {
              "label": "Use outline button style"
            },
            "button_label_2": {
              "label": "Second button label",
              "info": "Leave the label blank to hide the button."
            },
            "button_link_2": {
              "label": "Second button link"
            },
            "button_style_secondary_2": {
              "label": "Use outline button style"
            }
          }
        }
      },
      "presets": {
        "name": "Rich text"
      }
    },
    "scrolling-text": {
      "name": "Scrolling text\/image",
      "settings": {
        "scroll_direction": {
          "options__1": {
            "label": "Right"
          },
          "options__2": {
            "label": "Left"
          },
          "label": "Scroll direction"
        },
        "enable_scroll_decoration": {
          "label": "Enable decoration"
        },
        "scroll_decoration": {
          "options__1": {
            "label": "Circle"
          },
          "options__2": {
            "label": "Diamond"
          },
          "options__3": {
            "label": "Hexagon"
          },
          "options__4": {
            "label": "Star"
          },
          "label": "Scroll decoration"
        },
        "enable_stencil_text": {
          "label": "Stencil text style"
        },
        "scroll_speed": {
          "label": "Speed"
        },
        "scroll_height": {
          "label": "Scroll height"
        },
        "scroll_text_size": {
          "label": "Scroll text size"
        },
        "hover_stop": {
          "label": "Stop on hover"
        },
        "keep_small_mobile": {
          "label": "Keep small mobile"
        },
        "border": {
          "label": "Add border"
        },
        "enable_announcement_bar_desktop_sticky": {
          "label": "Enable sticky layout on desktop"
        },
        "enable_announcement_bar_mobile_sticky": {
          "label": "Enable sticky layout on mobile"
        }
      },
      "blocks": {
        "text": {
          "name": "Text\/Image",
          "settings": {
            "text": {
              "label": "Description"
            },
            "image": {
              "label": "Image",
              "info": "If you add an image the text will be hidden!"
            },
            "image_link": {
              "label": "Image link"
            }
          }
        }
      },
      "presets": {
        "name": "Scrolling text\/image"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "caption": {
          "label": "Subheading"
        },
        "heading": {
          "label": "Heading"
        },
        "text": {
          "label": "Description"
        },
        "button_label_1": {
          "label": "Button label"
        },
        "button_link_1": {
          "label": "Button link"
        },
        "video_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Layout one"
          },
          "options__2": {
            "label": "Layout two"
          }
        },
        "cover_image": {
          "label": "Cover image"
        },
        "video_url": {
          "label": "URL",
          "placeholder": "Use a YouTube or Vimeo URL",
          "info": "Accepts YouTube or Vimeo URL"
        },
        "description": {
          "label": "Video alt text",
          "info": "Describe the video for customers using screen readers. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/theme-structure\/theme-features#video)"
        },
        "image_padding": {
          "label": "Add image padding",
          "info": "Select image padding if you don't want your cover image to be cropped."
        },
        "full_width": {
          "label": "Make section full width"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "video-background": {
      "name": "Video background",
      "settings": {
        "video_url": {
          "label": "Select video",
          "info": "Upload your video to Content - Files, then copy the link and paste it here"
        },
        "poster": {
          "label": "Add fallback background image in case video doesn't load"
        },
        "caption": {
          "label": "Subheading"
        },
        "heading": {
          "label": "Heading"
        },
        "text": {
          "label": "Description"
        },
        "button_label": {
          "label": "Button label",
          "info": "Leave the label blank to hide the button."
        },
        "link": {
          "label": "Button link"
        },
        "secondary_style": {
          "label": "Use outline button style"
        },
        "background_height": {
          "label": "Desktop background height",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "background_height_mobile": {
          "label": "Mobile background height",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "text_align": {
          "label": "Text alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "full_width_background": {
          "label": "Make layout full width"
        },
        "box_align": {
          "label": "Content position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "ignore_box": {
          "label": "Enable solid box"
        },
        "blur": {
          "label": "For low quality video add blur"
        },
        "opacity": {
          "label": "Change video opacity"
        },
        "header_video": {
          "content": "Video"
        },
        "video_style": {
          "label": "Video style",
          "info": "Native displays the video in its original size without any content overlay. To include content, select the background option.",
          "options__1": {
            "label": "Native"
          },
          "options__2": {
            "label": "Background"
          }
        }
      },
      "presets": {
        "name": "Video background"
      }
    },
    "slideshow": {
      "name": "Slideshow",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Full width"
          },
          "options__2": {
            "label": "Boxed"
          }
        },
        "slide_height": {
          "label": "Slide height",
          "options__1": {
            "label": "Adapt to first image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "slider_visual": {
          "label": "Pagination style",
          "options__1": {
            "label": "Arrows"
          },
          "options__2": {
            "label": "Lines"
          }
        },
        "auto_rotate": {
          "label": "Auto-Rotate slides"
        },
        "change_slides_speed": {
          "label": "Change slides every"
        },
        "slideshow_controls_background": {
          "label": "Add slider controls background"
        },
        "slider_animations": {
          "label": "Slider animations",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Vertical Zoom"
          },
          "options__3": {
            "label": "Horizontal Zoom"
          },
          "options__4": {
            "label": "Vertical Reveal"
          },
          "options__5": {
            "label": "Horizontal Reveal"
          }
        },
        "mobile": {
          "content": "Mobile layout"
        },
        "show_text_below": {
          "label": "Show content below images on mobile"
        },
        "accessibility": {
          "content": "Accessibility",
          "label": "Slideshow description",
          "info": "Describe the slideshow for customers using screen readers."
        }
      },
      "blocks": {
        "slide": {
          "name": "Slide",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "heading": {
              "label": "Heading"
            },
            "subheading": {
              "label": "Text"
            },
            "button_label": {
              "label": "Button label",
              "info": "Leave the label blank to hide the button."
            },
            "link": {
              "label": "Button link"
            },
            "secondary_style": {
              "label": "Use outline button style"
            },
            "box_align": {
              "label": "Desktop content position",
              "info": "Position is automatically optimized for mobile.",
              "options__1": {
                "label": "Top left"
              },
              "options__2": {
                "label": "Top center"
              },
              "options__3": {
                "label": "Top right"
              },
              "options__4": {
                "label": "Middle left"
              },
              "options__5": {
                "label": "Middle center"
              },
              "options__6": {
                "label": "Middle right"
              },
              "options__7": {
                "label": "Bottom left"
              },
              "options__8": {
                "label": "Bottom center"
              },
              "options__9": {
                "label": "Bottom right"
              }
            },
            "show_text_box": {
              "label": "Show text box on desktop"
            },
            "text_alignment": {
              "label": "Desktop content alignment",
              "option_1": {
                "label": "Left"
              },
              "option_2": {
                "label": "Center"
              },
              "option_3": {
                "label": "Right"
              }
            },
            "image_overlay_opacity": {
              "label": "Image overlay opacity"
            },
            "color_scheme": {
              "info": "Visible when container displayed."
            },
            "text_alignment_mobile": {
              "label": "Mobile content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              },
              "options__3": {
                "label": "Right"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Slideshow"
      }
    },
    "slideshow-two-columns": {
      "name": "Slideshow modern",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Full width"
          },
          "options__2": {
            "label": "Boxed"
          }
        },
        "slide_height": {
          "label": "Slide height",
          "options__1": {
            "label": "Adapt to first image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "slider_visual": {
          "label": "Pagination style",
          "options__1": {
            "label": "Arrows"
          },
          "options__2": {
            "label": "Lines"
          }
        },
        "auto_rotate": {
          "label": "Auto-Rotate slides"
        },
        "change_slides_speed": {
          "label": "Change slides every"
        },
        "slideshow_controls_background": {
          "label": "Add slider controls background"
        },
        "slider_animations": {
          "label": "Slider animations",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Vertical Zoom"
          },
          "options__3": {
            "label": "Horizontal Zoom"
          },
          "options__4": {
            "label": "Vertical Reveal"
          },
          "options__5": {
            "label": "Horizontal Reveal"
          }
        },
        "mobile": {
          "content": "Mobile layout"
        },
        "hide_slider_controls": {
          "label": "Hide slider controls"
        },
        "show_text_below": {
          "label": "Show content below images on mobile"
        },
        "accessibility": {
          "content": "Accessibility",
          "label": "Slideshow description",
          "info": "Describe the slideshow for customers using screen readers."
        }
      },
      "blocks": {
        "slide": {
          "name": "Slide",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Subheading"
            },
            "heading": {
              "label": "Heading"
            },
            "subheading": {
              "label": "Text"
            },
            "button_label": {
              "label": "Button label",
              "info": "Leave the label blank to hide the button."
            },
            "link": {
              "label": "Button link"
            },
            "secondary_style": {
              "label": "Use outline button style"
            },
            "text_alignment": {
              "label": "Desktop content alignment",
              "option_1": {
                "label": "Left"
              },
              "option_2": {
                "label": "Center"
              },
              "option_3": {
                "label": "Right"
              }
            },
            "color_scheme": {
              "info": "Visible when container displayed."
            },
            "text_alignment_mobile": {
              "label": "Mobile content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              },
              "options__3": {
                "label": "Right"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Slideshow modern"
      }
    },
    "advanced-slideshow": {
      "name": "Parallax slider",
      "settings": {
        "auto_rotate": {
          "label": "Auto-Rotate slides"
        },
        "slider_direction": {
          "label": "Slider direction",
          "options__1": {
            "label": "Horizontal"
          },
          "options__2": {
            "label": "Vetical"
          }
        },
        "slider_loop": {
          "label": "Infinite loop"
        },
        "slider_interval": {
          "label": "Slider interval"
        },
        "slider_height": {
          "label": "Slider height"
        },
        "full_width": {
          "label": "Make slider full width"
        },
        "box": {
          "content": "Content box settings"
        },
        "content_height": {
          "label": "Content vertical position"
        },
        "content_position": {
          "label": "Content horizontal position"
        },
        "content_size": {
          "label": "Content box size"
        },
        "content_align": {
          "label": "Content alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "content_box": {
          "label": "Enable content box background"
        },
        "content_opacity": {
          "label": "Content box opacity"
        },
        "text": {
          "content": "Text settings"
        },
        "heading_size": {
          "label": "Heading size"
        },
        "heading_style": {
          "label": "Heading text style",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Uppercase"
          },
          "options__3": {
            "label": "Capitalize"
          }
        },
        "caption_size": {
          "label": "Caption size"
        },
        "link_size": {
          "label": "Link size"
        },
        "caption_style": {
          "label": "Caption text style",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Uppercase"
          },
          "options__3": {
            "label": "Capitalize"
          }
        },
        "mobile": {
          "content": "Mobile settings"
        },
        "mobile_heading_size": {
          "label": "Heading size"
        },
        "mobile_caption_size": {
          "label": "Caption size"
        },
        "mobile_height": {
          "label": "Slider height"
        },
        "mobile_content_height": {
          "label": "Content vertical position"
        },
        "mobile_content_size": {
          "label": "Content box size"
        },
        "other": {
          "content": "Other settings"
        }
      },
      "blocks": {
        "slide": {
          "name": "Slide",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Caption"
            },
            "heading": {
              "label": "Heading"
            },
            "button_label": {
              "label": "Button label",
              "info": "Leave the label blank to hide the button."
            },
            "link": {
              "label": "Button link"
            },
            "show_link_button": {
              "label": "Enable button"
            },
            "secondary_style": {
              "label": "Use outline button style"
            }
          }
        }
      },
      "presets": {
        "name": "Parallax slider"
      }
    },
    "slick-slider": {
      "name": "Slick slider",
      "settings": {
        "header": {
          "content": "Slider Settings"
        },
        "image_opacity": {
          "label": "Image opacity"
        },
        "opacity_mobile": {
          "label": "Opacity only on mobile"
        }
      },
      "presets": {
        "name": "Slick slider"
      }
    },
    "comparison-slider": {
      "name": "Comparison slider",
      "settings": {
        "title": {
          "label": "Heading"
        },
        "caption": {
          "label": "Subheading"
        },
        "image": {
          "label": "Image one"
        },
        "image_2": {
          "label": "Image two"
        },
        "comparison_slider_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Vertical"
          },
          "options__2": {
            "label": "Right Column"
          },
          "options__3": {
            "label": "Left Column"
          }
        },
        "height": {
          "label": "Desktop height"
        },
        "mouse_percent": {
          "label": "Slider default position"
        },
        "disable_before_after": {
          "label": "Disable before\/after"
        },
        "before_text": {
          "label": "Before text"
        },
        "after_text": {
          "label": "After text"
        },
        "header_mobile": {
          "content": "Mobile"
        },
        "mobile_height": {
          "label": "Mobile height"
        },
        "full_width": {
          "label": "Make layout full width"
        },
        "accessibility": {
          "content": "Accessibility",
          "label": "Slideshow description",
          "info": "Describe the slideshow for customers using screen readers."
        }
      },
      "presets": {
        "name": "Comparison slider"
      }
    },
    "anchor_link": {
      "name": "Anchor link",
      "settings": {
        "anchor": {
          "label": "HTML anchor",
          "info": "Add anchor text to link to"
        }
      },
      "presets": {
        "name": "Anchor link"
      }
    },
    "separator": {
      "name": "Separator",
      "settings": {
        "separator_style": {
          "label": "Style",
          "options__1": {
            "label": "Line"
          },
          "options__2": {
            "label": "Wave 1"
          },
          "options__3": {
            "label": "Wave 2"
          },
          "options__4": {
            "label": "Wave 3"
          },
          "options__5": {
            "label": "Wave 4"
          },
          "options__6": {
            "label": "Cloud 1"
          },
          "options__7": {
            "label": "Cloud 2"
          },
          "options__8": {
            "label": "Hills 1"
          },
          "options__9": {
            "label": "Hills 2"
          },
          "options__10": {
            "label": "Scalloped edge 1"
          },
          "options__11": {
            "label": "Scalloped edge 2"
          }
        },
        "spacer_color": {
          "label": "Spacer color"
        },
        "margin_top_desktop": {
          "label": "Margin top desktop"
        },
        "margin_bottom_desktop": {
          "label": "Margin bottom desktop"
        }
      },
      "presets": {
        "name": "Separator"
      }
    },
    "collapsible_content": {
      "name": "Collapsible content",
      "settings": {
        "caption": {
          "label": "Subheading"
        },
        "heading": {
          "label": "Heading"
        },
        "heading_alignment": {
          "label": "Heading alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "content_style": {
          "label": "Layout",
          "options__1": {
            "label": "Box"
          },
          "options__2": {
            "label": "No box"
          }
        },
        "container_color_scheme": {
          "label": "Container color scheme",
          "info": "Visible when layout is set to row or section container."
        },
        "open_first_collapsible_row": {
          "label": "Open first collapsible row"
        },
        "open_all_collapsible_row": {
          "label": "Open all"
        },
        "header_mobile": {
          "content": "Mobile layout"
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Collapsible row",
          "settings": {
            "heading": {
              "info": "Include a heading that explains the content.",
              "label": "Heading"
            },
            "row_content": {
              "label": "Row content"
            },
            "page": {
              "label": "Row content from page"
            },
            "icon": {
              "label": "Icon",
              "options__1": {
                "label": "None"
              },
              "options__2": {
                "label": "Basket"
              },
              "options__3": {
                "label": "Bag Heart"
              },
              "options__4": {
                "label": "Box"
              },
              "options__5": {
                "label": "Box Heart"
              },
              "options__6": {
                "label": "Brush"
              },
              "options__7": {
                "label": "Calendar"
              },
              "options__8": {
                "label": "Chat Bubble"
              },
              "options__9": {
                "label": "Check Mark"
              },
              "options__10": {
                "label": "Clipboard"
              },
              "options__11": {
                "label": "Droplet"
              },
              "options__12": {
                "label": "Droplet Half"
              },
              "options__13": {
                "label": "Envelope"
              },
              "options__14": {
                "label": "Eye"
              },
              "options__15": {
                "label": "Eye Dropper"
              },
              "options__16": {
                "label": "Exclamation"
              },
              "options__17": {
                "label": "Gift"
              },
              "options__18": {
                "label": "Gem"
              },
              "options__19": {
                "label": "Globe"
              },
              "options__20": {
                "label": "Heart"
              },
              "options__21": {
                "label": "Headset"
              },
              "options__22": {
                "label": "List"
              },
              "options__23": {
                "label": "Smoothie"
              },
              "options__24": {
                "label": "Lock"
              },
              "options__25": {
                "label": "Magic"
              },
              "options__26": {
                "label": "Map Pin"
              },
              "options__27": {
                "label": "Cup"
              },
              "options__28": {
                "label": "Paw Print"
              },
              "options__29": {
                "label": "Shop"
              },
              "options__30": {
                "label": "Person"
              },
              "options__31": {
                "label": "Plane"
              },
              "options__32": {
                "label": "Plant"
              },
              "options__33": {
                "label": "Price Tag"
              },
              "options__34": {
                "label": "Fire"
              },
              "options__35": {
                "label": "Recycle"
              },
              "options__36": {
                "label": "Return"
              },
              "options__37": {
                "label": "Snowflake"
              },
              "options__38": {
                "label": "Star"
              },
              "options__39": {
                "label": "Stopwatch"
              },
              "options__40": {
                "label": "Tag"
              },
              "options__41": {
                "label": "Tree"
              },
              "options__42": {
                "label": "Thumb Up"
              },
              "options__43": {
                "label": "Truck"
              },
              "options__44": {
                "label": "Question Mark"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Collapsible content"
      }
    },
    "tabs": {
      "name": "Tabs",
      "settings": {
        "tabs_style": {
          "label": "Style",
          "options__1": {
            "label": "Default"
          },
          "options__2": {
            "label": "Simple"
          },
          "options__3": {
            "label": "Button"
          }
        },
        "full_width": {
          "label": "Make tabs full width"
        }
      },
      "blocks": {
        "tab": {
          "name": "Tab",
          "settings": {
            "image": {
              "label": "Image"
            },
            "hide_image": {
              "label": "Hide image"
            },
            "tab_image_width": {
              "label": "Image size"
            },
            "heading": {
              "info": "Include a heading that explains the content.",
              "label": "Heading"
            },
            "row_content": {
              "label": "Tab content"
            },
            "page": {
              "label": "Tab content from page"
            },
            "button_label": {
              "label": "First button label",
              "info": "Leave the label blank to hide the button."
            },
            "button_link": {
              "label": "First button link"
            },
            "button_style_secondary": {
              "label": "Use outline button style"
            }
          }
        }
      },
      "presets": {
        "name": "Tabs"
      }
    },
    "about": {
      "name": "About",
      "settings": {
        "caption": {
          "label": "Subheading"
        },
        "title": {
          "label": "Heading"
        },
        "text": {
          "label": "Text"
        },
        "image": {
          "label": "Image"
        },
        "open_first_collapsible_row": {
          "label": "Open first collapsible Row"
        },
        "header_mobile": {
          "content": "Mobile layout"
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Collapsible row",
          "settings": {
            "heading": {
              "info": "Include a heading that explains the content.",
              "label": "Heading"
            },
            "row_content": {
              "label": "Row content"
            },
            "page": {
              "label": "Row content from page"
            },
            "icon": {
              "label": "Icon",
              "options__1": {
                "label": "None"
              },
              "options__2": {
                "label": "Basket"
              },
              "options__3": {
                "label": "Bag Heart"
              },
              "options__4": {
                "label": "Box"
              },
              "options__5": {
                "label": "Box Heart"
              },
              "options__6": {
                "label": "Brush"
              },
              "options__7": {
                "label": "Calendar"
              },
              "options__8": {
                "label": "Chat Bubble"
              },
              "options__9": {
                "label": "Check Mark"
              },
              "options__10": {
                "label": "Clipboard"
              },
              "options__11": {
                "label": "Droplet"
              },
              "options__12": {
                "label": "Droplet Half"
              },
              "options__13": {
                "label": "Envelope"
              },
              "options__14": {
                "label": "Eye"
              },
              "options__15": {
                "label": "Eye Dropper"
              },
              "options__16": {
                "label": "Exclamation"
              },
              "options__17": {
                "label": "Gift"
              },
              "options__18": {
                "label": "Gem"
              },
              "options__19": {
                "label": "Globe"
              },
              "options__20": {
                "label": "Heart"
              },
              "options__21": {
                "label": "Headset"
              },
              "options__22": {
                "label": "List"
              },
              "options__23": {
                "label": "Smoothie"
              },
              "options__24": {
                "label": "Lock"
              },
              "options__25": {
                "label": "Magic"
              },
              "options__26": {
                "label": "Map Pin"
              },
              "options__27": {
                "label": "Cup"
              },
              "options__28": {
                "label": "Paw Print"
              },
              "options__29": {
                "label": "Shop"
              },
              "options__30": {
                "label": "Person"
              },
              "options__31": {
                "label": "Plane"
              },
              "options__32": {
                "label": "Plant"
              },
              "options__33": {
                "label": "Price Tag"
              },
              "options__34": {
                "label": "Fire"
              },
              "options__35": {
                "label": "Recycle"
              },
              "options__36": {
                "label": "Return"
              },
              "options__37": {
                "label": "Snowflake"
              },
              "options__38": {
                "label": "Star"
              },
              "options__39": {
                "label": "Stopwatch"
              },
              "options__40": {
                "label": "Tag"
              },
              "options__41": {
                "label": "Tree"
              },
              "options__42": {
                "label": "Thumb Up"
              },
              "options__43": {
                "label": "Truck"
              },
              "options__44": {
                "label": "Question Mark"
              }
            }
          }
        }
      },
      "presets": {
        "name": "About"
      }
    }
  }
}

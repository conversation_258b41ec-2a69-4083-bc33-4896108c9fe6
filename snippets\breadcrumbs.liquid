{{ 'component-breadcrumbs.css' | asset_url | stylesheet_tag }}
{%- unless template == 'index' or template == 'cart' or template == '404' -%}
  {% assign t = template | split: '.' | first %}
  <div class="page-width {{ settings.breadcrumbs_style }}">
    <nav class="breadcrumbs" role="navigation" aria-label="breadcrumbs">
      <ol>
        <li>
          <a class="link" href="{{ shop.url }}" title="Home">{{ 'general.breadcrumbs.shop_url' | t }}</a>
        </li>
        {% case t %}
          {% when 'page' %}
            <li>
              <a class="link" href="{{ page.url }}" aria-current="page">{{ page.title }}</a>
            </li>
          {% when 'product' %}
            {% if product.collections.size > 0 %}
              {% assign first_collection = product.collections.first %}
              <li>
                {{ first_collection.title | link_to: first_collection.url }}
              </li>
            {% endif %}
            <li>
              <a class="link" href="{{ product.url }}" aria-current="page">{{ product.title }}</a>
            </li>
          {% when 'collection' and collection.handle %}
            {% if current_tags %}
              <li>{{ collection.title | link_to: collection.url }}</li>
              <li>
                {% capture tag_url %}{{ collection.url }}/{{ current_tags | join: " + " }}{% endcapture %}
                <a class="link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: ' + ' }}</a>
              </li>
            {% else %}
              <li>
                <a class="link" href="{{ collection.url }}" aria-current="page">{{ collection.title }}</a>
              </li>
            {% endif %}
          {% when 'blog' %}
            {% if current_tags %}
              <li>{{ blog.title | link_to: blog.url }}</li>
              <li>
                {% capture tag_url %}{{blog.url}}/tagged/{{ current_tags | join: "+" }}{% endcapture %}
                <a class="link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: ' + ' }}</a>
              </li>
            {% else %}
              <li>
                <a class="link" href="{{ blog.url }}" aria-current="page">{{ blog.title }}</a>
              </li>
            {% endif %}
          {% when 'article' %}
            <li>{{ blog.title | link_to: blog.url }}</li>
            <li>
              <a class="link" href="{{ article.url }}" aria-current="page">{{ article.title }}</a>
            </li>
          {% else %}
            <li aria-current="page">
              <a class="link" href="{{ request.path }}" aria-current="page">{{ page_title }}</a>
            </li>
        {% endcase %}
      </ol>
    </nav>
  </div>
{% endunless %}
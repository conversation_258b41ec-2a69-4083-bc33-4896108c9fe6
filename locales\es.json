{"general": {"password_page": {"login_form_heading": "Ingresar a la tienda usando contraseña:", "login_password_button": "Ingresar usando contraseña", "login_form_password_label": "Contraseña", "login_form_password_placeholder": "Tu contraseña", "login_form_error": "¡Contraseña incorrecta!", "login_form_submit": "Ingresar", "admin_link_html": "¿Eres el propietario de la tienda? <a href=\"/admin\" class=\"link underlined-link\">Inicia sesión aquí</a>", "powered_by_shopify_html": "Esta tienda será impulsada por {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Compartir en Facebook", "share_on_twitter": "Tuitear en Twitter", "share_on_pinterest": "Anclar en Pinterest"}, "links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Continuar comprando", "pagination": {"label": "Paginación", "page": "<PERSON><PERSON><PERSON><PERSON> {{ number }}", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "previous": "Página anterior"}, "search": {"search": "Busca tus artículos...", "reset": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>"}, "cart": {"view": "Ver mi carrito ({{ count }})", "view_empty_cart": "Ver mi carrito", "item_added": "Elemento añadido a tu carrito"}, "breadcrumbs": {"shop_url": "<PERSON><PERSON>o"}, "share": {"close": "<PERSON><PERSON><PERSON> compartir", "copy_to_clipboard": "<PERSON><PERSON><PERSON> enlace", "share_url": "Enlace", "success_message": "Enlace copiado al portapapeles"}, "slider": {"of": "de", "next_slide": "Deslizar a la derecha", "previous_slide": "Deslizar a la izquierda", "name": "<PERSON><PERSON><PERSON>"}}, "newsletter": {"label": "Correo electrónico", "success": "<PERSON><PERSON><PERSON> por suscribirte", "button_label": "Suscribirse"}, "accessibility": {"skip_to_text": "<PERSON>r al contenido", "skip_to_product_info": "Ir a la información del producto", "close": "<PERSON><PERSON><PERSON>", "unit_price_separator": "por", "vendor": "Proveedor:", "error": "Error", "refresh_page": "Elegir una selección resulta en una actualización completa de la página.", "link_messages": {"new_window": "Se abre en una ventana nueva.", "external": "Abre sitio web externo."}, "loading": "Cargando...", "total_reviews": "reseñas en total", "star_reviews_info": "{{ rating_value }} de {{ rating_max }} estrellas", "collapsible_content_title": "Contenido plegable", "complementary_products": "Productos complementarios", "layout_switcher": "Selector de dise<PERSON>"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "<PERSON><PERSON> más: {{ title }}", "comments": {"one": "{{ count }} comentario", "other": "{{ count }} comentarios"}, "moderated": "Por favor, ten en cuenta que los comentarios deben ser aprobados antes de ser publicados.", "comment_form_title": "Dejar un comentario", "name": "Nombre", "email": "Correo electrónico", "message": "Comentario", "post": "Publicar comentario", "back_to_blog": "Volver al blog", "share": "Compartir este artículo", "success": "¡Tu comentario se publicó con éxito! ¡Gracias!", "success_moderated": "Tu comentario se publicó con éxito. Lo publicaremos en un momento, ya que nuestro blog está moderado.", "previous_post": "Entrada anterior", "next_post": "Entrada siguiente"}}, "onboarding": {"product_title": "Título de ejemplo del producto", "collection_title": "Nombre de tu colección"}, "products": {"product": {"add_to_cart": "<PERSON><PERSON><PERSON> al <PERSON>", "choose_options": "Vista rápida", "select_variant": "Seleccionar opciones", "choose_product_options": "Elige opciones para {{ product_name }}", "description": "Descripción", "inventory_in_stock": "En stock", "inventory_in_stock_show_count": "{{ quantity }} en stock", "inventory_low_stock": "Stock bajo", "inventory_low_stock_show_count": "Stock bajo: quedan {{ quantity }}", "inventory_out_of_stock": "Agotado", "inventory_out_of_stock_continue_selling": "En stock", "sku": "SKU", "on_sale": "<PERSON><PERSON><PERSON>", "product_variants": "Variantes del producto", "media": {"gallery_viewer": "<PERSON>isor de <PERSON>", "load_image": "<PERSON>gar imagen {{ index }} en vista de galería", "load_model": "Cargar modelo 3D {{ index }} en vista de galería", "load_video": "Reproducir video {{ index }} en vista de galería", "image_available": "La imagen {{ index }} ya está disponible en la vista de galería", "open_media": "Abrir contenido multimedia {{ index }} en modal", "play_model": "Reproducir Visor 3D", "play_video": "Reproducir video"}, "quantity": {"label": "Cantidad", "input_label": "Cantidad para {{ product }}", "increase": "Aumentar cantidad para {{ product }}", "decrease": "<PERSON><PERSON><PERSON><PERSON><PERSON> canti<PERSON> para {{ product }}", "minimum_of": "<PERSON><PERSON><PERSON> {{ quantity }}", "maximum_of": "<PERSON><PERSON><PERSON><PERSON> de {{ quantity }}", "multiples_of": "Incrementos de {{ quantity }}", "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> en el carrito", "note": "Ver reglas de cantidad"}, "pickup_availability": {"view_store_info": "Ver información de la tienda", "check_other_stores": "Ver disponibilidad en otras tiendas", "pick_up_available": "Recogida disponible", "pick_up_available_at_html": "Recogida disponible en <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Recogida no disponible en este momento en <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "No se pudo cargar la disponibilidad de la recogida", "refresh": "Actualizar"}, "price": {"from_price_html": "Desde {{ price }}", "regular_price": "Precio regular", "sale_price": "Precio en oferta", "unit_price": "Precio unitario"}, "volume_pricing": {"title": "<PERSON><PERSON>s por <PERSON>n", "note": "Precios por volumen disponibles", "minimum": "{{ quantity }}+", "price_at_each": "a {{ price }}/ud", "price_range": "{{ minimum }} - {{ maximum }}"}, "share": "Compartir este producto", "sold_out": "Agotado", "unavailable": "No disponible", "vendor": "<PERSON><PERSON><PERSON><PERSON>", "value_unavailable": "{{ option_value }} - No disponible", "variant_sold_out_or_unavailable": "Variante agotada o no disponible", "video_exit_message": "{{ title }} abre el video en pantalla completa en la misma ventana.", "view_full_details": "Ver detalles completos", "xr_button": "Ver en tu espacio", "xr_button_label": "Ver en tu espacio, carga el artículo en una ventana de realidad aumentada", "include_taxes": "Impuestos incluidos.", "shipping_policy_html": "<a href=\"{{ link }}\">Envío</a> calculado al finalizar la compra."}, "modal": {"label": "Galería multimedia"}, "facets": {"apply": "Aplicar", "clear": "Limpiar", "clear_all": "Eliminar todos", "from": "<PERSON><PERSON>", "filter_and_sort": "Filtrar y ordenar", "filter_by_label": "Filtrar por:", "filter_button": "Filtrar", "filters_selected": {"one": "{{ count }} se<PERSON><PERSON><PERSON>do", "other": "{{ count }} se<PERSON><PERSON><PERSON><PERSON>"}, "filter_selected_accessibility": "{{ type }} ({{ count }} filtros seleccionados)", "show_more": "<PERSON>er más", "show_less": "<PERSON>er menos", "max_price": "El precio más alto es {{ price }}", "product_count": {"one": "{{ product_count }} de {{ count }} producto", "other": "{{ product_count }} de {{ count }} productos"}, "product_count_simple": {"one": "{{ count }} producto", "other": "{{ count }} productos"}, "reset": "Restablecer", "sort_button": "Ordenar", "sort_by_label": "Ordenar por:", "to": "<PERSON><PERSON>", "clear_filter": "Eliminar filtro"}}, "templates": {"search": {"no_results": "No se encontraron resultados para “{{ terms }}”. Verifica la ortografía o utiliza una palabra o frase diferente.", "page": "<PERSON><PERSON><PERSON><PERSON>", "products": "Productos", "results_pages_with_count": {"one": "{{ count }} p<PERSON><PERSON>a", "other": "{{ count }} p<PERSON><PERSON>as"}, "results_suggestions_with_count": {"one": "{{ count }} sugerencia", "other": "{{ count }} sugerencias"}, "results_products_with_count": {"one": "{{ count }} producto", "other": "{{ count }} productos"}, "results_with_count": {"one": "{{ count }} resultado", "other": "{{ count }} resultados"}, "results_with_count_and_term": {"one": "{{ count }} resultado encontrado para “{{ terms }}”", "other": "{{ count }} resultados encontrados para “{{ terms }}”"}, "title": "Resultados de búsqueda", "search_for": "Buscar “{{ terms }}”", "suggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pages": "<PERSON><PERSON><PERSON><PERSON>"}, "cart": {"cart": "<PERSON><PERSON>"}, "contact": {"form": {"title": "Formulario de contacto", "name": "Nombre", "email": "Correo electrónico", "phone": "Número de teléfono", "comment": "Comentario", "send": "Enviar", "post_success": "<PERSON><PERSON><PERSON> por contactarnos. Nos pondremos en contacto contigo lo antes posible.", "error_heading": "Por favor, ajusta lo siguiente:"}}, "404": {"title": "Página no encontrada", "subtext": "404"}}, "sections": {"header": {"announcement": "<PERSON><PERSON><PERSON>", "menu": "Menú", "cart_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}}, "cart": {"title": "<PERSON><PERSON>", "caption": "Artículos en el carrito", "remove_title": "Eliminar {{ title }}", "subtotal": "Subtotal", "new_subtotal": "Nuevo subtotal", "note": "Nota del carrito", "checkout": "<PERSON><PERSON>", "view_cart": "<PERSON>er carrito", "empty": "Tu carrito está vacío", "cart_error": "Hubo un error al actualizar tu carrito. Por favor, inténtalo de nuevo.", "cart_quantity_error_html": "Solo puedes agregar {{ quantity }} de este artículo a tu carrito.", "taxes_and_shipping_policy_at_checkout_html": "Impuestos y <a href=\"{{ link }}\">envío</a> calculados al pagar", "taxes_included_but_shipping_at_checkout": "Impuestos incluidos y envío calculado al pagar", "taxes_included_and_shipping_policy_html": "Impuestos incluidos. <a href=\"{{ link }}\">Envío</a> calculado al pagar.", "taxes_and_shipping_at_checkout": "Impuestos y envío calculados al pagar", "headings": {"product": "Producto", "price": "Precio", "total": "Total", "quantity": "Cantidad", "image": "Imagen del producto"}, "update": "Actualizar", "login": {"title": "¿Tienes una cuenta?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Inicia sesión</a> para pagar más rápido."}}, "footer": {"payment": "Métodos de pago"}, "featured_blog": {"view_all": "Ver todo", "onboarding_title": "Publicación del blog", "onboarding_content": "Ofrece a tus clientes un resumen de tu publicación del blog"}, "featured_collection": {"view_all": "Ver todo", "view_all_label": "Ver todos los productos en la colección {{ collection_name }}"}, "collection_list": {"view_all": "Ver todo"}, "countdown": {"count_days": "Días", "count_hours": "<PERSON><PERSON>", "count_minutes": "<PERSON><PERSON><PERSON>", "count_seconds": "<PERSON><PERSON><PERSON>"}, "collection_template": {"empty": "No se encontraron productos", "title": "Colección", "use_fewer_filters_html": "Utiliza menos filtros o <a class=\"{{ class }}\" href=\"{{ link }}\">elimina todos</a>"}, "video": {"load_video": "Cargar video: {{ description }}"}, "slideshow": {"load_slide": "<PERSON><PERSON> diapositiva", "previous_slideshow": "Diapositiva anterior", "next_slideshow": "Diapositiva siguiente", "pause_slideshow": "Pausar presentación", "play_slideshow": "Iniciar <PERSON>", "carousel": "<PERSON><PERSON><PERSON>", "slide": "Diapositiva"}, "page": {"title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> página"}, "quick_order_list": {"product_total": "Subtotal del producto", "view_cart": "<PERSON>er carrito", "each": "{{ money }}/ud", "product": "Producto", "variant": "<PERSON><PERSON><PERSON>", "variant_total": "Total de variantes", "items_added": {"one": "{{ quantity }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ quantity }} <PERSON><PERSON><PERSON><PERSON>"}, "items_removed": {"one": "{{ quantity }} art<PERSON><PERSON>lo eliminado", "other": "{{ quantity }} art<PERSON><PERSON><PERSON> eliminados"}, "product_variants": "Variantes del producto", "total_items": "Total de artículos", "remove_all_single_item_confirmation": "¿Eliminar 1 artículo de su carrito?", "remove_all_items_confirmation": "¿Eliminar todos los {{ quantity }} artículos de su carrito?", "remove_all": "Eliminar todo", "cancel": "<PERSON><PERSON><PERSON>"}}, "localization": {"country_label": "País/Región", "language_label": "Idioma", "update_language": "Actualizar idioma", "update_country": "Actualizar país/región"}, "customer": {"account": {"title": "C<PERSON><PERSON>", "details": "Detalles de la cuenta", "view_addresses": "Ver direcciones", "return": "Volver a los detalles de la cuenta"}, "account_fallback": "C<PERSON><PERSON>", "activate_account": {"title": "Activar cuenta", "subtext": "Crea tu contraseña para activar tu cuenta.", "password": "Contraseña", "password_confirm": "Confirmar con<PERSON>", "submit": "Activar cuenta", "cancel": "<PERSON><PERSON>zar <PERSON>"}, "addresses": {"title": "Direcciones", "default": "Predeterminada", "add_new": "Agregar una nueva dirección", "edit_address": "<PERSON><PERSON>", "first_name": "Nombre", "last_name": "Apellido", "company": "Compañía", "address1": "Dirección 1", "address2": "Dirección 2", "city": "Ciudad", "country": "País/Región", "province": "Provincia", "zip": "Código postal", "phone": "Teléfono", "set_default": "Establecer como dirección predeterminada", "add": "Agregar <PERSON>", "update": "Actualizar <PERSON>cci<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "delete_confirm": "¿Estás seguro de que deseas eliminar esta dirección?"}, "log_in": "In<PERSON><PERSON>", "log_out": "<PERSON><PERSON><PERSON>", "login_page": {"cancel": "<PERSON><PERSON><PERSON>", "create_account": "<PERSON><PERSON><PERSON> cuenta", "email": "Correo electrónico", "forgot_password": "¿Olvidaste tu contraseña?", "guest_continue": "<PERSON><PERSON><PERSON><PERSON>", "guest_title": "Continuar como invitado", "password": "Contraseña", "title": "In<PERSON><PERSON>", "sign_in": "Ingresar", "submit": "Enviar"}, "order": {"title": "Pedido {{ name }}", "date_html": "Realizado el {{ date }}", "cancelled_html": "Pedido cancelado el {{ date }}", "cancelled_reason": "Motivo: {{ reason }}", "billing_address": "Dirección de facturación", "payment_status": "Estado de pago", "shipping_address": "Dirección de envío", "fulfillment_status": "Estado de cumplimiento", "discount": "Descuento", "shipping": "Envío", "tax": "Impuesto", "product": "Producto", "sku": "SKU", "price": "Precio", "quantity": "Cantidad", "total": "Total", "total_refunded": "Reembolsado", "fulfilled_at_html": "<PERSON><PERSON><PERSON><PERSON> el {{ date }}", "track_shipment": "<PERSON><PERSON><PERSON> env<PERSON>", "tracking_url": "<PERSON><PERSON> de segu<PERSON>o", "tracking_company": "Transportista", "tracking_number": "Número de seguimiento", "subtotal": "Subtotal", "total_duties": "<PERSON><PERSON><PERSON>"}, "orders": {"title": "Historial de pedidos", "order_number": "Pedido", "order_number_link": "Número de pedido {{ number }}", "date": "<PERSON><PERSON>", "payment_status": "Estado de pago", "fulfillment_status": "Estado de cumplimiento", "total": "Total", "none": "Aún no has realizado ningún pedido."}, "recover_password": {"title": "Restablecer contraseña", "subtext": "Te enviaremos un correo electrónico para restablecer tu contraseña", "success": "Te hemos enviado un correo electrónico con un enlace para actualizar tu contraseña."}, "register": {"title": "<PERSON><PERSON><PERSON> cuenta", "first_name": "Nombre", "last_name": "Apellido", "email": "Correo electrónico", "password": "Contraseña", "submit": "<PERSON><PERSON><PERSON>"}, "reset_password": {"title": "Restablecer contraseña de la cuenta", "subtext": "Ingresa una nueva contraseña", "password": "Contraseña", "password_confirm": "Confirmar con<PERSON>", "submit": "Restablecer contraseña"}}, "gift_cards": {"issued": {"title": "¡Aquí tienes tu tarjeta de regalo de {{ value }} para {{ shop }}!", "subtext": "Tu tarjeta de regalo", "gift_card_code": "Código de la tarjeta de regalo", "shop_link": "Continuar comprando", "remaining_html": "<PERSON><PERSON> restante {{ balance }}", "add_to_droplet_wallet": "Agregar a la billetera Droplet", "qr_image_alt": "Código QR — escanea para canjear la tarjeta de regalo", "copy_code": "<PERSON><PERSON>r c<PERSON>", "expired": "V<PERSON>cid<PERSON>", "copy_code_success": "<PERSON>ódigo copiado exitosamente", "print_gift_card": "Imprimir"}}, "recipient": {"form": {"checkbox": "Quiero enviar esto como un regalo", "expanded": "Formulario del destinatario de la tarjeta de regalo expandido", "collapsed": "Formulario del destinatario de la tarjeta de regalo colapsado", "email_label": "Correo electrónico del destinatario", "email_label_optional_for_no_js_behavior": "Correo electrónico del destinatario (opcional)", "email": "Correo electrónico", "name_label": "Nombre del destinatario (opcional)", "name": "Nombre", "message_label": "Mensaje (opcional)", "message": "Men<PERSON><PERSON>", "max_characters": "{{ max_chars }} caracteres máx.", "send_on": "AAAA-MM-DD", "send_on_label": "Enviar el (opcional)"}}}
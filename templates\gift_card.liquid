{% layout none %}

<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    <script src="{{ 'vendor/qrcode.js' | shopify_asset_url }}" defer></script>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="{{ settings.color_background }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    {%- assign formatted_initial_value = gift_card.initial_value | money_without_trailing_zeros | strip_html -%}

    <title>{{ 'gift_cards.issued.title' | t: value: formatted_initial_value, shop: shop.name }}</title>

    <meta name="description" content="{{ 'gift_cards.issued.subtext' | t }}">

    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
    %}

    {% style %}
      {{ settings.type_body_font | font_face: font_display: 'swap' }}
      {{ body_font_bold | font_face: font_display: 'swap' }}
      {{ body_font_italic | font_face: font_display: 'swap' }}
      {{ body_font_bold_italic | font_face: font_display: 'swap' }}
      {{ settings.type_header_font | font_face: font_display: 'swap' }}

      {% for scheme in settings.color_schemes -%}
        {% assign scheme_classes = scheme_classes | append: ', .color-' | append: scheme.id %}
        {% if forloop.index == 1 -%}
          :root,
        {%- endif %}
        .color-{{ scheme.id }} {
          --color-background: {{ scheme.settings.background_color.red }},{{ scheme.settings.background_color.green }},{{ scheme.settings.background_color.blue }};
        {% if scheme.settings.background_gradient != empty %}
          --gradient-background: {{ scheme.settings.background_gradient }};
        {% else %}
          --gradient-background: {{ scheme.settings.background_color }};
        {% endif %}

        {% liquid
          assign background_color = scheme.settings.background_color
          assign background_color_brightness = background_color | color_brightness
          if background_color_brightness <= 26
            assign background_color_contrast = background_color | color_lighten: 50
          elsif background_color_brightness <= 65
            assign background_color_contrast = background_color | color_lighten: 5
          else
            assign background_color_contrast = background_color | color_darken: 25
          endif
        %}

          --color-foreground: {{ scheme.settings.text_color.red }}, {{ scheme.settings.text_color.green }}, {{ scheme.settings.text_color.blue }};
          --color-base-solid-button-labels: {{ scheme.settings.button_primary_text_color.red }}, {{ scheme.settings.button_primary_text_color.green }}, {{ scheme.settings.button_primary_text_color.blue }};
          --color-base-solid-button: {{ scheme.settings.button_primary_background_color.red }}, {{ scheme.settings.button_primary_background_color.green }}, {{ scheme.settings.button_primary_background_color.blue }};
          --color-base-outline-button-labels: {{ scheme.settings.button_secondary_color.red }}, {{ scheme.settings.button_secondary_color.green }}, {{ scheme.settings.button_secondary_color.blue }};
          --color-background-contrast: {{ background_color_contrast.red }},{{ background_color_contrast.green }},{{ background_color_contrast.blue }};
          --color-shadow: {{ scheme.settings.text_color.red }}, {{ scheme.settings.text_color.green }}, {{ scheme.settings.text_color.blue }};
          --color-link: {{ scheme.settings.color_link.red }}, {{ scheme.settings.color_link.green }}, {{ scheme.settings.color_link.blue }};
          }
      {% endfor %}

      {{ scheme_classes | prepend: 'body' }} {
        color: rgba(var(--color-foreground), 0.75);
        background-color: rgb(var(--color-background));
      }
  
      :root {

        --color-base-background-accent-1: {{ settings.accent_background_color_1.red }}, {{ settings.accent_background_color_1.green }}, {{ settings.accent_background_color_1.blue }};
        --color-base-accent-1: {{ settings.accent_color_1.red }}, {{ settings.accent_color_1.green }}, {{ settings.accent_color_1.blue }};
        --color-base-accent-2: {{ settings.accent_color_2.red }}, {{ settings.accent_color_2.green }}, {{ settings.accent_color_2.blue }};
        --color-base-text-accent-2: {{ settings.accent_text_color_2.red }}, {{ settings.accent_text_color_2.green }}, {{ settings.accent_text_color_2.blue }};
        --color-base-border-1: {{ settings.border_color_1.red }}, {{ settings.border_color_1.green }}, {{ settings.border_color_1.blue }};
        --color-base-opacity: {{ settings.opacity_color.red }}, {{ settings.opacity_color.green }}, {{ settings.opacity_color.blue }};
        --color-base-quick-add-button-labels: {{ settings.button_quick_add_text_color.red }}, {{ settings.button_quick_add_text_color.green }}, {{ settings.button_quick_add_text_color.blue }};
        --color-base-quick-add-button: {{ settings.button_quick_add_background_color.red }}, {{ settings.button_quick_add_background_color.green }}, {{ settings.button_quick_add_background_color.blue }};
        --payment-terms-background-color: {{ settings.background_color_1 }};

          --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
          --font-body-style: {{ settings.type_body_font.style }};
          --font-body-weight: {{ settings.type_body_font.weight }};
          --font-body-weight-bold: {{ settings.type_body_font.weight | plus: 300 | at_most: 1000 }};
  
          --font-heading-family: {{ settings.type_header_font.family }}, {{ settings.type_header_font.fallback_families }};
          --font-heading-style: {{ settings.type_header_font.style }};
          --font-heading-weight: {{ settings.type_header_font.weight }};
          --font-heading-weight-bold: {{ settings.type_header_weight }};
  
          --font-body-scale: {{ settings.body_scale | divided_by: 100.0 }};
          --font-heading-scale: {{ settings.heading_scale | times: 1.0 | divided_by: settings.body_scale }};
          --font-navigation-scale: {{ settings.navigation_scale | times: 1.0 | divided_by: settings.body_scale }};
          --font-subnavigation-scale: {{ settings.subnavigation_scale | times: 1.0 | divided_by: settings.body_scale }};
          --text-transform: {{ settings.text_style }};
        
        --page-width: {{ settings.page_width }}px;
        --page-width-margin: {% if settings.page_width == '1620' %}2{% else %}0{% endif %}rem;

        --buttons-radius: {{ settings.buttons_radius }}px;
        --buttons-radius-outset: {% if settings.buttons_radius > 0 %}{{ settings.buttons_radius | plus: settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-width: {{ settings.buttons_border_thickness }}px;
        --buttons-border-opacity: {{ settings.buttons_border_opacity | divided_by: 100.0 }};
        --buttons-shadow-opacity: {{ settings.buttons_shadow_opacity | divided_by: 100.0 }};
        --buttons-shadow-visible: {% if settings.buttons_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --buttons-shadow-horizontal-offset: {{ settings.buttons_shadow_horizontal_offset }}px;
        --buttons-shadow-vertical-offset: {{ settings.buttons_shadow_vertical_offset }}px;
        --buttons-shadow-blur-radius: {{ settings.buttons_shadow_blur }}px;
        --buttons-border-offset: {% if settings.buttons_radius > 0 %}0.3{% else %}0{% endif %}px;


        {% if settings.exclude_inputs == false %}
        --inputs-radius: {{ settings.global_border_radius }}px;
        --inputs-border-width: {{ settings.global_border_thickness }}px;
        --inputs-border-opacity: {{ settings.global_border_opacity | divided_by: 100.0 }};
        --inputs-shadow-opacity: {{ settings.global_shadow_opacity | divided_by: 100.0 }};
        --inputs-shadow-horizontal-offset: {{ settings.global_shadow_horizontal_offset }}px;
        --inputs-shadow-vertical-offset: {{ settings.global_shadow_vertical_offset }}px;
        --inputs-shadow-blur-radius: {{ settings.global_shadow_blur }}px;
        --inputs-radius-outset: {% if settings.global_border_radius > 0 %}{{ settings.global_border_radius | plus: settings.global_border_thickness }}{% else %}0{% endif %}px;
        {% else %}
        --inputs-radius: 0px;
        --inputs-border-width: 0px;
        --inputs-border-opacity: 0;
        --inputs-shadow-opacity: 0;
        --inputs-shadow-horizontal-offset: 0px;
        --inputs-shadow-vertical-offset: 0px;
        --inputs-shadow-blur-radius: 0px;
        --inputs-radius-outset: 0px;
        {% endif %}
      }
    {% endstyle %}

    {%- unless settings.type_body_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}

    {{ 'template-giftcard.css' | asset_url | stylesheet_tag }}
  </head>

  <body>
    <header class="gift-card__title">
      <span class="h2">{{ shop.name }}</span>
      <h1 class="gift-card-heading">{{ 'gift_cards.issued.subtext' | t }}</h1>
    </header>

    <main class="gift-card">
      <div class="gift-card__image-wrapper">
        <img
          src="{{ 'gift-card/card.svg' | shopify_asset_url }}"
          alt=""
          class="gift-card__image"
          height="{{ 570 | divided_by: 1.5 }}"
          width="570"
          loading="lazy"
        >
      </div>
      <div class="gift-card__information-wrapper">
        <div class="gift-card__price">
          <p>
            {% if settings.currency_code_enabled %}
              {{ gift_card.initial_value | money_with_currency }}
            {% else %}
              {{ gift_card.initial_value | money }}
            {% endif %}
          </p>
          {%- if gift_card.enabled == false or gift_card.expired -%}
            <p class="gift-card__label badge badge--{{ settings.sold_out_badge_color_scheme }}">
              {{ 'gift_cards.issued.expired' | t }}
            </p>
          {%- endif -%}
        </div>

        {% if settings.currency_code_enabled %}
          {%- assign gift_card_balance = gift_card.balance | money_with_currency -%}
        {% else %}
          {%- assign gift_card_balance = gift_card.balance | money -%}
        {% endif %}
        {%- if gift_card.balance != gift_card.initial_value -%}
          <p class="gift-card__label caption-large">
            {{ 'gift_cards.issued.remaining_html' | t: balance: gift_card_balance }}
          </p>
        {%- endif -%}
        <div class="gift-card__qr-code" data-identifier="{{ gift_card.qr_identifier }}"></div>
        <div class="gift-card-information">
          <input
            type="text"
            class="gift-card__number"
            value="{{ gift_card.code | format_code }}"
            aria-label="{{ 'gift_cards.issued.gift_card_code' | t }}"
            readonly
          >
          <div class="gift-card__copy-code">
            <button class="link gift-card__copy-link">{{ 'gift_cards.issued.copy_code' | t }}</button>
            <span class="gift-card__copy-success form-message" role="status"></span>
            <template>
              {%- render 'icon-success' -%}
              {{ 'gift_cards.issued.copy_code_success' | t }}
            </template>
          </div>
          {%- if gift_card.pass_url -%}
            <a href="{{ gift_card.pass_url }}" class="gift_card__droplet-wallet">
              <img
                src="{{ 'gift-card/add-to-droplet-wallet.svg' | shopify_asset_url }}"
                width="120"
                height="40"
                alt="{{ 'gift_cards.issued.add_to_droplet_wallet' | t }}"
                loading="lazy"
              >
            </a>
          {%- endif -%}
          <div class="gift-card__buttons no-print">
            <a
              href="{{ shop.url }}"
              class="button"
              target="_blank"
              rel="noopener"
              aria-describedby="a11y-new-window-message"
            >
              {{ 'gift_cards.issued.shop_link' | t }}
            </a>
            <button
              class="button button--secondary"
              onclick="window.print();"
            >
              {{ 'gift_cards.issued.print_gift_card' | t }}
            </button>
          </div>
        </div>
      </div>
    </main>

    <div hidden>
      <span id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</span>
    </div>
  </body>
</html>

<script>
  var string = { qrImageAlt: {{ 'gift_cards.issued.qr_image_alt' | t | json }} };
  document.addEventListener('DOMContentLoaded', function() {
   new QRCode( document.querySelector('.gift-card__qr-code'), {
    text: document.querySelector('.gift-card__qr-code').dataset.identifier,
    width: 120,
    height: 120,
    imageAltText: string.qrImageAlt
    });
  });

  var template = document.getElementsByTagName("template")[0];
  var clonedTemplate = template.content.cloneNode(true);

  var isMessageDisplayed = false
  document
  .querySelector('.gift-card__copy-link')
  .addEventListener('click', () => {
    navigator.clipboard.writeText(document.querySelector('.gift-card__number').value).then(function () {
      if (!isMessageDisplayed) {
        document.querySelector('.gift-card__copy-success').appendChild(clonedTemplate);
        isMessageDisplayed = true
      }
    });
  });
</script>

<quantity-input class="quantity cart-quantity">
    <button class="quantity-button no-js-hidden" name="minus" type="button">
      <span class="visually-hidden">
        {{- 'products.product.quantity.decrease' | t: product: variant.title | escape -}}
      </span>
      {% render 'icon-minus' %}
    </button>
    <input
      class="quantity-input"
      data-quantity-variant-id="{{ variant.id }}"
      type="number"
      name="updates[{{ variant_id }}]"
      {% # theme-check-disable %}
      value="{{ cart | item_count_for_variant: variant.id }}"
      data-cart-quantity="{{ cart | item_count_for_variant: variant.id }}"
      min="{{ variant.quantity_rule.min }}"
      {% if variant.quantity_rule.max != null %}
        max="{{ variant.quantity_rule.max }}"
      {% endif %}
      step="{{ variant.quantity_rule.increment }}"
      {% # theme-check-enable %}
      aria-label="{{ 'products.product.quantity.input_label' | t: product: variant.title | escape }}"
      id="Quantity-{{ variant.id }}"
      data-index="{{ variant.id }}"
    >
    <button class="quantity-button no-js-hidden" name="plus" type="button">
      <span class="visually-hidden">
        {{- 'products.product.quantity.increase' | t: product: variant.title | escape -}}
      </span>
      {% render 'icon-plus' %}
    </button>
</quantity-input>
  